package com.saida.services.common.mq.message;

import com.saida.services.common.mq.vlinker.BaseMessage;
import lombok.Data;

import java.util.List;

@Data
public class SignalNodeMessage extends BaseMessage {

    /**
     * 节点ID
     */
    private String id;
    /**
     * 节点名称
     */
    private String name;
    /**
     * 信令服务接口地址
     */
    private String dispatchUrl;
    /**
     * SIP IP
     */
    private String sipIp;
    /**
     * SIP端口
     */
    private Integer sipPort;
    /**
     * SIP ID
     */
    private String sipId;
    /**
     * SIP域
     */
    private String sipDomain;
    /**
     * 在线设备数
     */
    private Integer onLineNum;
    /**
     * 承载设备数
     */
    private Integer bearNum;
    /**
     * 在线状态， 1：在线，0：离线
     */
    private Integer status;

    /**
     * 设备与信令交互的秘钥
     */
    private String sk;

    /**
     * 域名
     */
    private String domainName;

    /**
     * 类型  1:国标级联(主) 2:国标节点（从） 3：赛达SDK节点 4：海康SDK 5：英特灵达SDK 6：EhomeSDK
     */
    private Integer type;

    private UniverseDto universe;

    @Data
    public static class UniverseDto {
        private Boolean enabled;
        private String ip;
        private Integer port;
    }

    private List<NetworksDto> networks;

    @Data
    public static class NetworksDto {
        private String name;
        private Boolean enable_https;
        private String ip;
        private String domain;
        private String webrtc_url;
        private String flv_url;
        private String hls_url;
        private String ws_flv_url;
    }
}
