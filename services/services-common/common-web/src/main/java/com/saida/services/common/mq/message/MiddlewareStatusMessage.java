package com.saida.services.common.mq.message;

import com.saida.services.common.mq.vlinker.BaseMessage;
import lombok.Data;

@Data
public class MiddlewareStatusMessage extends BaseMessage {
    //名称
    private String name;
    //ip
    private String ip;
    //端口
    private String port;
    //1 正常 0 异常
    private Integer status;

    private String mac;


    /**
     * {
     * "key":"业务key",
     * "source":"来源",
     * "sendTime":"发送时间",
     *   "name": "主机名称",
     *   "ip": "ip",
     *   "port": "端口",
     *   "status": "1 正常 0 异常"
     * }
     */
}
