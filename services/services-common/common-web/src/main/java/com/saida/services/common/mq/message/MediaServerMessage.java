package com.saida.services.common.mq.message;

import com.saida.services.common.mq.vlinker.BaseMessage;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MediaServerMessage extends BaseMessage {

    /**
     * id
     */
    private String id;
    /**
     * 媒体名称
     */
    private String name;
    /**
     * 归属节点
     */
    private String nodeId;
    /**
     * 内外IP
     */
    private String innerIp;
    /**
     * 公网 IP
     */
    private String outerIp;
    /**
     * 公网域名
     */
    private String domain;
    /**
     * 在线流数
     */
    private Integer onLineNum;
    /**
     * 承载流数
     */
    private Integer bearNum;
    /**
     * 入口流量
     */
    private BigDecimal enterFlow;
    /**
     * 出口流量
     */
    private BigDecimal outFlow;
    /**
     * 状态  1:在线，0:离线
     */
    private Integer status;
}
