package com.saida.services.common.mq.message;

import com.saida.services.common.mq.vlinker.BaseMessage;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName OsdMessage
 * @Desc
 * @Date 2025/2/16 09:33
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OsdMessage extends BaseMessage {

    // 设备编码
    private String sn;

    // 设备名称
    private String name;

    private String model;

    // 0:飞机类 1:负载类 2:遥控器类 3:机场类
    private Integer domain;

    private String lon;

    private String lat;

    // 固件版本
    private String firmwareVersion;

    // 设备主控sn
    private String mainControlSn;

    // 机场状态	"0":"空闲中","1":"现场调试","2":"远程调试","3":"固件升级中","4":"作业中"
    private Integer dockModeCode;

    private Integer droneModeCode;

    // 固件升级状态	"0":"未升级","1":"升级中"
    private Integer firmwareStatus;

    // 机场空调状态 "0":"空闲模式(无制冷、制热、除湿等)","1":"制冷模式","2":"制热模式","3":"除湿模式","4":"制冷退出模式","5":"制热退出模式","6":"除湿退出模式","7":"制冷准备模式","8":"制热准备模式","9":"除湿准备模式"
    private Integer airConditionerState;

    // 机场累计作业次数
    private Integer jobNumber;

    // 工作电流  毫安 / mA
    private Integer workingCurrent;

    // 工作电压 毫伏 / mV
    private Integer workingVoltage;

    // 是否设置备降点	"0":"未设置","1":"已设置"
    private Integer landPointIsConfigured;

    // 备用电池开关	"0":"关闭","1":"开启"
    private Integer backupBatterySwitch;

    // 备用电池电压	备用电池关闭时电压为0","max":"30000","min":"0","step":"1","unit_name":"毫伏 / mV
    private Integer backupBatteryVoltage;

    // 备用电池温度	 摄氏度 / °C
    private BigDecimal backupBatteryTemperature;

    // 仓内湿度 相对湿度 / %RH
    private Integer humidity;

    // 仓内温度
    private BigDecimal temperature;

    // 环境温度
    private BigDecimal environmentTemperature;

    // 飞行器夜航灯状态  "0":"关闭","1":"打开"
    private Integer nightLightsState;

    // 飞行器限高
    private Integer heightLimit;

    // 限远
    private Integer distanceLimit;

    // 飞行器避障状态	"0":"关闭","1":"开启"
    private Integer obstacleAvoidance;

    // 飞行安全数据库版本
    private String flysafeDatabaseVersion;

    // 电池的总剩余电量
    private Integer batteryCapacityPercent;

    // 电压	 毫伏 / mV
    private Integer batteryVoltage;

    // 电池温度
    private BigDecimal batteryTemperature;
}
