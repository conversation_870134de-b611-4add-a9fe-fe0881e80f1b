package com.saida.services.common.mq.kafka;

import com.saida.services.common.mq.vlinker.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "kafka", name = "enable", havingValue = "true")
public class KafkaVLinkerMqTemplate implements VLinkerMqTemplate {

    @Autowired(required = false)
    private KafkaConfig kafkaConfig;
    @Resource
    private KafkaEnhanceTemplate kafka;

    @Override
    public String getNameServer() {
        return kafkaConfig.getBootstrapServers();
    }

    @Override
    public <T extends BaseMessage> SendResultWrapper send(String topic, T message) {
        return send(topic, null, message);
    }

    @Override
    public <T extends BaseMessage> SendResultWrapper send(String topic, String key, T message) {
        RecordMetadata metadata = kafka.send(topic, key, message);
        return SendResultWrapper.builder()
                .topic(topic)
                .key(key != null ? key : message.getKey())
                .provider("kafka")
                .success(true)
                .messageId(metadata.topic() + "-" + metadata.partition() + "-" + metadata.offset())
                .extraInfo("partition=" + metadata.partition() + ", offset=" + metadata.offset())
                .build();
    }

    @Override
    public SendResultWrapper send(String topic, List<RawMessageWrapper> messages) {
        List<ProducerRecord<String, String>> records = messages.stream()
                .map(msg -> new ProducerRecord<>(topic, msg.getKey(), msg.getPayload()))
                .collect(Collectors.toList());

        List<RecordMetadata> result = kafka.send(topic, records);
        return SendResultWrapper.builder()
                .topic(topic)
                .provider("kafka")
                .success(true)
                .extraInfo("count=" + result.size())
                .build();
    }

    @Override
    public <T extends BaseMessage> void asyncSend(String topic, T message, MqSendCallback callback) {
        asyncSend(topic, null, message, callback);
    }

    @Override
    public <T extends BaseMessage> void asyncSend(String topic, String key, T message, MqSendCallback callback) {
        kafka.asyncSend(topic, key, message, (metadata, exception) -> {
            if (exception != null) {
                callback.onFailure(exception);
            } else {
                callback.onSuccess(SendResultWrapper.builder()
                        .topic(topic)
                        .key(key != null ? key : message.getKey())
                        .provider("kafka")
                        .messageId(metadata.topic() + "-" + metadata.partition() + "-" + metadata.offset())
                        .success(true)
                        .extraInfo("partition=" + metadata.partition() + ", offset=" + metadata.offset())
                        .build());
            }
        });
    }
}
