package com.saida.services.common.mq.rocketMq;

import com.alibaba.fastjson.JSONObject;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.mq.vlinker.BaseMessage;
import com.saida.services.exception.BizRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;


@Slf4j
@Component
@ConditionalOnProperty(prefix = "rocketmq", name = "enable", havingValue = "true")
public class RocketMQEnhanceTemplate {

    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Autowired(required = false)
    private RocketMqConfig rocketMqConfig;
    @Resource
    private ApplicationContext applicationContext;

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        threadPoolConfig.taskRunner(() -> {
            try {
                initProducer();
            } catch (Exception e) {
                for (int i = 0; i < 10; i++) {
                    log.error("❌ rocketmq生产者 初始化失败了 程序退出 springboot RocketMQEnhanceTemplate error");
                }
                log.error("❌ rocketmq生产者 初始化失败了 程序退出 springboot RocketMQEnhanceTemplate error", e);
                SpringApplication.exit(applicationContext, () -> 1);
            }
        });
    }

    public static DefaultMQProducer producer = null;

    @PreDestroy
    public void destroy() {
        if (producer != null) {
            producer.shutdown();
        }
    }

    private void initProducer() {
        if (rocketMqConfig == null) {
            log.info("RocketMqConfig is null (未开启mq)");
            return;
        }
        if (producer != null) {
            return;
        }
        // 1. 创建生产者并指定组名
        producer = new DefaultMQProducer(rocketMqConfig.getProducer().getGroup());
        // 2. 设置 NameServer 地址
        producer.setNamesrvAddr(rocketMqConfig.getNameServer());
        // 3. 启动生产者
        try {
            producer.start();
            log.info("DefaultMQProducer start success group:{},nameServer:{}", rocketMqConfig.getProducer().getGroup(), rocketMqConfig.getNameServer());
        } catch (MQClientException e) {
            log.error("DefaultMQProducer start error", e);
        }
    }


    /**
     * 发送同步消息
     */
    public <T extends BaseMessage> SendResult send(String topic, T message) {
        return send(topic, null, message);
    }

    /**
     * 发送同步消息
     */
    public <T extends BaseMessage> SendResult send(String topic, String tag, T message) {
        if (producer == null) {
            throw new RuntimeException("producer is null");
        }
        Message sendMessage = new Message(topic, tag, message.getKey(), JSONObject.toJSONString(message).getBytes(StandardCharsets.UTF_8));
        SendResult send;
        try {
            send = producer.send(sendMessage);
        } catch (Exception e) {
            log.error("同步消息内容：{}, 发送异常：{}", JSONObject.toJSONString(message), e.getMessage());
            throw new RuntimeException(e);
        }
        // 此处为了方便查看给日志转了json，根据选择选择日志记录方式，例如ELK采集
        log.info("[{}]同步消息[{}]发送结果[{}]", topic + ":" + tag, JSONObject.toJSON(message), JSONObject.toJSON(send));
        return send;
    }


    public SendResult send(String topic, List<Message> sendMessage) {
        if (producer == null) {
            throw new BizRuntimeException("producer is null");
        }
        SendResult send;
        try {
            send = producer.send(sendMessage);
        } catch (Exception e) {
            log.error("批量同步消息内容：{}, 发送异常：{}", sendMessage.size(), e.getMessage());
            throw new RuntimeException(e);
        }
        // 此处为了方便查看给日志转了json，根据选择选择日志记录方式，例如ELK采集
        log.info("[{}]批量同步消息[{}]发送结果[{}]", topic + ":", sendMessage.size(), JSONObject.toJSON(send));
        return send;
    }

    public <T extends BaseMessage> void asyncSend(String topic, String tag, T message, SendCallback callback) {
        if (producer == null) {
            throw new RuntimeException("producer is null");
        }
        Message sendMessage = new Message(topic, tag, message.getKey(), JSONObject.toJSONString(message).getBytes(StandardCharsets.UTF_8));
        try {
            producer.send(sendMessage, callback);
            log.info("[{}]异步消息[{}]", topic + ":" + tag, JSONObject.toJSON(message));
        } catch (Exception e) {
            log.error("异步消息内容：{}, 发送异常：{}", JSONObject.toJSONString(message), e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public <T extends BaseMessage> void asyncSend(String topic, T message, SendCallback callback) {
        asyncSend(topic, null, message, callback);
    }

}