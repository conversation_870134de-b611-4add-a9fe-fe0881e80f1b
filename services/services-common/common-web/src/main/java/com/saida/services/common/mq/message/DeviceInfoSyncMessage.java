package com.saida.services.common.mq.message;

import com.saida.services.common.mq.vlinker.BaseMessage;
import lombok.Data;

/**
 * 设备信息同步到信令
 */
@Data
public class DeviceInfoSyncMessage extends BaseMessage {

    /**
     * 节点唯一标识
     */
    private String node_id;
    /**
     * EDIT/DEL
     */
    private String action;
    /**
     * 设备唯一标识
     */
    private String sn;
    /**
     * 设备名称
     */
    private String name;
    /**
     * 协议
     */
    private String protocol;
    /**
     * 账号
     */
    private String username;
    /**
     * 密码
     */
    private String password;

    /**
     * IP
     */
    private String ip;
    /**
     * 端口
     */
    private Integer port;
}