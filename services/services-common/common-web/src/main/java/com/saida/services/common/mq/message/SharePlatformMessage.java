package com.saida.services.common.mq.message;

import com.saida.services.common.mq.vlinker.BaseMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName SharePlatformMessage
 * @Desc
 * @Date 2024/10/25 13:52
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SharePlatformMessage extends BaseMessage {
    private String nodeId;

    private Integer messageType;

    private SharePlatformData data;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SharePlatformData {
        private String sipId;

        private String id;

        // 1共享 0取消共享
        private Integer shareType;

        private Set<String> virtualOrganIds;

        private String virtualOrganId;

        private List<ShareDeviceData> deviceList;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ShareDeviceData {
        private String id;
        private String virtualOrganId;
        private String customName;
        private Boolean videoShare;
        private Boolean ptzShare;
        private Boolean vcrShare;
        private String deviceId;
        private String channelId;
        private String customId;
        private String parentId;
        private String longitude;
        private String latitude;
    }
}
