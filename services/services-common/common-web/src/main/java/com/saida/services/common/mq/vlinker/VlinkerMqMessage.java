package com.saida.services.common.mq.vlinker;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;


/**
 * vlinker mq 消息接受类
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VlinkerMqMessage {
    private String topic;
    private String tag;
    private String key;
    private byte[] data;
    private long timestamp;
    private Map<String, String> headers;
}
