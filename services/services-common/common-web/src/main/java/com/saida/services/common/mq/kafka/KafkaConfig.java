package com.saida.services.common.mq.kafka;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Slf4j
@Data
@Component
@ConfigurationProperties("kafka")
@ConditionalOnProperty(prefix = "kafka", name = "enable", havingValue = "true")
public class KafkaConfig {
    private Boolean enable;
    // 服务地址
    private String bootstrapServers;
    private Short replicationFactor = 1;     // 一般为 broker 数量的副本数，测试集群为1

}
