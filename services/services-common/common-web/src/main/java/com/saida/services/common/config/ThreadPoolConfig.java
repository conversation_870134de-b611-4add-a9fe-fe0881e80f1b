package com.saida.services.common.config;

import brave.Span;
import brave.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Resource;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * description:
 * author zhangjc
 * date 2022/9/9 16:18
 */
@Slf4j
@EnableAsync
@Configuration
public class ThreadPoolConfig {

    public static final String TASK_EXECUTOR_BEAN_NAME = "vTsExecutor";
    public static final String TASK_SCHEDULER_BEAN_NAME = "vTsScheduler";

    @Primary
    @Bean(name = ThreadPoolConfig.TASK_EXECUTOR_BEAN_NAME)
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        int coreSize = Runtime.getRuntime().availableProcessors();
        // 核心线程池大小
        executor.setCorePoolSize(coreSize);
        // 最大线程数
        executor.setMaxPoolSize(coreSize * 2);
        // 队列容量
        executor.setQueueCapacity(500);
        // 活跃时间
        executor.setKeepAliveSeconds(60);
        // 线程名字前缀
        executor.setThreadNamePrefix("vlinkerThread-");
        // executor.setTaskDecorator(new MyTaskDecorator());
        // 设置线程池关闭的时候等待所有任务都完成再继续销毁其他的Bean
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 线程池对拒绝任务的处理策略,当线程池没有处理能力的时候，该策略会直接在 execute 方法的调用线程中运行被拒绝的任务；如果执行程序已关闭，则会丢弃该任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        // System.out.println("初始化线程池");
        executor.setAwaitTerminationSeconds(3600000); // 1000小时
        executor.setKeepAliveSeconds(60 * 60 * 12);
        return executor;
    }

    @Bean(name = ThreadPoolConfig.TASK_SCHEDULER_BEAN_NAME)
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler() {
            private static final long serialVersionUID = -1L;

            @Override
            public void destroy() {
                // 尚未开始的延迟任务将被取消,正在执行的任务会继续完成
                this.getScheduledThreadPoolExecutor().setExecuteExistingDelayedTasksAfterShutdownPolicy(false);
                super.destroy();
            }
        };
        int coreSize = Runtime.getRuntime().availableProcessors();
        scheduler.setPoolSize(coreSize * 2);
        scheduler.setThreadNamePrefix("vlinkerScheduler-");
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
//        scheduler.setAwaitTerminationSeconds(10);
        return scheduler;
    }

    static class MyTaskDecorator implements TaskDecorator {
        @Override
        public Runnable decorate(Runnable runnable) {
            try {
                RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
                return () -> {
                    try {
                        RequestContextHolder.setRequestAttributes(attributes);
                        runnable.run();
                    } finally {
                        RequestContextHolder.resetRequestAttributes();
                    }
                };
            } catch (IllegalStateException e) {
                return runnable;
            }
        }
    }

    // 创建一个不限制大小的线程池
    private static final ExecutorService initTaskExecutor = Executors.newCachedThreadPool(runnable -> {
        Thread thread = new Thread(runnable);
        thread.setName("初始化任务线程");
        thread.setDaemon(true);
        return thread;
    });

    @Resource
    private Tracer tracer;

    /**
     * 通用任务执行方法，带异常捕获
     */
    public void taskRunner(Runnable task) {
        initTaskExecutor.execute(() -> {
            // 如果没有当前的 Span（或者没有 traceId），则创建新的 Span
            Span newSpan = tracer.newTrace().name("VLinkerTraceRunnable").start();
            try (Tracer.SpanInScope scope = tracer.withSpanInScope(newSpan)) {
                // ✅ traceId/span 自动注入 MDC（如果配置了日志桥）
                task.run();  // 执行传入的任务
            } catch (Exception e) {
                log.error("❌❌❌❌ 任务执行失败：{}", e.getMessage());
            } finally {
                newSpan.finish();
            }
        });
    }
}