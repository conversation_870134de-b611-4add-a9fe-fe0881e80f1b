package com.saida.services.common.mq.kafka;

import com.alibaba.fastjson.JSONObject;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.mq.vlinker.BaseMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.*;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "kafka", name = "enable", havingValue = "true")
public class KafkaEnhanceTemplate {

    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Autowired(required = false)
    private KafkaConfig kafkaConfig;
    @Resource
    private ApplicationContext applicationContext;

    private static Producer<String, String> producer = null;

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        threadPoolConfig.taskRunner(()->{
            try {
                initProducer();
            } catch (Exception e) {
                for (int i = 0; i < 10; i++) {
                    log.error("❌ Kafka生产者 初始化失败了 程序退出 springboot KafkaEnhanceTemplate error");
                }
                log.error("❌ Kafka生产者 初始化失败了 程序退出 springboot KafkaEnhanceTemplate error", e);
                SpringApplication.exit(applicationContext, () -> 1);
            }
        });
    }

    @PreDestroy
    public void destroy() {
        if (producer != null) {
            producer.close(Duration.ofSeconds(2));
        }
    }

    private void initProducer() {
        if (kafkaConfig == null) {
            log.info("KafkaConfig is null (未开启kafka)");
            return;
        }
        if (producer != null) {
            return;
        }
        log.info("KafkaConfig 开始创建发布者");
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConfig.getBootstrapServers());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class.getName());
        // 可选项
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        producer = new KafkaProducer<>(props);
        log.info("KafkaProducer init success, servers: {}", kafkaConfig.getBootstrapServers());
    }

    /**
     * 发送同步消息
     */
    public <T extends BaseMessage> RecordMetadata send(String topic, T message) {
        return send(topic, null, message);
    }

    /**
     * 发送同步消息
     */
    public <T extends BaseMessage> RecordMetadata send(String topic, String tag, T message) {
        if (producer == null) {
            throw new RuntimeException("producer is null");
        }

        ProducerRecord<String, String> record = new ProducerRecord<>(
                topic,
                message.getKey(),
                JSONObject.toJSONString(message)
        );

        try {
            RecordMetadata metadata = producer.send(record).get();
            log.info("[{}]同步消息[{}]发送成功: offset={}, partition={}",
                    topic, JSONObject.toJSON(message), metadata.offset(), metadata.partition());
            return metadata;
        } catch (Exception e) {
            log.error("同步消息内容：{}, 发送异常：{}", JSONObject.toJSONString(message), e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 批量发送同步消息
     */
    public List<RecordMetadata> send(String topic, List<ProducerRecord<String, String>> records) {
        if (producer == null) {
            throw new RuntimeException("producer is null");
        }
        List<RecordMetadata> results = new ArrayList<>();
        for (ProducerRecord<String, String> record : records) {
            try {
                RecordMetadata metadata = producer.send(record).get();
                results.add(metadata);
            } catch (Exception e) {
                log.error("批量同步消息发送异常 topic:{}, key:{}, error: {}", record.topic(), record.key(), e.getMessage());
                throw new RuntimeException(e);
            }
        }
        log.info("[{}]批量同步消息[{}]发送成功", topic, records.size());
        return results;
    }

    /**
     * 异步发送
     */
    public <T extends BaseMessage> void asyncSend(String topic, T message, Callback callback) {
        asyncSend(topic, null, message, callback);
    }

    public <T extends BaseMessage> void asyncSend(String topic, String tag, T message, Callback callback) {
        if (producer == null) {
            throw new RuntimeException("producer is null");
        }

        ProducerRecord<String, String> record = new ProducerRecord<>(
                topic,
                message.getKey(),
                JSONObject.toJSONString(message)
        );

        try {
            producer.send(record, callback);
            log.info("[{}]异步消息[{}]已提交", topic, JSONObject.toJSON(message));
        } catch (Exception e) {
            log.error("异步消息内容：{}, 发送异常：{}", JSONObject.toJSONString(message), e.getMessage());
            throw new RuntimeException(e);
        }
    }

}

