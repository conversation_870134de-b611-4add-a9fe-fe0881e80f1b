package com.saida.services.common.mq.kafka;


import brave.Span;
import brave.Tracer;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.mq.vlinker.VLinkerMqMessageListener;
import com.saida.services.common.mq.vlinker.VLinkerTopicConfig;
import com.saida.services.common.mq.vlinker.VlinkerMqMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.CreateTopicsResult;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "kafka", name = "enable", havingValue = "true")
public class InitKafkaConsumer {

    @Autowired(required = false)
    private KafkaConfig kafkaConfig;
    @Autowired(required = false)
    private Map<String, VLinkerMqMessageListener> listenerMap;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Resource
    private Tracer tracer;
    @Resource
    private ApplicationContext applicationContext;


    private static final Map<String, Thread> workerThreads = new ConcurrentHashMap<>();
    private static final AtomicBoolean isRunning = new AtomicBoolean(true);

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        threadPoolConfig.taskRunner(()->{
            try {
                initKafkaConsumers();
            } catch (Exception e) {
                for (int i = 0; i < 10; i++) {
                    log.error("❌ Kafka 初始化失败了 程序退出 springboot InitKafkaConsumer error");
                }
                log.error("❌ Kafka 初始化失败了 程序退出 springboot InitKafkaConsumer error", e);
                SpringApplication.exit(applicationContext, () -> 1);
            }
        });
    }

    public void initKafkaConsumers() {
        if (listenerMap == null || listenerMap.isEmpty()) {
            log.error("listenerMap is empty");
            return;
        }
        log.info("===== 初始化 Kafka 消费者 =====");
        listenerMap.forEach((listenerKey, listenerValue) -> {
            VLinkerTopicConfig vLinkerTopicConfig = listenerValue.vLinkerTopicConfig();
            String topic = vLinkerTopicConfig.getTopic();
            createTopicIfNeeded(vLinkerTopicConfig);
            Properties props = new Properties();
            props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConfig.getBootstrapServers());
            props.put(ConsumerConfig.GROUP_ID_CONFIG, "conv");
            props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
            props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, ByteArrayDeserializer.class.getName());
            props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "false");
            props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
            props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, vLinkerTopicConfig.getWriteQueueNums());
            KafkaConsumer<String, byte[]> consumer = new KafkaConsumer<>(props);
            consumer.subscribe(Collections.singletonList(topic));
            Thread worker = new Thread(() -> {
                while (isRunning.get()) {
                    try {
                        ConsumerRecords<String, byte[]> records = consumer.poll(Duration.ofMillis(500));
                        if (!records.isEmpty()) {
                            onMessage(listenerValue, records);
                            consumer.commitSync();
                        }
                    } catch (Exception e) {
                        log.error("Kafka consumer exception in topic: {}", topic, e);
                    }
                }
                consumer.close();
            });
            worker.setName("KafkaConsumerWorker-" + topic);
            worker.start();
            workerThreads.put(topic, worker);
            log.info("[Topic:{}][Group:conv] Kafka 消费者启动成功", topic);
        });
    }

    public void createTopicIfNeeded(VLinkerTopicConfig topicConfig) {
        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConfig.getBootstrapServers());
        String topicName = topicConfig.getTopic();
        try (AdminClient adminClient = AdminClient.create(props)) {
            Set<String> existingTopics = adminClient.listTopics().names().get();
            if (!existingTopics.contains(topicName)) {
                NewTopic newTopic = new NewTopic(topicName, topicConfig.getReadQueueNums(), kafkaConfig.getReplicationFactor());
                CreateTopicsResult result = adminClient.createTopics(Collections.singleton(newTopic));
                result.all().get();
                log.info("✅ Kafka Topic [{}] 创建成功，分区数：{}，副本因子：{}", topicName, topicConfig.getReadQueueNums(), kafkaConfig.getReplicationFactor());
            } else {
                log.info("ℹ️ Kafka Topic [{}] 已存在，跳过创建", topicName);
            }
        } catch (Exception e) {
            log.error("❌ Kafka 创建 Topic [{}] 失败", topicName, e);
            throw new RuntimeException(e);
        }
    }


    private void onMessage(VLinkerMqMessageListener listenerValue, ConsumerRecords<String, byte[]> records) {
        for (ConsumerRecord<String, byte[]> record : records) {
            Span newSpan = tracer.nextSpan().name("kafka-consumer-span").start();
            try (Tracer.SpanInScope ws = tracer.withSpanInScope(newSpan)) {
                Headers headers = record.headers();
                Map<String, String> headersMap = new HashMap<>();
                headers.forEach(header -> headersMap.put(header.key(), new String(header.value())));
                listenerValue.onMessage(VlinkerMqMessage
                        .builder()
                        .topic(record.topic())
                        .key(record.key())
                        .tag(headersMap.get("tag"))
                        .data(record.value())
                        .headers(headersMap)
                        .timestamp(System.currentTimeMillis())
                        .build());
            } catch (Exception e) {
                newSpan.error(e);
                log.error("[Kafka topic:{}] 消息处理异常", listenerValue.vLinkerTopicConfig().getTopic(), e);
            } finally {
                newSpan.finish();
            }
        }
    }

    @PreDestroy
    public void shutdown() {
        log.info("Spring 容器停止，Kafka 消费者关闭...");
        isRunning.set(false);
        workerThreads.forEach((group, thread) -> {
            try {
                thread.join(1000);
                log.info("Kafka 消费线程关闭完成: {}", group);
            } catch (InterruptedException e) {
                log.warn("Kafka 消费线程中断: {}", group, e);
                Thread.currentThread().interrupt();
            }
        });
    }
}