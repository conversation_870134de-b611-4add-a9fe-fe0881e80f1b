package com.saida.services.common.mq.message;

import com.saida.services.common.mq.vlinker.BaseMessage;
import lombok.Data;

@Data
public class HostResourceUsageMessage extends BaseMessage {
    /*
    {
    "key":"消息id",
    "source":"来源",
    "sendTime":"发送时间 毫秒时间戳",
    "cpuSum":"cpu总量（浮点数字）",
    "memSum":"mem总量（浮点数字）",
    "diskSum":"disk总量（浮点数字）",
    "netSum":"net总量（浮点数字）",
    "cpu":"cpu占用率（浮点数字）",
    "mem":"mem占用率（浮点数字）",
    "disk":"disk占用率（浮点数字）",
    "net":"net占用率（浮点数字）",
    "nodeId":"节点id",
    "name":"主机名称",
    "operatingSystem":"操作系统",
    "core":"核心",
    "memory":"内存",
    "hardDisk":"硬盘",
    "intranetIPAddress":"内网ip",
    "publicIPAddress":"外网ip",
    "bandwidthOnTheLine":"上线带宽",
    "downstreamBandwidth":"下行带宽",
    "mac":"mac地址"
}
     */
    private Double cpuSum;
    private Double memSum;
    private Double diskSum;
    private Double netSum;
    private Double cpu;
    private Double mem;
    private Double disk;
    private Double net;
    //节点id
    private String nodeId;
    //主机名称
    private String name;
    //操作系统
    private String operatingSystem;
    //核心
    private String core;
    //内存
    private String memory;
    //硬盘
    private String hardDisk;
    //内网ip
    private String intranetIPAddress;
    //外网ip
    private String publicIPAddress;
    //上线带宽
    private String bandwidthOnTheLine;
    //下行带宽
    private String downstreamBandwidth;
    //mac地址
    private String mac;

}
