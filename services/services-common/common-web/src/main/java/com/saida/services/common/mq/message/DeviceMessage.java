package com.saida.services.common.mq.message;

import com.saida.services.common.mq.vlinker.BaseMessage;
import lombok.Data;

@Data
public class DeviceMessage extends BaseMessage {

    /**
     * 设备编码
     */
    private String device_id;

    /**
     * 状态，1：在线，0：离线
     */
    private Integer status;
    /**
     * 设备型号
     */
    private String model;
    /**
     * 设备版本
     */
    private String version;

    /**
     * 设备端侧名称
     */
    private String device_name;

    private String manufacturer;

    private Long updated_at;

    private String reason;

    /**
     * 公网ip
     */
    private String eip;
    /**
     * 内网ip
     */
    private String iip;

    /**
     * 目录订阅
     */
    private Integer catalog_subscribe;

    /**
     * 告警订阅
     */
    private Integer alarm_subscribe;

    /**
     * 位置订阅
     */
    private Integer position_subscribe;
}
