package com.saida.services.feign.srv.system.fallback;

import com.saida.services.algorithm.dto.PushAlarmDto;
import com.saida.services.common.base.DtoResult;
import com.saida.services.feign.srv.system.IFeignSrvSystemApiController;
import com.saida.services.open.dto.IotDataPushDto;
import com.saida.services.open.dto.IotOnOffLinePushDto;
import com.saida.services.open.dto.OpenNoticeThirdPartyAppMsgDTO;
import com.saida.services.open.entity.DeviceInfoEntity;
import com.saida.services.srv.dto.SrvAddDeviceByWifiDTO;
import com.saida.services.srv.dto.VlinkConvMsg;
import com.saida.services.srv.vo.BCBindDeviceVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class SrvSystemFallbackFactory implements FallbackFactory<IFeignSrvSystemApiController> {

    @Override
    public IFeignSrvSystemApiController create(Throwable cause) {
        log.error("Feign 调用失败，异常信息：{}", cause.getMessage(),cause);
        return new IFeignSrvSystemApiController() {

            @Override
            public DtoResult<Void> vlinkOpenAlarmReceive(PushAlarmDto dto) {
                return DtoResult.error("系统升级中,请稍后");
            }

            @Override
            public DtoResult<Void> vlinkOpenIotDataReceive(IotDataPushDto dto) {
                return DtoResult.error("系统升级中,请稍后");
            }

            @Override
            public DtoResult<Void> receiveOpenMsg(OpenNoticeThirdPartyAppMsgDTO dto) {
                return DtoResult.error("系统升级中,请稍后");
            }

            @Override
            public DtoResult<Void> syncAllDeviceChannelDataFromOpen() {
                return DtoResult.error("系统升级中,请稍后");
            }

            @Override
            public DtoResult<Void> syncIncreDeviceChannelDataFromOpen(List<DeviceInfoEntity> deviceInfoEntityList) {
                return DtoResult.error("系统升级中,请稍后");
            }

            @Override
            public DtoResult<Void> vlinkOpenIotOnOffLineReceive(IotOnOffLinePushDto dto) {
                return DtoResult.error("系统升级中,请稍后");
            }

            @Override
            public DtoResult<BCBindDeviceVo> addCustomerDevice(SrvAddDeviceByWifiDTO dto) {
                return DtoResult.error("系统升级中,请稍后");
            }

            @Override
            public DtoResult<Void> vlinkConvMsg(VlinkConvMsg vlinkConvMsg) {
                return DtoResult.error("系统升级中,请稍后");
            }
        };
    }
}