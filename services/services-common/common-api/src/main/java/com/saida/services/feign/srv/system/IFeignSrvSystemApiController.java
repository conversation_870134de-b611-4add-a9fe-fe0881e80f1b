package com.saida.services.feign.srv.system;

import com.saida.services.algorithm.dto.PushAlarmDto;
import com.saida.services.common.base.DtoResult;
import com.saida.services.feign.srv.system.fallback.SrvSystemFallbackFactory;
import com.saida.services.open.dto.IotDataPushDto;
import com.saida.services.open.dto.IotOnOffLinePushDto;
import com.saida.services.open.dto.OpenNoticeThirdPartyAppMsgDTO;
import com.saida.services.open.entity.DeviceInfoEntity;
import com.saida.services.srv.dto.SrvAddDeviceByWifiDTO;
import com.saida.services.srv.dto.VlinkConvMsg;
import com.saida.services.srv.vo.BCBindDeviceVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(
        value = "srv-system-server",
        contextId = "feignSrvSystemApiController",
        path = "/srv-system",
        fallbackFactory = SrvSystemFallbackFactory.class)
public interface IFeignSrvSystemApiController {

    @PostMapping("/feign/srv-system/vlinkOpenAlarmReceive")
    DtoResult<Void> vlinkOpenAlarmReceive(@RequestBody PushAlarmDto dto);

    @PostMapping("/feign/srv-system/vlinkConvMsg")
    DtoResult<Void> vlinkConvMsg(@RequestBody VlinkConvMsg vlinkConvMsg);

    @PostMapping("/feign/srv-system/vlinkOpenIotDataReceive")
    DtoResult<Void> vlinkOpenIotDataReceive(@RequestBody IotDataPushDto dto);

    @PostMapping("/feign/srv-system/receiveOpenMsg")
    DtoResult<Void> receiveOpenMsg(@RequestBody OpenNoticeThirdPartyAppMsgDTO dto);

    @PostMapping("/feign/srv-system/syncAllDeviceChannelDataFromOpen")
    DtoResult<Void> syncAllDeviceChannelDataFromOpen();

    @PostMapping("/feign/srv-system/syncIncreDeviceChannelDataFromOpen")
    DtoResult<Void> syncIncreDeviceChannelDataFromOpen(@RequestBody List<DeviceInfoEntity> deviceInfoEntityList);

    @PostMapping("/feign/srv-system/vlinkOpenIotOnOffLineReceive")
    DtoResult<Void> vlinkOpenIotOnOffLineReceive(@RequestBody IotOnOffLinePushDto dto);

    @PostMapping("/feign/srv-system/customer/device/add")
    DtoResult<BCBindDeviceVo> addCustomerDevice(@RequestBody SrvAddDeviceByWifiDTO dto);
}