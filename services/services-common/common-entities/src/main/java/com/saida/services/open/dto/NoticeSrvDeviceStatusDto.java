package com.saida.services.open.dto;

import com.saida.services.open.enums.OpenNoticeThirdPartyAppEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class NoticeSrvDeviceStatusDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private OpenNoticeThirdPartyAppEnum openNoticeThirdPartyAppEnum;

    private String deviceId;

    private Integer status;
}