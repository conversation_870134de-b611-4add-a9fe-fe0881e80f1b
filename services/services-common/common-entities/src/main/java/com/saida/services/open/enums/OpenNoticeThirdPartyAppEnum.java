package com.saida.services.open.enums;

import com.saida.services.open.dto.OpenNoticeMsgDTO;
import com.saida.services.open.dto.OpenNoticeOnLineMsgDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OpenNoticeThirdPartyAppEnum {

    ONE("1", OpenNoticeOnLineMsgDTO.class, "设备上下线通知");

    private final String code;
    private final Class<? extends OpenNoticeMsgDTO> classes;
    private final String des;
}