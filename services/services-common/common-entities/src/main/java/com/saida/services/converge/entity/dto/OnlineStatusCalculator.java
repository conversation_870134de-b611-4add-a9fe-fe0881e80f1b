package com.saida.services.converge.entity.dto;

import lombok.Getter;

import java.util.*;

@Getter
public class OnlineStatusCalculator {

    public static void main(String[] args) {
        /**
         * 行号     id                   parent_id            name                                    id_chain                                                    onlineCount          offlineCount
         * ---------- -------------------- -------------------- --------------------------------------- ----------------------------------------------------------- -------------------- --------------------
         * 1          1934875429622525954  1934875363482546178  四会市视频资源共享应用平台 1934875271191080962,1934875363482546178,1934875429622525954 47                   3
         * 2          1934875363482546178  1934875271191080962  四会市                               1934875271191080962,1934875363482546178                     0                    0
         * 3          1934875271191080962  0                    肇庆市                               1934875271191080962                                         0                    0
         *
         * 已用时间: 3.541(毫秒). 执行号:2244604.
         * SQL>
         */
        List<VirtualOrganTreeDto> list = new ArrayList<>();
        VirtualOrganTreeDto dto1 = new VirtualOrganTreeDto();
        dto1.setId(1934875429622525954L);
        dto1.setName("四会市视频资源共享应用平台");
        dto1.setParentId(1934875363482546178L);
        dto1.setIdChain("1934875271191080962,1934875363482546178,1934875429622525954");
        dto1.setOnlineCount(47);
        dto1.setOfflineCount(3);

        VirtualOrganTreeDto dto2 = new VirtualOrganTreeDto();
        dto2.setId(1934875363482546178L);
        dto2.setName("四会市");
        dto2.setParentId(1934875271191080962L);
        dto2.setIdChain("1934875271191080962,1934875363482546178");

        VirtualOrganTreeDto dto3 = new VirtualOrganTreeDto();
        dto3.setId(1934875271191080962L);
        dto3.setName("肇庆市");
        dto3.setParentId(0L);
        dto3.setIdChain("1934875271191080962");
        list.add(dto1);
        list.add(dto2);
        list.add(dto3);
        OnlineStatusCalculator onlineStatusCalculator = new OnlineStatusCalculator();
        list.forEach(onlineStatusCalculator::addVirtualOrganTreeDto);
        onlineStatusCalculator.buildTreeRelations();
        onlineStatusCalculator.calculateOnlineStatusFromLeaves(1934875271191080962L);

        Map<Long, VirtualOrganTreeDto> virtualOrganTreeDtoMap = onlineStatusCalculator.getVirtualOrganTreeDtoMap();
        virtualOrganTreeDtoMap.forEach((k, v) -> {
            System.out.println(v.getName() + " " + v.getOnlineCount() + " " + v.getOfflineCount());
        });


    }

    private final Map<Long, VirtualOrganTreeDto> VirtualOrganTreeDtoMap = new HashMap<>();


    public void addVirtualOrganTreeDto(VirtualOrganTreeDto dto) {
        VirtualOrganTreeDtoMap.put(dto.getId(), dto);
    }


    public void buildTreeRelations() {
        for (VirtualOrganTreeDto node : VirtualOrganTreeDtoMap.values()) {
            if (node.getParentId() != 0) {
                VirtualOrganTreeDto parent = VirtualOrganTreeDtoMap.get(node.getParentId());
                if (parent != null) {
                    parent.getChildList().add(node);
                }
            }
        }
    }


    // 从叶子节点开始向上累加在线状态
    public void calculateOnlineStatusFromLeaves(Long rootId) {
        for (VirtualOrganTreeDto leaf : getLeaves(VirtualOrganTreeDtoMap)) {
            updateOnlineStatus(leaf, rootId);
        }
    }

    // 更新节点及其父节点的在线状态
    private void updateOnlineStatus(VirtualOrganTreeDto node, Long rootId) {
        Integer onlineCount = node.getOnlineCount();
        Integer offlineCount = node.getOfflineCount();
        while (node != null && node.getParentId() != 0) {
            node = VirtualOrganTreeDtoMap.get(node.getParentId());
            if (node != null) {
                node.setOnlineCount(onlineCount + node.getOnlineCount());
                node.setOfflineCount(offlineCount + node.getOfflineCount());
            }
        }
    }

    // 获取所有叶子节点
    private List<VirtualOrganTreeDto> getLeaves(Map<Long, VirtualOrganTreeDto> map) {
        List<VirtualOrganTreeDto> leaves = new ArrayList<>();
        for (VirtualOrganTreeDto VirtualOrganTreeDto : map.values()) {
            if (VirtualOrganTreeDto.getChildList().isEmpty()) {
                leaves.add(VirtualOrganTreeDto);
            }
        }
        return leaves;
    }

}
