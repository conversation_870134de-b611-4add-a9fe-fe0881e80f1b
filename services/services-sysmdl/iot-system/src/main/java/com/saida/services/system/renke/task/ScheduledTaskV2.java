package com.saida.services.system.renke.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.saida.services.iot.entity.DeviceEntity;
import com.saida.services.iot.entity.ProductEntity;
import com.saida.services.system.device.service.DeviceService;
import com.saida.services.system.event.PushAppEvent;
import com.saida.services.system.event.SaveAndUpdateDataEvent;
import com.saida.services.system.product.service.ProductPropertyService;
import com.saida.services.system.product.service.ProductService;
import com.xxl.job.core.handler.annotation.VlinkerXxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 仁科---定时任务
 */
@Component
@Slf4j
public class ScheduledTaskV2 {

    private static final List<RenkeHttpUtilV1.UserDto> userListv1 = new ArrayList<RenkeHttpUtilV1.UserDto>() {{
        //赛达账号
//        add(RenkeHttpUtilV1.UserDto.builder().loginName("cq230625sdkj").password("cq230625sdkj").build());
        add(RenkeHttpUtilV1.UserDto.builder().loginName("cq230513hjjc").password("cq230513hjjc").build());
    }};

    @Resource
    private RenkeHttpUtilV2 renkeHttpUtilV2;
    @Resource
    private RenkeHttpUtilV1 renkeHttpUtilV1;

    @Resource
    private DeviceService deviceService;

    @Autowired
    private ProductPropertyService productPropertyService;
    @Autowired
    private ProductService productService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Value("${scheduledTaskV2.dataTask:false}")
    private Boolean dataTask;

    /**
     * 5分钟执行一次
     */
    @VlinkerXxlJob(
            value = "dataTask",
            cron = "0 0/5 * * * ?",
            desc = "仁科设备-定时任务获取设备列表"
    )
//    @Scheduled(cron = "0 0/5 * * * ? ")
    public void dataTask() {
        if (!dataTask) {
            return;
        }
        log.info("5分钟执行一次->仁科的定时更新任务");
        userListv1.forEach(userDto -> {
            try {
                doDeviceListv1(userDto);
            } catch (Exception e) {
                log.error("仁科设备-定时任务,获取设备列表异常", e);
            }
        });
    }


    private void doDeviceListv1(RenkeHttpUtilV1.UserDto userDto) {
        Map<Long, ProductEntity> productMap = new HashMap<>();

        JSONArray realTimeDataByDeviceAddr = null;
        try {
            realTimeDataByDeviceAddr = renkeHttpUtilV1.getRealTimeDataByDeviceAddr(userDto);
        } catch (Exception e) {
            log.info("renke-doRealTimeDatav1-获取实时数据异常", e);
            return;
        }
        for (int j = 0; j < realTimeDataByDeviceAddr.size(); j++) {
            JSONObject data = realTimeDataByDeviceAddr.getJSONObject(j);
            //设备编号
            String deviceAddr = data.getString("deviceAddr");
            DeviceEntity device = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getImei, deviceAddr), false);
            //设备已经落库才会 继续获取其他数据
            if (device == null) {
                log.info("doDeviceListv1 仁科设备-定时任务,查无设备 deviceAddr:{}", deviceAddr);
                continue;
            }
            deviceService.update(new LambdaUpdateWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getImei, deviceAddr)
                    .set(DeviceEntity::getStatus, "online".equals(data.getString("status")) ? 1 : 2));

            ProductEntity productEntity = productMap.computeIfAbsent(device.getProductId(), k -> productService.getById(device.getProductId()));

            if (productEntity == null) {
                log.info("doDeviceListv1 仁科设备-定时任务,查无产品 deviceAddr:{}", deviceAddr);
                return;
            }
            JSONObject jsonObject = decodeWeatherData(data.getJSONArray("data"));
            jsonObject.put("imei", device.getImei());
            //类型 1-普通消息 2-告警消息
            jsonObject.put("type", 1);
            jsonObject.put("createTime", System.currentTimeMillis());
            log.info("仁科设备-定时任务-data,{}", jsonObject);
            applicationEventPublisher.publishEvent(new SaveAndUpdateDataEvent(this, device.getImei(), jsonObject, productEntity));
            applicationEventPublisher.publishEvent(new PushAppEvent(this, device, jsonObject));
        }
    }

    private JSONObject decodeWeatherData(JSONArray jsonArray) {
        JSONObject jsonObject = new JSONObject();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject object = jsonArray.getJSONObject(i);
            Integer nodeId = object.getInteger("nodeId");
            String temValueStr = object.getString("temValueStr");
            String humValueStr = object.getString("humValueStr");
            if (nodeId == 1) {
                //风力
                jsonObject.put("windPower", temValueStr);
                //风速
                jsonObject.put("windSpeed", humValueStr);
            }
            if (nodeId == 2) {
                //风向
                jsonObject.put("windDirection", temValueStr);
            }
            if (nodeId == 3) {
                //土壤温度
                jsonObject.put("soilTem", temValueStr);
                //土壤湿度
                jsonObject.put("soilHum", humValueStr);
            }
            if (nodeId == 4) {
                //土壤温度
                jsonObject.put("soilTem2", temValueStr);
                //土壤湿度
                jsonObject.put("soilHum2", humValueStr);
            }
            if (nodeId == 5) {
                //土壤EC
                jsonObject.put("EC", humValueStr);
            }
            if (nodeId == 6) {
                //土壤PH2
                jsonObject.put("soilPH2", temValueStr);
                //土壤PH
                jsonObject.put("soilPH", humValueStr);
            }
            if (nodeId == 7) {
                //土壤温度
                jsonObject.put("soilTem3", temValueStr);
                //辐射
                jsonObject.put("radiation", humValueStr);
            }
            if (nodeId == 8) {
                //空气温度
                jsonObject.put("airTem", temValueStr);
                //空气湿度
                jsonObject.put("airHum", humValueStr);
            }
            if (nodeId == 9) {
                //土壤温度
                jsonObject.put("soilTem4", temValueStr);
                //噪音
                jsonObject.put("noise", humValueStr);
            }
            if (nodeId == 10) {
                //PH10
                jsonObject.put("PH10", temValueStr);
                //PH2.5
                jsonObject.put("PH25", humValueStr);
            }
            if (nodeId == 11) {
                //大气压
                jsonObject.put("barometricPressure", humValueStr);
            }
            if (nodeId == 12) {
                //噪音
//                jsonObject.put("light", temValueStr);
                jsonObject.put("noise", humValueStr);
            }
            if (nodeId == 13) {
                //当前雨量
                jsonObject.put("nowRain", humValueStr);
            }
            if (nodeId == 14) {
                //昨日雨量
                jsonObject.put("yesterdayRain", humValueStr);
            }
            if (nodeId == 15) {
                //瞬时雨量
                jsonObject.put("rain", humValueStr);
            }
            if (nodeId == 16) {
                //累积雨量
                jsonObject.put("totalRain", temValueStr);
            }
            if (nodeId == 17) {
                //管式温度
                jsonObject.put("pipeTem", temValueStr);
                //管式湿度
                jsonObject.put("pipeHum", humValueStr);
            }
            if (nodeId == 18) {
                //管式温度
                jsonObject.put("pipeTem2", temValueStr);
                //管式湿度
                jsonObject.put("pipeHum2", humValueStr);
            }
            if (nodeId == 19) {
                //管式温度
                jsonObject.put("pipeTem3", temValueStr);
                //管式湿度
                jsonObject.put("pipeHum3", humValueStr);
            }
            if (nodeId == 20) {
                //管式温度
                jsonObject.put("pipeTem4", temValueStr);
                //管式湿度
                jsonObject.put("pipeHum4", humValueStr);
            }
            if (nodeId == 21) {
                //磷
                jsonObject.put("P", temValueStr);
                //氮
                jsonObject.put("N", humValueStr);
            }
            if (nodeId == 22) {
                //钾
                jsonObject.put("K", humValueStr);
            }
            if (nodeId == 23) {
                //CO2
                jsonObject.put("CO2", humValueStr);
            }

        }
        return jsonObject;
    }

}
