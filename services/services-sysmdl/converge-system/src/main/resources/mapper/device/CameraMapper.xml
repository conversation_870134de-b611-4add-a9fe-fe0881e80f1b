<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.saida.services.system.device.mapper.CameraMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.saida.services.system.device.entity.CameraEntity" id="cameraMap">
        <result property="id" column="id"/>
        <result property="regionId" column="region_id"/>
        <result property="thirdCode" column="third_code"/>
        <result property="name" column="name"/>
        <result property="manufacturer" column="manufacturer"/>
        <result property="ip" column="ip"/>
        <result property="port" column="port"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="mainStream" column="main_stream"/>
        <result property="subStream" column="sub_stream"/>
        <result property="box" column="box"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="listPage" resultType="com.saida.services.system.device.entity.CameraEntity">
        select t.*, sr.name regionName from camera t
        left join sys_region sr on t.region_id=sr.id
        where  1 = 1 and t.channel_id is not null
            <choose>
                <when test="entity.subLevel">
                    <if test="entity.regionIdChain != null and entity.regionIdChain != ''">
                        AND sr.id_chain like concat(#{entity.regionIdChain},'%')
                    </if>
                </when>
                <otherwise>
                    <if test="entity.regionId != null">
                        AND t.region_id = #{entity.regionId}
                    </if>
                </otherwise>
            </choose>
            <if test="entity.manufacturer != null">
                and t.manufacturer = #{entity.manufacturer}
            </if>
            <if test="entity.thirdCode != null and entity.thirdCode != ''">
                and t.third_code like concat('%', #{entity.thirdCode}, '%')
            </if>
            <if test="entity.name != null and entity.name != ''">
                and t.name like concat('%', #{entity.name}, '%')
            </if>
            <if test="entity.channelId != null and entity.channelId != ''">
                and t.channel_id like concat('%', #{entity.channelId}, '%')
            </if>
            <if test="entity.channelName != null and entity.channelName != ''">
                and t.channel_name like concat('%', #{entity.channelName}, '%')
            </if>
            <if test="entity.boxName != null and entity.boxName != ''">
                and t.box_name like concat('%',#{entity.boxName},'%')
            </if>
        group by t.id
        order by t.id desc
    </select>

    <select id="apiListPage" resultType="com.saida.services.system.device.entity.CameraEntity">
        select t.* from camera t
        left join sys_region sr on t.region_id=sr.id
        <where>
            t.source != 1
            <choose>
                <when test="entity.subLevel">
                    AND sr.id_chain like concat(#{entity.regionIdChain},'%')
                </when>
                <otherwise>
                    AND t.region_id = #{entity.regionId}
                </otherwise>
            </choose>
            <if test="entity.manufacturer != null">
                and t.manufacturer = #{entity.manufacturer}
            </if>
            <if test="entity.name != null and entity.name != ''">
                and t.name like concat('%',#{entity.name},'%')
            </if>
            <if test="entity.thirdCode != null and entity.thirdCode != ''">
                and t.third_code like concat('%',#{entity.thirdCode},'%')
            </if>
            <if test="entity.boxName != null and entity.boxName != ''">
                and t.box_name like concat('%',#{entity.boxName},'%')
            </if>
        </where>
        group by t.id
    </select>

    <select id="getCameraListByCodeAndChannelId" parameterType="com.saida.services.algorithm.dto.DeviceInfoDto" resultType="com.saida.services.system.device.entity.CameraEntity">
        select t.* from camera t
        <where>
            <if test="dtoList != null and dtoList.size() > 0">
                <foreach item="dto" index="index" collection="dtoList" separator="or">
                    (
                    <if test="dto.deviceCode != null and dto.deviceCode != ''">
                        t.third_code = #{dto.deviceCode}
                    </if>
                    <if test="dto.channelId != null and dto.channelId != ''">
                        and t.channel_id = #{dto.channelId}
                    </if>
                    )
                </foreach>
            </if>
        </where>
    </select>

</mapper>