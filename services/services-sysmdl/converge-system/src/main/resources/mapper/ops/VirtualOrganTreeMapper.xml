<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.saida.services.system.ops.mapper.VirtualOrganTreeMapper">

    <select id="getList" resultType="com.saida.services.converge.entity.VirtualOrganTreeEntity">
        select t.* from virtual_organ_tree t
        <where>
            <if test="entity.id != null ">
                and t.id = #{entity.id}
            </if>
            <if test="entity.top != null ">
                and t.parent_id = 0
            </if>
            <if test="entity.idChain != null and entity.idChain != ''">
                and t.id_chain like concat(#{entity.idChain},'%')
            </if>
        </where>
        order by
        t.create_time
    </select>


    <select id="getRootId" resultType="java.lang.Long">
        select t.parent_id from virtual_organ_tree t
        <where>
            <if test="entity.top != null ">
                and t.parent_id = 0
            </if>
            <if test="entity.idChain != null and entity.idChain != ''">
                and t.id_chain = #{entity.idChain}
            </if>
        </where>
        ORDER BY LENGTH(t.id_chain) limit 1
    </select>


    <select id="getInfoByOrganCode" resultType="com.saida.services.converge.entity.VirtualOrganTreeEntity">
        select t.* from virtual_organ_tree t
        where t.organ_code = #{entity.organCode}
        order by t.create_time desc
        limit 1
    </select>

    <update id="updateParentIdBatch">
        UPDATE visual_device_tree t1 SET t1.id_chain = REPLACE (t1.id_chain, #{dto.oldIdChain}, #{dto.newIdChain})
        <where>
            <if test="dto.idSet != null and dto.idSet.size() > 0">
                AND t1.id in
                <foreach item="id" collection="dto.idSet" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </update>

    <select id="getChildList" resultType="com.saida.services.converge.entity.VirtualOrganTreeEntity">
        SELECT
        t.*,
        d.deviceTotalNum,
        d.onlineDeviceNum,
        p.organ_code as parentCode
        FROM
        virtual_organ_tree t
        LEFT JOIN (
        SELECT
        r.virtual_organ_id,
        count( 1 ) deviceTotalNum,
        COUNT(
        IF
        ( o.status = 1, 1, NULL )) onlineDeviceNum
        FROM
        virtual_organ_device_relative r
        LEFT JOIN ops_device_channel o ON r.channel_id = o.channel_id
        where o.id is not null
        GROUP BY
        r.virtual_organ_id
        ) d ON t.id = d.virtual_organ_id
        LEFT JOIN virtual_organ_tree p on t.parent_id = p.id
        <where>
            <if test="entity.id != null ">
                and t.id = #{entity.id}
            </if>
            <if test="entity.idChain != null and entity.idChain != ''">
                FIND_IN_SET(#{entity.id}, t.id_chain)
            </if>
            <if test="entity.organCode != null and entity.organCode != ''">
                and t.organ_code = #{entity.organCode}
            </if>
            <if test="entity.parentId != null and entity.includeSelf == null">
                and t.parent_id = #{entity.parentId}
            </if>
            <if test="entity.parentId != null and entity.includeSelf != null and entity.includeSelf == 1">
                and (t.parent_id = #{entity.parentId} or t.id = #{entity.parentId})
            </if>
            <if test="entity.name != null and entity.name != ''">
                and t.name like concat('%',#{entity.name},'%')
            </if>
            <if test="entity.shareStatus != null">
                and t.share_status = #{entity.shareStatus}
            </if>
        </where>
        order by
        t.create_time
    </select>

    <select id="getChild" resultType="com.saida.services.converge.entity.VirtualOrganTreeEntity">
        SELECT
        t.*,
        d.deviceTotalNum,
        d.onlineDeviceNum,
        p.organ_code as parentCode
        FROM
        virtual_organ_tree t
        LEFT JOIN (
        SELECT
        r.virtual_organ_id,
        count( 1 ) deviceTotalNum,
        COUNT(
        IF
        ( o.status = 1, 1, NULL )) onlineDeviceNum
        FROM
        virtual_organ_device_relative r
        LEFT JOIN ops_device_channel o ON r.channel_id = o.channel_id
        where o.id is not null
        GROUP BY
        r.virtual_organ_id
        ) d ON t.id = d.virtual_organ_id
        LEFT JOIN virtual_organ_tree p on t.parent_id = p.id
        <where>
            <if test="entity.id != null ">
                and t.id = #{entity.id}
            </if>
            <if test="entity.idChain != null and entity.idChain != ''">
                FIND_IN_SET(#{entity.id}, t.id_chain)
            </if>
            <if test="entity.organCode != null and entity.organCode != ''">
                and t.organ_code = #{entity.organCode}
            </if>
            <if test="entity.parentId != null and entity.includeSelf == null">
                and t.parent_id = #{entity.parentId}
            </if>
            <if test="entity.parentId != null and entity.includeSelf != null and entity.includeSelf == 1">
                and (t.parent_id = #{entity.parentId} or t.id = #{entity.parentId})
            </if>
            <if test="entity.name != null and entity.name != ''">
                and t.name like concat('%',#{entity.name},'%')
            </if>
            <if test="entity.shareStatus != null">
                and t.share_status = #{entity.shareStatus}
            </if>
        </where>
        order by
        t.create_time
    </select>

    <select id="getVOChannelList" resultType="com.saida.services.converge.entity.VirtualOrganTreeEntity">
        select c.id,t.id as parent_id,t.name,t.parent_id,t.sort,t.organ_code,t.id_chain,o.status as is_online,c.channel_id,c.channel_name,c.bid as channel_bid,o.id as device_id,t.id,r.virtual_organ_id as vo_tree_id
        from ops_device_channel c
        left join virtual_organ_device_relative r on r.channel_id = c.channel_id
        left join ops_device o on c.device_sn = o.sn
        left join virtual_organ_tree t on r.virtual_organ_id = t.id
        where c.delete_flag = 1
            <if test="entity.id != null ">
                and t.id = #{entity.id}
            </if>
            <if test="entity.id != null ">
                and r.top_organ_id = #{entity.id}
            </if>
            <if test="entity.channelName != null and entity.channelName != ''">
                and c.channel_name like concat('%', #{entity.channelName},'%')
            </if>
            <if test="entity.top != null ">
                and t.parent_id = 0
            </if>
            <if test="entity.idChain != null and entity.idChain != ''">
                and t.id_chain like concat(#{entity.idChain},'%')
            </if>
        order by
        t.create_time desc
    </select>
    <select id="getTreeListByOnline" resultType="com.saida.services.converge.entity.dto.VirtualOrganTreeDto">
        SELECT
            vot.*,
            COUNT(IF(odc.status = 1,1,null)) onlineCount,
            COUNT(IF(odc.status != 1,1,null)) offlineCount
        FROM
            virtual_organ_tree vot
                LEFT JOIN virtual_organ_device_relative vodr ON vodr.virtual_organ_id = vot.id
                LEFT JOIN ops_device_channel odc on vodr.channel_id = odc.channel_id and odc.delete_flag = 1
        WHERE 1 = 1
           and  vot.id_chain LIKE CONCAT(#{entity.idChain}, '%')
        GROUP BY
            vot.id
        order by vot.id_chain

    </select>

    <update id="updateShareStatusByIds">
        update virtual_organ_tree set share_status = #{dto.shareStatus}, share_code_type = #{dto.shareCodeType}
        <where>
            <if test="orgIds != null and orgIds.size > 0">
                and id in
                <foreach collection="orgIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>

    <select id="getParentCodeByIds" resultType="com.saida.services.converge.entity.VirtualOrganTreeEntity">
       select * from virtual_organ_tree
        where id in ( select parent_id from virtual_organ_tree where id in
        <where>
            <foreach collection="orgIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </where>
        )
    </select>
</mapper>