<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.saida.services.system.peopleDeployControl.mapper.PeopleIdentifyDeployControlMapper">

    <resultMap type="com.saida.services.system.peopleDeployControl.vo.PeopleIdentifyDeployControlPageQryVo" id="PeopleIdentifyDeployControlPageQryMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="cloudServerId" column="cloud_server_id" jdbcType="INTEGER"/>
        <result property="timeTemplateId" column="time_template_id" jdbcType="INTEGER"/>
        <result property="faceSimilarity" column="face_similarity" jdbcType="VARCHAR"/>
        <result property="samePersonAlarmInterval" column="same_person_alarm_interval" jdbcType="INTEGER"/>
        <result property="samePersonAlarmIntervalUnit" column="same_person_alarm_interval_unit" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="runStartTime" column="run_start_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <collection property="groupIds" javaType="list" ofType="Long" select="selectGroupsByDeployId" column="id"/>
        <collection property="cameraIds" javaType="list" ofType="String" select="selectDevicesByDeployId" column="id"/>
        <collection property="algorithmIds" javaType="list" ofType="Long" select="selectAlgorithmsByDeployId" column="id"/>
    </resultMap>

    <select id="listPage" resultMap="PeopleIdentifyDeployControlPageQryMap">
        select
            t.*
        from people_identify_deploy_control t
        where 1=1
        <if test="name != null and name != ''">
            and t.name like concat('%',#{name},'%')
        </if>
        <if test="status != null">
            and t.status = #{status}
        </if>
        order by t.create_time  desc
    </select>
    <select id="selectGroupsByDeployId" resultType="Long">
        select
            group_id
        from deploy_control_group_ref
        where deploy_control_id = #{id}
    </select>
    <select id="selectDevicesByDeployId" resultType="Long">
        select
            camera_id
        from deploy_control_device_channel_ref
        where deploy_control_id = #{id}
    </select>
    <select id="selectAlgorithmsByDeployId" resultType="Long">
        select
            algorithm_id
        from deploy_control_algorithm_ref
        where deploy_control_id = #{id}
    </select>
    <select id="queryListByDeviceAndCloudServerId"
            resultType="com.saida.services.system.peopleDeployControl.entity.PeopleIdentifyDeployControlEntity">
        select
            *
        from people_identify_deploy_control t
        left join deploy_control_device_channel_ref ref on t.id = ref.deploy_control_id
        left join deploy_control_algorithm_ref ref1 on t.id = ref1.deploy_control_id
        where
            t.status = 1
            and ref.camera_id = #{deviceId}
            and ref1.algorithm_id = #{algorithmId}
    </select>

</mapper>

