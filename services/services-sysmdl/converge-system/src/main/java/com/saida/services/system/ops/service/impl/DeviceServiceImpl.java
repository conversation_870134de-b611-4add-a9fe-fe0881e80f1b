package com.saida.services.system.ops.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.mq.rocketMq.RocketMQEnhanceTemplate;
import com.saida.services.common.mq.message.DeviceMessage;
import com.saida.services.common.tools.IdGenerateUtil;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.dto.BatchSynchronizationNameDto;
import com.saida.services.converge.dto.BatchSynchronizationNameResDto;
import com.saida.services.converge.dto.FeignUpdateDeviceOnlineDto;
import com.saida.services.converge.dto.SynchronousDeviceBasicDataDto;
import com.saida.services.converge.entity.*;
import com.saida.services.converge.entity.dto.*;
import com.saida.services.converge.entity.params.OpsDeviceParams;
import com.saida.services.converge.entity.system.ConvSysOrgEntity;
import com.saida.services.converge.enums.AccessWayType;
import com.saida.services.converge.enums.DeviceModelEnums;
import com.saida.services.converge.enums.SdkAccessType;
import com.saida.services.converge.enums.TransportProtocolEnums;
import com.saida.services.converge.qxNode.QxNodeApiEnum;
import com.saida.services.converge.qxNode.req.device.*;
import com.saida.services.converge.qxNode.resp.ChannelRecordTimeLine;
import com.saida.services.converge.qxNode.resp.device.ChannelRecordTimeLineResp;
import com.saida.services.converge.qxNode.resp.device.DeviceChannelDiagnosticsResp;
import com.saida.services.converge.qxNode.resp.device.DeviceDiagnosticsResp;
import com.saida.services.converge.qxNode.resp.device.GetDeviceBasicResp;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.entities.pojo.CountDto;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.feign.open.system.IFeignOpenSystemApiController;
import com.saida.services.open.dto.SyncDeviceByOrgFromConvDto;
import com.saida.services.open.dto.SyncIncreDeviceChannelDataFromConvDto;
import com.saida.services.open.resp.VlinkerConvergeAddDeviceResp;
import com.saida.services.system.client.nodev1.QxNodeReqService;
import com.saida.services.system.client.nodev1.QxNodeReqUtil;
import com.saida.services.system.client.nodev1.ResponseDto;
import com.saida.services.system.deviceSdk.dahua.DaHuaInitMain;
import com.saida.services.system.job.UpdateDeviceChannelJob;
import com.saida.services.system.nodeGrpc.GrpcConfig;
import com.saida.services.system.ops.dto.DeviceOnlineFrequencyDto;
import com.saida.services.system.ops.dto.DeviceSubscribeDto;
import com.saida.services.system.ops.dto.DeviceUtilizeDto;
import com.saida.services.system.ops.mapper.DeviceMapper;
import com.saida.services.system.ops.service.*;
import com.saida.services.system.ops.vo.StorageListVo;
import com.saida.services.system.ops.vo.StorageViewListVo;
import com.saida.services.system.pb.OpenCommonEnum;
import com.saida.services.system.pb.OpenCommonMessage;
import com.saida.services.system.pb.OpenSunSaida;
import com.saida.services.system.pb.SunOpenGrpc;
import com.saida.services.system.sys.service.AttributeDetailService;
import com.saida.services.system.sys.service.ConvSysOrgService;
import com.saida.services.system.sys.service.impl.ConvSysOrgServiceImpl;
import com.saida.services.system.video.deviceSdkAccessService.DeviceSdkAccessService;
import com.saida.services.system.video.param.VideoNodeBaseParam;
import com.saida.services.system.video.service.VideoCommonService;
import com.saida.services.tools.attr.AttrUtil;
import io.grpc.ManagedChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("deviceService")
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, DeviceEntity> implements DeviceService {

    @Resource
    private DeviceModelService deviceModelService;
    @Resource
    private DeviceModelVersionService deviceModelVersionService;
    @Resource
    private SignalNodeService signalNodeService;
    @Autowired(required = false)
    private RocketMQEnhanceTemplate rocketMQEnhanceTemplate;
    @Resource
    private DeviceRecordPlanService deviceRecordPlanService;
    @Resource
    private QxNodeReqUtil qxNodeReqUtil;
    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;
    @Resource
    private UpdateDeviceChannelJob updateDeviceChannelJob;
    @Resource
    private IFeignOpenSystemApiController iFeignOpenSystemApiController;
    @Autowired
    private ConvSysOrgServiceImpl orgService;
    @Autowired
    private DeviceService deviceService;

    /**
     * 获取访问方式类型列表
     * <p>
     * 本方法将枚举类AccessWayType中的所有枚举常量转换为一个CountDto对象列表
     * 每个CountDto对象包含类型ID和类型名称两个字段
     * 这对于需要展示所有访问方式类型的场景非常有用，例如UI展示或下拉选择
     */
    @Override
    public List<CountDto> getAccessWayTypeList() {
        // 获取所有AccessWayType枚举常量
        AccessWayType[] values = AccessWayType.values();
        // 将枚举常量流转换为CountDto对象列表
        return Arrays.stream(values).map(t -> {
            // 创建一个新的CountDto对象
            CountDto dto = new CountDto();
            // 设置CountDto对象的type字段为枚举常量的type值的字符串表示
            dto.setType(String.valueOf(t.getType()));
            // 设置CountDto对象的typeName字段为枚举常量的名称
            dto.setTypeName(t.getName());
            // 返回CountDto对象
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CountDto> getSdkAccessTypeList() {
        // 获取所有AccessWayType枚举常量
        SdkAccessType[] values = SdkAccessType.values();
        // 将枚举常量流转换为CountDto对象列表
        return Arrays.stream(values).map(t -> {
            // 创建一个新的CountDto对象
            CountDto dto = new CountDto();
            // 设置CountDto对象的type字段为枚举常量的type值的字符串表示
            dto.setType(String.valueOf(t.getCode()));
            // 设置CountDto对象的typeName字段为枚举常量的名称
            dto.setTypeName(t.getName());
            // 返回CountDto对象
            return dto;
        }).collect(Collectors.toList());
    }

    @Resource
    private Map<String, DeviceSdkAccessService> deviceSdkAccessServiceMap;

    @Override
    public List<CountDto> getDeviceSdkOnline(Long deviceId) {
        DeviceEntity entity = getById(deviceId);
        if (entity == null) {
            return new ArrayList<>();
        }
        // 获取所有AccessWayType枚举常量
        SdkAccessType[] values = SdkAccessType.values();
        // 将枚举常量流转换为CountDto对象列表
        return Arrays.stream(values).map(t -> {
            // 创建一个新的CountDto对象
            CountDto dto = new CountDto();
            // 设置CountDto对象的type字段为枚举常量的type值的字符串表示
            dto.setType(String.valueOf(t.getCode()));
            // 设置CountDto对象的typeName字段为枚举常量的名称
            dto.setTypeName(t.getName());
            dto.setIsOnline(2);
            if (Objects.equals(t.getCode(), entity.getSdkAccess())) {
                DeviceSdkAccessService deviceSdkAccessService = deviceSdkAccessServiceMap.get(t.getClazz());
                if (deviceSdkAccessService != null) {
                    dto.setIsOnline(deviceSdkAccessService.sdkOnline(entity) ? 1 : 2);
                }
            }
            // 返回CountDto对象
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 获取传输协议列表
     * <p>
     * 本方法旨在将传输协议枚举转换为CountDto对象列表，以便于前端展示或其他用途
     * 它利用Java 8的流式编程，对每个枚举值进行转换，创建CountDto实例并收集到一个列表中
     *
     * @return List<CountDto> 传输协议的CountDto对象列表
     */
    @Override
    public List<CountDto> getTransportProtocolList() {
        // 获取所有传输协议枚举值
        TransportProtocolEnums[] values = TransportProtocolEnums.values();
        // 使用流式编程将枚举值转换为CountDto对象列表
        return Arrays.stream(values).map(t -> {
            // 创建CountDto实例
            CountDto dto = new CountDto();
            // 设置CountDto的type属性为枚举值的类型，转换为字符串形式
            dto.setType(String.valueOf(t.getType()));
            // 设置CountDto的typeName属性为枚举值的名称
            dto.setTypeName(t.getName());
            // 返回CountDto实例
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 生成GB号码
     *
     * @param orgId 组织ID
     * @return 返回生成的GB号码或错误信息
     */
    @Override
    public Result createGbNumber(Long orgId) {
        // 根据组织ID获取组织信息
        ConvSysOrgEntity sysOrgEntity = convSysOrgService.getById(orgId);
        if (sysOrgEntity == null) {
            return Result.error("组织不存在");
        }
        // 获取指定组织ID下的所有GB号码
        List<DeviceEntity> list = super.list(new LambdaQueryWrapper<DeviceEntity>()
                .select(DeviceEntity::getDeviceCode)
                .eq(DeviceEntity::getOrgId, orgId));
        List<Integer> gbNumberListByOrgId = list.stream()
                .filter(e -> e.getDeviceCode() != null && e.getDeviceCode().length() > 7
                        && NumberUtil.isInteger(e.getDeviceCode().substring(e.getDeviceCode().length() - 7)))
                .map(e ->
                        Integer.parseInt(e.getDeviceCode().substring(e.getDeviceCode().length() - 7)))
                .collect(Collectors.toList());
        int number = 0;
        // 遍历1到9999999，寻找未使用的GB号码
        for (int i = 1; i < 9999999; i++) {
            if (!gbNumberListByOrgId.contains(i)) {
                number = i;
                break;
            }
        }
        if (number > 0) {
            // 按照特定格式生成GB号码
            String gbNumber = String.format("%-8s",
                            StrUtil.subPre(StringUtil.isBlank(sysOrgEntity.getGbCode()) ? "00000000" : sysOrgEntity.getGbCode(), 8))
                    .replace(' ', '0')
                    + "00" + "132" + String.format("%07d", number);
            return Result.ok(gbNumber);
        }
        return Result.error("获取GB号码失败");
    }

    /**
     * 分页查询设备列表
     *
     * @param entity      查询参数实体
     * @param baseRequest 基础请求对象，包含分页信息
     * @return 返回设备列表的分页信息
     */
    @Override
    public BasePageInfoEntity<OpsDeviceDto> listPage(OpsDeviceParams entity, BaseRequest baseRequest) {
        // 获取当前组织ID
        Long orgId = JwtUtil.getOrgId();
        // 如果查询参数中指定了组织ID，则使用参数中的组织ID
        if (entity.getOrgId() != null) {
            orgId = entity.getOrgId();
        }
        // 如果查询参数中指定了子级并且子级为1，则需要处理组织ID链
        if (entity.getSubLevel() != null && entity.getSubLevel() == 1) {
            // 根据组织ID获取组织信息
            ConvSysOrgEntity orgEntity = convSysOrgService.getById(orgId);
            // 如果组织信息存在，则设置组织ID链
            if (orgEntity != null) {
                entity.setOrgIdChain(orgEntity.getIdChain());
            }
        }
        // 开始分页
        PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize());
        // 执行查询
        List<OpsDeviceDto> list = this.baseMapper.getList(entity);
        // 如果查询结果为空，则直接返回空的分页信息
        if (list.isEmpty()) {
            return new BasePageInfoEntity<>(new PageInfo<>(list));
        }
        // 补充设备属性信息
        fillAttr(list);
        // 返回分页信息
        return new BasePageInfoEntity<>(new PageInfo<>(list));
    }

    @Override
    public List<OpsDeviceDto> getList(OpsDeviceParams entity) {
        // 获取当前组织ID
        Long orgId = JwtUtil.getOrgId();
        // 如果查询参数中指定了组织ID，则使用参数中的组织ID
        if (entity.getOrgId() != null) {
            orgId = entity.getOrgId();
        }
        // 如果查询参数中指定了子级并且子级为1，则需要处理组织ID链
        if (entity.getSubLevel() != null && entity.getSubLevel() == 1) {
            // 根据组织ID获取组织信息
            ConvSysOrgEntity orgEntity = convSysOrgService.getById(orgId);
            // 如果组织信息存在，则设置组织ID链
            if (orgEntity != null) {
                entity.setOrgIdChain(orgEntity.getIdChain());
            }
        }
        return this.baseMapper.getList(entity);
    }

    /**
     * 更改设备的媒体服务ID
     *
     * @param deviceId       设备ID
     * @param channelId      通道ID
     * @param mediaServiceId 新的媒体服务ID
     */
    @Override
    public void changeMediaService(Long deviceId, String channelId, String mediaServiceId) {
        // 通过设备ID获取设备信息
        DeviceEntity device = getById(deviceId);
        // 检查设备是否存在
        if (device == null) {
            throw new BizRuntimeException("设备不存在");
        }
        // 通过设备的节点ID获取信号节点信息
        SignalNodeEntity node = signalNodeService.getById(device.getNodeId());
        // 检查节点是否存在
        if (node == null) {
            throw new BizRuntimeException("未绑定节点");
        }
        // 创建更改媒体服务请求对象
        ChangeMediaServiceReq deviceRebootReq = new ChangeMediaServiceReq();
        // 设置设备编码
        deviceRebootReq.setCode(device.getDeviceCode());
        // 设置新的媒体服务ID
        deviceRebootReq.setMediaServiceId(mediaServiceId);
        // 设置通道ID
        deviceRebootReq.setChannelId(channelId);
        // 发送请求到信号节点，要求更改媒体服务
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.CHANGE_MEDIA_SERVICE, node, deviceRebootReq);
        // 检查请求响应状态
        if (responseDto.getHttpCode() != 200) {
            // 如果请求失败，抛出异常并包含错误信息
            throw new BizRuntimeException(QxNodeApiEnum.CHANGE_MEDIA_SERVICE.getDes() + "失败");
        }
    }

    /**
     * 重启设备
     *
     * @param deviceId 设备ID
     * @throws BizRuntimeException 业务运行时异常
     */
    @Override
    public DtoResult<Void> reboot(Long deviceId) {
        // 获取设备信息
        DeviceEntity device = this.getById(deviceId);
        if (device == null) {
            throw new BizRuntimeException("设备不存在");
        }
        VideoNodeBaseParam param = new VideoNodeBaseParam();
        param.setDeviceCode(device.getDeviceCode());
        param.setDeviceEntity(device);
        return videoCommonService.deviceRestart(param);
    }

    @Override
    public BasePageInfoEntity<DeviceAndChannelDto> deviceListPageByOpen(OpsDeviceParams entity, BaseRequest baseRequest) {
        // 调用基映射器的分页查询方法，传入当前页码和每页大小以及查询条件
        try (Page<DeviceAndChannelDto> page = PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize())) {
            List<DeviceAndChannelDto> list = baseMapper.deviceListPageByOpen(entity);
            // 对查询结果进行非空校验，如果页对象为空，或者记录列表为空，或者记录列表为空列表，则直接返回页对象
            if (CollectionUtil.isEmpty(list)) {
                return new BasePageInfoEntity<>();
            }
            // 型号集合
            Set<String> modelSet = list.stream().map(DeviceEntity::getModel).filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(modelSet)) {
                List<DeviceModelEntity> deviceModelEntities = deviceModelService.list(new LambdaQueryWrapper<DeviceModelEntity>()
                        .in(DeviceModelEntity::getModel, modelSet));
                Map<String, DeviceModelEntity> deviceModelEntityMap = deviceModelEntities.stream().collect(Collectors.toMap(DeviceModelEntity::getModel, Function.identity()));
                list.forEach(deviceAndChannelDto -> deviceAndChannelDto.setDeviceModelEntity(deviceModelEntityMap.get(deviceAndChannelDto.getModel())));

                // 再查型号版本
                // 型号：版本：设备型号实体
                Map<String, Map<String, DeviceModelVersionEntity>> modelMap = new LinkedHashMap<>();
                list.stream().filter(e ->
                                StringUtil.isNotEmpty(e.getModel())
                                        && StringUtil.isNotEmpty(e.getVersion()))
                        .forEach(deviceAndChannelDto -> {
                            DeviceModelVersionEntity orDefault1 = modelMap.getOrDefault(deviceAndChannelDto.getModel()
                                    , new HashMap<>()).getOrDefault(deviceAndChannelDto.getVersion(), null);
                            if (orDefault1 == null) {
                                DeviceModelEntity modelEntity = deviceModelEntityMap.get(deviceAndChannelDto.getModel());
                                if (modelEntity != null) {
                                    DeviceModelVersionEntity one = deviceModelVersionService.getOne(new LambdaQueryWrapper<DeviceModelVersionEntity>()
                                            .eq(DeviceModelVersionEntity::getModelId, modelEntity.getId())
                                            .eq(DeviceModelVersionEntity::getVersionNum, deviceAndChannelDto.getVersion()), false);
                                    deviceAndChannelDto.setDeviceModelVersionEntity(one);
                                    Map<String, DeviceModelVersionEntity> orDefault = modelMap.getOrDefault(deviceAndChannelDto.getModel(), new HashMap<>());
                                    orDefault.put(deviceAndChannelDto.getVersion(), one);
                                    modelMap.put(deviceAndChannelDto.getModel(), orDefault);
                                }
                            } else {
                                deviceAndChannelDto.setDeviceModelVersionEntity(orDefault1);
                            }
                        });
            }
            // 返回包含查询结果的页对象
            return new BasePageInfoEntity<>(page);
        }
    }

    /**
     * 根据开启状态分页查询设备和通道信息
     * 此方法主要用于根据一定的开启状态条件，分页查询设备和通道的信息
     * 它通过调用基映射器的listPageByOpen方法来实现分页查询，并对查询结果进行简单的非空校验
     *
     * @param entity      查询条件参数对象，包含设备和通道的开启状态等信息
     * @param baseRequest 基础请求对象，包含分页信息如当前页码和每页大小
     * @return 返回一个分页对象，包含查询到的设备和通道信息，如果查询结果为空，则返回空的分页对象
     */
    @Override
    public BasePageInfoEntity<DeviceAndChannelDto> listPageByOpen(OpsDeviceParams entity, BaseRequest baseRequest) {
        // 调用基映射器的分页查询方法，传入当前页码和每页大小以及查询条件
        try (Page<DeviceAndChannelDto> page = PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize())) {
            List<DeviceAndChannelDto> list = baseMapper.listPageByOpen(entity);
            // 对查询结果进行非空校验，如果页对象为空，或者记录列表为空，或者记录列表为空列表，则直接返回页对象
            if (CollectionUtil.isEmpty(list)) {
                return new BasePageInfoEntity<>();
            }
            // 型号集合
            Set<String> modelSet = list.stream().map(DeviceEntity::getModel).filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(modelSet)) {
                List<DeviceModelEntity> deviceModelEntities = deviceModelService.list(new LambdaQueryWrapper<DeviceModelEntity>()
                        .in(DeviceModelEntity::getModel, modelSet));
                Map<String, DeviceModelEntity> deviceModelEntityMap = deviceModelEntities.stream().collect(Collectors.toMap(DeviceModelEntity::getModel, Function.identity()));
                list.forEach(deviceAndChannelDto -> deviceAndChannelDto.setDeviceModelEntity(deviceModelEntityMap.get(deviceAndChannelDto.getModel())));

                // 再查型号版本
                // 型号：版本：设备型号实体
                Map<String, Map<String, DeviceModelVersionEntity>> modelMap = new LinkedHashMap<>();
                list.stream().filter(e ->
                                StringUtil.isNotEmpty(e.getModel())
                                        && StringUtil.isNotEmpty(e.getVersion()))
                        .forEach(deviceAndChannelDto -> {
                            DeviceModelVersionEntity orDefault1 = modelMap.getOrDefault(deviceAndChannelDto.getModel()
                                    , new HashMap<>()).getOrDefault(deviceAndChannelDto.getVersion(), null);
                            if (orDefault1 == null) {
                                DeviceModelEntity modelEntity = deviceModelEntityMap.get(deviceAndChannelDto.getModel());
                                if (modelEntity != null) {
                                    DeviceModelVersionEntity one = deviceModelVersionService.getOne(new LambdaQueryWrapper<DeviceModelVersionEntity>()
                                            .eq(DeviceModelVersionEntity::getModelId, modelEntity.getId())
                                            .eq(DeviceModelVersionEntity::getVersionNum, deviceAndChannelDto.getVersion()), false);
                                    deviceAndChannelDto.setDeviceModelVersionEntity(one);
                                    Map<String, DeviceModelVersionEntity> orDefault = modelMap.getOrDefault(deviceAndChannelDto.getModel(), new HashMap<>());
                                    orDefault.put(deviceAndChannelDto.getVersion(), one);
                                    modelMap.put(deviceAndChannelDto.getModel(), orDefault);
                                }
                            } else {
                                deviceAndChannelDto.setDeviceModelVersionEntity(orDefault1);
                            }
                        });
            }
            // 返回包含查询结果的页对象
            return new BasePageInfoEntity<>(page);
        }
    }

    @Autowired
    private OpsDeviceOnlineRecordService opsDeviceOnlineRecordService;

    @Override
    public BasePageInfoEntity<OpsDeviceDto> monitorList(OpsDeviceParams params, BaseRequest baseRequest) {
        Long orgId = JwtUtil.getOrgId();
        if (params.getOrgId() != null) {
            orgId = params.getOrgId();
        }
        if (params.getSubLevel() != null && params.getSubLevel() == 1) {
            ConvSysOrgEntity orgEntity = convSysOrgService.getById(orgId);
            if (orgEntity != null) {
                params.setOrgIdChain(orgEntity.getIdChain());
            }
        }
        PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize());
        List<OpsDeviceDto> list = this.baseMapper.getList(params);
        if (list.isEmpty()) {
            return new BasePageInfoEntity<>(new PageInfo<>(list));
        }

        List<Long> deviceIdList = list.stream().map(DeviceEntity::getId).collect(Collectors.toList());
        Map<Long, DeviceOnlineFrequencyDto> frequencyMap = opsDeviceOnlineRecordService.getDeviceOnlineFrequencyByDeviceIds(deviceIdList);
        list.forEach(it -> {
            if (frequencyMap.containsKey(it.getId())) {
                DeviceOnlineFrequencyDto dto = frequencyMap.get(it.getId());
                it.setOnlineFrequency(Optional.ofNullable(dto.getFrequency()).orElse(0));
                it.setLatestTime(dto.getLatestTime());
            } else {
                it.setOnlineFrequency(0);
                it.setLatestTime(it.getCreateTime());
            }
        });

        fillAttr(list);
        return new BasePageInfoEntity<>(new PageInfo<>(list));
    }

    @Resource
    private IFeignOpenSystemApiController feignOpenSystemApiController;
    @Resource
    private DaHuaInitMain daHuaInitMain;
    @Resource
    private VideoCommonService videoCommonService;

    @Override
    public DtoResult<Void> addOrUpdate(DeviceEntity entity) {
        try {
            // 1. 校验设备基本信息
            DtoResult<Void> validationResult = validateDeviceEntity(entity);
            if (!validationResult.success()) {
                return validationResult;
            }
            SignalNodeEntity newNode = signalNodeService.getById(entity.getNodeId());
            if (newNode == null) {
                return DtoResult.error("节点不存在");
            }
            entity.setNodeVersion(newNode.getVersion());
            // 2. 处理新增或修改逻辑
            boolean isNew = entity.getId() == null;
            Integer newSdkAccess = entity.getSdkAccess();
            Integer oldSdkAccess = null;
            if (isNew) {
                DtoResult<Void> existCheck = checkIfDeviceExists(entity);
                if (!existCheck.success()) {
                    return existCheck;
                }
                handleNewDevice(entity, newNode);
            } else {
                DeviceEntity oldDevice = this.getById(entity.getId());
                if (oldDevice == null) {
                    return DtoResult.error("设备不存在");
                }
                oldSdkAccess = oldDevice.getSdkAccess();
                DtoResult<Void> existCheck = checkIfDeviceExistsForUpdate(entity);
                if (!existCheck.success()) {
                    return existCheck;
                }
                handleUpdateDevice(entity);
            }
            if (entity.getGbIn() == null){
                entity.setGbIn(1);
            }
            // 4. 处理 视频 设备接入
            if (entity.getGbIn() == 1) {
                DtoResult<Void> gbCheck = handleGbDevice(entity, newNode, isNew);
                if (!gbCheck.success()) {
                    return gbCheck;
                }
            }
            deviceServiceAsync.initSdkAccess(entity, newSdkAccess, oldSdkAccess);
            return DtoResult.ok();
        } catch (Exception e) {
            log.error("保存/更新设备失败...msg={}", e.getMessage(), e);
            return DtoResult.error("操作失败");
        }
    }


    /**
     * 校验设备基本信息
     */
    private DtoResult<Void> validateDeviceEntity(DeviceEntity entity) {
        if (StringUtil.isEmpty(entity.getSn())) {
            return DtoResult.error("设备SN码必填");
        }
        if (entity.getOrgId() == null) {
            return DtoResult.error("机构必选");
        }
        if (Objects.equals(entity.getAccessWay(), AccessWayType.PLATFORM_CASCADE.getType())) {
            return DtoResult.error("平台级联设备不允许新增");
        }
        return DtoResult.ok();
    }

    /**
     * 校验新增设备是否已存在
     */
    private DtoResult<Void> checkIfDeviceExists(DeviceEntity entity) {
        if (getOne(new LambdaQueryWrapper<DeviceEntity>().eq(DeviceEntity::getSn, entity.getSn()), false) != null) {
            return DtoResult.error("设备SN码已存在");
        }
        if (getOne(new LambdaQueryWrapper<DeviceEntity>().eq(DeviceEntity::getDeviceCode, entity.getDeviceCode()), false) != null) {
            return DtoResult.error("设备SN码已存在");
        }
        return DtoResult.ok();
    }

    /**
     * 校验修改设备是否冲突
     */
    private DtoResult<Void> checkIfDeviceExistsForUpdate(DeviceEntity entity) {
        if (getOne(new LambdaQueryWrapper<DeviceEntity>()
                .ne(DeviceEntity::getId, entity.getId())
                .eq(DeviceEntity::getSn, entity.getSn()), false) != null) {
            return DtoResult.error("设备SN码已存在");
        }
        return DtoResult.ok();
    }

    @Resource
    private OpsDeviceDiscoveryService opsDeviceDiscoveryService;

    /**
     * 处理新增设备
     */
    private void handleNewDevice(DeviceEntity entity, SignalNodeEntity newNode) {
        entity.setCreateTime(DateTime.now());
        entity.setCreateUser(JwtUtil.getUserId());
        entity.setNodeVersion(newNode.getVersion());
        if (StringUtil.isEmpty(entity.getUsername())) {
            entity.setUsername("admin");
        }
        if (StringUtil.isEmpty(entity.getPassword())) {
            entity.setPassword(IdGenerateUtil.generateInviteCode62());
        }
        OpsDeviceDiscoveryEntity deviceDiscoveryEntity = opsDeviceDiscoveryService.getOne(new LambdaQueryWrapper<OpsDeviceDiscoveryEntity>()
                .eq(OpsDeviceDiscoveryEntity::getDeviceCode, entity.getDeviceCode())
                .eq(OpsDeviceDiscoveryEntity::getNodeId, entity.getNodeId()), false);
        if (deviceDiscoveryEntity != null) {
            entity.setStatus(deviceDiscoveryEntity.getStatus());
            entity.setVersion(deviceDiscoveryEntity.getVersion());
            entity.setModel(deviceDiscoveryEntity.getModel());
            entity.setEip(deviceDiscoveryEntity.getEip());
            entity.setIip(deviceDiscoveryEntity.getIip());
            entity.setEndSideName(deviceDiscoveryEntity.getName());
            opsDeviceDiscoveryService.removeById(deviceDiscoveryEntity.getId());
        }
        save(entity);
    }

    /**
     * 处理修改设备
     */
    private void handleUpdateDevice(DeviceEntity entity) {
        entity.setUpdateTime(DateTime.now());
        entity.setUpdateUser(JwtUtil.getUserId());
        updateById(entity);

        if (StringUtil.isNotEmpty(entity.getDeviceCode())) {
            syncDeviceToExternalSystem(entity);
        }
    }

    /**
     * 设备信息同步到外部系统
     */
    private void syncDeviceToExternalSystem(DeviceEntity entity) {
        SyncDeviceByOrgFromConvDto dto = new SyncDeviceByOrgFromConvDto();
        dto.setOrgId(entity.getOrgId());
        dto.setDeviceCode(entity.getDeviceCode());
        try {
            DtoResult<Void> result = feignOpenSystemApiController.syncDeviceByOrgFromConv(dto);
            log.info("同步设备信息到能开：{}", JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("同步设备信息到能开异常...msg={}", e.getMessage());
        }
    }


    /**
     * 处理 GB 设备
     */
    private DtoResult<Void> handleGbDevice(DeviceEntity entity, SignalNodeEntity newNode, boolean isNew) {
        if (entity.getAccessWay() == null || AccessWayType.getType(entity.getAccessWay()) == null) {
            return DtoResult.error("接入方式错误");
        }
        if (StringUtil.isEmpty(entity.getNodeId())) {
            return DtoResult.error("节点必选");
        }

        if (isNew) {
            return registerGbDevice(entity, newNode);
        } else {
            return updateGbDevice(entity, newNode);
        }
    }

    /**
     * 注册 GB 设备
     */
    private DtoResult<Void> registerGbDevice(DeviceEntity entity, SignalNodeEntity newNode) {
        DtoResult<VlinkerConvergeAddDeviceResp> result = addDeviceToQxNode(entity, newNode);
        if (!result.success()) {
            removeById(entity.getId());
            return DtoResult.error(result.getMessage(), result.getError());
        }
        if (result.getData() != null) {
            VlinkerConvergeAddDeviceResp data = result.getData();
            super.update(new LambdaUpdateWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getId, entity.getId())
                    .set(DeviceEntity::getNodeXId, data.getNodeXId())
                    .set(DeviceEntity::getAuthKey, data.getAuthKey())
                    .set(DeviceEntity::getSignalingAddr, data.getSignalingAddr()));
        }
        return DtoResult.ok();
    }

    /**
     * 更新 GB 设备
     */
    private DtoResult<Void> updateGbDevice(DeviceEntity entity, SignalNodeEntity newNode) {
        List<OpsDeviceChannelEntity> channelList = opsDeviceChannelService.list(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, entity.getId()));
        DtoResult<VlinkerConvergeAddDeviceResp> result = addDeviceToQxNode(entity, newNode);
        if (result.success()) {
            if (result.getData() != null) {
                VlinkerConvergeAddDeviceResp data = result.getData();
                super.update(new LambdaUpdateWrapper<DeviceEntity>()
                        .eq(DeviceEntity::getId, entity.getId())
                        .set(DeviceEntity::getNodeXId, data.getNodeXId())
                        .set(DeviceEntity::getAuthKey, data.getAuthKey())
                        .set(DeviceEntity::getSignalingAddr, data.getSignalingAddr()));

                SyncIncreDeviceChannelDataFromConvDto syncIncreDeviceChannelDataFromConvDto = new SyncIncreDeviceChannelDataFromConvDto();
                List<SyncIncreDeviceChannelDataFromConvDto.DeviceChannelInfo> deleteList = new ArrayList<>();
                // 通道也要处理下线
                channelList.forEach(channel -> {
                    // 通知开放平台
                    SyncIncreDeviceChannelDataFromConvDto.DeviceChannelInfo deviceChannelInfo = new SyncIncreDeviceChannelDataFromConvDto.DeviceChannelInfo();
                    deviceChannelInfo.setDeviceCode(entity.getDeviceCode());
                    deviceChannelInfo.setDeviceName(entity.getName());
                    deviceChannelInfo.setChannelName(channel.getChannelName());
                    deviceChannelInfo.setChannelId(channel.getChannelId());
                    deviceChannelInfo.setOrgId(channel.getOrgId());
                    deviceChannelInfo.setOnline(channel.getStatus());
                    deleteList.add(deviceChannelInfo);
                    opsDeviceChannelService.update(new LambdaUpdateWrapper<OpsDeviceChannelEntity>()
                            .eq(OpsDeviceChannelEntity::getId, channel.getId())
                            .set(OpsDeviceChannelEntity::getDeleteFlag, 2));
                });
                syncIncreDeviceChannelDataFromConvDto.setDeleteList(deleteList);
                syncIncreDeviceChannelDataFromConvDto.setSaveOrUpdateList(new ArrayList<>());
                try {
                    DtoResult<Void> voidDtoResult = iFeignOpenSystemApiController.syncIncreDeviceChannelDataFromConv(syncIncreDeviceChannelDataFromConvDto);
                    log.info("同步设备通道数据到open系统...params={}, dtoResult={}", JSON.toJSON(syncIncreDeviceChannelDataFromConvDto), JSON.toJSON(voidDtoResult));
                } catch (Exception e) {
                    log.info("同步设备通道数据到open系统.异常...params={}, msg={}", JSON.toJSON(syncIncreDeviceChannelDataFromConvDto), e.getMessage());
                }
            }

        }
        // 只是更新 成功与否不关心
        log.info("更新设备结果：{}", result);
        entity.setUpdateTime(DateTime.now());
        updateById(entity);
        return DtoResult.ok();
    }


    /**
     * 将设备添加到信令节点
     *
     * @param entity     设备实体信息
     * @param signalNode 信号节点实体信息
     * @return 返回操作结果
     * <p>
     * 此方法用于通过信号节点将设备添加到量子节点中它首先根据设备的访问方式类型创建一个添加设备的请求，
     * 然后将设备的相关信息设置到请求对象中最后，它发送请求到量子节点如果请求成功，返回成功结果；
     * 否则，返回错误结果
     */
    @Override
    public DtoResult<VlinkerConvergeAddDeviceResp> addDeviceToQxNode(DeviceEntity entity, SignalNodeEntity signalNode) {
        return videoCommonService.addDevice(entity, signalNode);
    }


    @Override
    public Result updateDeviceTransport(Long deviceId, Integer transport) {
        DeviceEntity entity = super.getById(deviceId);
        if (entity == null) {
            return Result.error("设备不存在");
        }
        SignalNodeEntity signalNode = signalNodeService.getById(entity.getNodeId());
        if (signalNode == null) {
            return Result.error("未绑定节点");
        }
        UpdateDeviceTransportReq updateDeviceTransportReq = new UpdateDeviceTransportReq();
        TransportProtocolEnums type = TransportProtocolEnums.getType(transport);
        if (type == null) {
            throw new BizRuntimeException("接入方式错误");
        }
        updateDeviceTransportReq.setTransport(type.getQxCode());
        updateDeviceTransportReq.setDeviceId(entity.getDeviceCode());
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.UPDATE_TRANSPORT_PROTOCOL, signalNode, updateDeviceTransportReq);
        if (responseDto.getHttpCode() != 200) {
            return Result.error(QxNodeApiEnum.UPDATE_TRANSPORT_PROTOCOL.getDes() + "失败");
        }
        super.update(new LambdaUpdateWrapper<DeviceEntity>()
                .set(DeviceEntity::getTransportProtocol, transport)
                .eq(DeviceEntity::getId, entity.getId()));
        return Result.ok();
    }

    @Override
    public Result deviceSubscribe(DeviceSubscribeDto deviceSubscribeDto) {
        List<DeviceEntity> deviceEntities = super.listByIds(deviceSubscribeDto.getIds());
        if (deviceEntities == null || deviceEntities.isEmpty()) {
            return Result.error("设备不存在");
        }
        Map<String, List<DeviceEntity>> collect = deviceEntities.stream().collect(Collectors.groupingBy(DeviceEntity::getNodeId));
        collect.forEach((nodeId, deviceList) -> {
            SignalNodeEntity node = signalNodeService.getById(nodeId);
            if (node != null) {
                DeviceSubscribeReq deviceSubscribeReq = new DeviceSubscribeReq();
                List<String> deviceCodes = deviceList.stream().map(DeviceEntity::getDeviceCode).collect(Collectors.toList());
                deviceSubscribeReq.setDeviceIds(deviceCodes);
                deviceSubscribeReq.setAlarm(deviceSubscribeDto.getAlarmSubscribe() == 1);
                deviceSubscribeReq.setCatalog(deviceSubscribeDto.getCatalogSubscribe() == 1);
                deviceSubscribeReq.setPosition(deviceSubscribeDto.getPositionSubscribe() == 1);
                ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.SUBSCRIBE_DEVICE, node, deviceSubscribeReq);
                if (responseDto.getHttpCode() != 200) {
                    throw new BizRuntimeException(QxNodeApiEnum.SUBSCRIBE_DEVICE.getDes() + "失败");
                }
                this.update(new LambdaUpdateWrapper<DeviceEntity>()
                        .in(DeviceEntity::getId, deviceList.stream().map(DeviceEntity::getId).collect(Collectors.toList()))
                        .set(DeviceEntity::getAlarmSubscribe, deviceSubscribeDto.getAlarmSubscribe())
                        .set(DeviceEntity::getCatalogSubscribe, deviceSubscribeDto.getCatalogSubscribe())
                        .set(DeviceEntity::getPositionSubscribe, deviceSubscribeDto.getPositionSubscribe()));

            }
        });
        return Result.ok();
    }

    @Override
    public Result synchronousDeviceBasicData(SynchronousDeviceBasicDataDto dto) {
        List<DeviceEntity> list = super.listByIds(dto.getDeviceIds());
        Map<String, List<DeviceEntity>> collect = list.stream().collect(Collectors.groupingBy(DeviceEntity::getNodeId));
        collect.keySet().forEach(nodeId -> {
            SignalNodeEntity node = signalNodeService.getById(nodeId);
            if (node != null) {
                List<DeviceEntity> deviceEntityList = collect.get(nodeId);
                deviceEntityList.forEach(deviceEntity -> {
                    GetDeviceBasicReq getDeviceBasicReq = new GetDeviceBasicReq();
                    getDeviceBasicReq.setDeviceId(deviceEntity.getDeviceCode());
                    ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_DEVICE_BASIC, node, getDeviceBasicReq);
                    if (responseDto.getHttpCode() == 200) {
                        GetDeviceBasicResp getDeviceBasicResp = JSON.parseObject(responseDto.getRes(), GetDeviceBasicResp.class);
                        if (StringUtil.isNotEmpty(getDeviceBasicResp.getName())) {
                            deviceEntity.setEndSideName(getDeviceBasicResp.getName());
                        }
                        deviceEntity.setExpiration(getDeviceBasicResp.getExpiration());
                        deviceEntity.setHeartBeatCount(getDeviceBasicResp.getHeartBeatCount());
                        deviceEntity.setHeartBeatInterval(getDeviceBasicResp.getHeartBeatInterval());
                        super.updateById(deviceEntity);
                    }
                });
            }

        });
        return Result.ok();
    }


    @Resource
    private DeviceServiceAsyncImpl deviceServiceAsync;

    @Override
    public Result batchSynchronizationName(BatchSynchronizationNameDto dto) {

        Map<Long, Integer> beanMap = dto.getDeviceIds().stream().collect(
                Collectors.toMap(Function.identity(), v -> 2, (v1, v2) -> v1));
        List<DeviceEntity> list = super.listByIds(dto.getDeviceIds());
        if (dto.getType() == 1) {
            // 端->平台
            list.forEach(deviceEntity -> {
                if (StringUtil.isNotEmpty(deviceEntity.getEndSideName())) {
                    deviceEntity.setName(deviceEntity.getEndSideName());
                    super.updateById(deviceEntity);
                    beanMap.put(deviceEntity.getId(), 1);
                }
            });
        } else {
            // 平台->端
            Map<String, List<DeviceEntity>> collect = list.stream().collect(Collectors.groupingBy(DeviceEntity::getNodeId));
            collect.keySet().forEach(nodeId -> {
                SignalNodeEntity node = signalNodeService.getById(nodeId);
                if (node != null) {
                    List<DeviceEntity> deviceEntityList = collect.get(nodeId);
                    deviceEntityList.forEach(deviceEntity -> {
                        SetDeviceBasicReq getDeviceBasicReq = new SetDeviceBasicReq();
                        getDeviceBasicReq.setDeviceId(deviceEntity.getDeviceCode());
                        getDeviceBasicReq.setName(deviceEntity.getName());
                        getDeviceBasicReq.setExpiration(deviceEntity.getExpiration());
                        getDeviceBasicReq.setHeartBeatCount(deviceEntity.getHeartBeatCount());
                        getDeviceBasicReq.setHeartBeatInterval(deviceEntity.getHeartBeatInterval());
                        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.UPDATE_DEVICE_BASIC, node, getDeviceBasicReq);
                        if (responseDto.getHttpCode() == 200) {
                            beanMap.put(deviceEntity.getId(), 1);
                        }
                    });
                }
            });
            SynchronousDeviceBasicDataDto deviceBasicDataDto = new SynchronousDeviceBasicDataDto();
            deviceBasicDataDto.setDeviceIds(dto.getDeviceIds());
            deviceServiceAsync.synchronousDeviceBasicDataAsync(deviceBasicDataDto);
        }

        BatchSynchronizationNameResDto resDto = new BatchSynchronizationNameResDto();
        resDto.setType(dto.getType());
        // 将Map转换为List<BatchSynchronizationNameResDto.Bean>
        List<BatchSynchronizationNameResDto.Bean> beanList = beanMap.entrySet().stream()
                .map(entry -> new BatchSynchronizationNameResDto.Bean(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
        resDto.setDeviceIds(beanList);
        return Result.ok(resDto);
    }

    private List<OpsDeviceDto> fillAttr(List<OpsDeviceDto> records) {
        if (records == null || records.isEmpty()) {
            return new ArrayList<>();
        }
        Map<Object, Object> dicMap = new HashMap<>();
        List<String> nodeIds = records.stream().map(DeviceEntity::getNodeId).filter(nodeId -> !StringUtil.isEmpty(nodeId)).distinct().collect(Collectors.toList());
        if (!nodeIds.isEmpty()) {
            List<SignalNodeEntity> nodeList = signalNodeService.listByIds(nodeIds);
            if (nodeList != null && !nodeList.isEmpty()) {
                dicMap.putAll(nodeList.stream().collect(Collectors.toMap(SignalNodeEntity::getId, SignalNodeEntity::getName)));
            }
        }
        Map<Long, String> attrMap = attributeDetailService.getAllIdNameMap();
        if (attrMap != null && !attrMap.isEmpty()) {
            dicMap.putAll(attrMap);
        }
        // 型号集合
        Set<String> modelSet = records.stream().map(DeviceEntity::getModel).filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
        Map<String, DeviceModelEntity> collect = new HashMap<>();
        if (CollectionUtil.isNotEmpty(modelSet)) {
            List<DeviceModelEntity> deviceModelEntities = deviceModelService.list(new LambdaQueryWrapper<DeviceModelEntity>()
                    .in(DeviceModelEntity::getModel, modelSet));
            collect.putAll(deviceModelEntities.stream().collect(Collectors.toMap(DeviceModelEntity::getModel, Function.identity(), (key1, key2) -> key1)));

        }
        records.replaceAll(o -> {
            o.setDeviceModelCode(collect.getOrDefault(o.getModel(), new DeviceModelEntity()).getDeviceType());
            o.setTurnstile(collect.getOrDefault(o.getModel(), new DeviceModelEntity()).getTurnstile());
            o.setDeviceModelName(DeviceModelEnums.getName(o.getDeviceModelCode()));
            return AttrUtil.putAttr(o, dicMap);
        });
        return records;
    }

    @Override
    public OpsDeviceDto getInfo(Long id) {
        DeviceEntity device = getById(id);
        if (device == null) {
            return null;
        }
        return fillAttr(new ArrayList<OpsDeviceDto>() {{
            add(BeanUtil.copyProperties(device, OpsDeviceDto.class));
        }}).get(0);
    }

    @Override
    public void delete(Long id) {
        removeById(id);
    }

    @Autowired
    private AttributeDetailService attributeDetailService;

    @Override
    public List<CountDto> groupByDeviceType(DeviceEntity entity) {
//        if (entity.getTurnOnTime() == null) {
//            entity.setTurnOnTime(new Date());
//        }
        AccessWayType[] values = AccessWayType.values();
        List<CountDto> countDtos = super.baseMapper.groupByDeviceType(entity);
        Map<Long, CountDto> collect = countDtos.stream().collect(Collectors.toMap(CountDto::getTypeLong, Function.identity(), (k1, k2) -> k1));
        Map<Integer, String> dicMap = Arrays.stream(values).collect(Collectors.toMap(AccessWayType::getType, AccessWayType::getName, (k1, k2) -> k1));

        List<CountDto> res = new ArrayList<>();
        dicMap.forEach((k, v) -> {
            CountDto orDefault = collect.getOrDefault(k.longValue(), CountDto.builder().count(0).type1(k).build());
            orDefault.setName(v);
            res.add(orDefault);
        });
        return res;
    }

    @Override
    public List<CountDto> groupByDayAndDeviceType(DeviceEntity entity) {
        if (entity.getTurnOnTime() == null) {
            entity.setTurnOnTime(new Date());
        }
        AccessWayType[] values = AccessWayType.values();
        List<CountDto> countDtos = super.baseMapper.groupByDayAndDeviceType(entity);

        List<CountDto> res = new ArrayList<>();
        // 天、类型、数量
        Map<String, Map<Long, Integer>> dayMap = countDtos.stream().collect(Collectors.groupingBy(CountDto::getDays, Collectors.toMap(CountDto::getTypeLong, CountDto::getCount, (k1, k2) -> k1)));
        Map<Integer, String> dicMap = Arrays.stream(values).collect(Collectors.toMap(AccessWayType::getType, AccessWayType::getName, (k1, k2) -> k1));


        DateUtil.rangeToList(DateUtil.beginOfWeek(entity.getTurnOnTime(), true),
                DateUtil.endOfWeek(entity.getTurnOnTime(), true), DateField.DAY_OF_WEEK).forEach(o -> {
            Map<Long, Integer> countMap = dayMap.getOrDefault(DateUtil.format(o, "yyyy-MM-dd"), new HashMap<>());
            List<CountDto> typeMap = new ArrayList<>();
            CountDto countDto = new CountDto();
            dicMap.forEach((k, v) -> {
                CountDto build = CountDto.builder().build();
                build.setCount(countMap.getOrDefault(k.longValue(), 0));
                build.setType1(k);
                build.setName(v);
                typeMap.add(build);
            });
            countDto.setDays(DateUtil.format(o, "yyyy-MM-dd"));
            countDto.setCountList(typeMap);
            res.add(countDto);
        });
        return res;
    }

    @Override
    public List<CountDto> groupByWeekAndDeviceType(DeviceEntity entity) {
        if (entity.getTurnOnTime() == null) {
            entity.setTurnOnTime(new Date());
        }
        AccessWayType[] values = AccessWayType.values();

        List<CountDto> countDtos = super.baseMapper.groupByWeekAndDeviceType(entity);
        List<CountDto> res = new ArrayList<>();

        // 周、类型、数量
        Map<Integer, Map<Long, Integer>> dayMap = countDtos.stream().collect(Collectors.groupingBy(CountDto::getWeekOfYear, Collectors.toMap(CountDto::getTypeLong, CountDto::getCount, (k1, k2) -> k1)));
        Map<Integer, String> dicMap = Arrays.stream(values).collect(Collectors.toMap(AccessWayType::getType, AccessWayType::getName, (k1, k2) -> k1));

        DateUtil.rangeToList(DateUtil.beginOfMonth(entity.getTurnOnTime()),
                DateUtil.endOfMonth(entity.getTurnOnTime()), DateField.WEEK_OF_MONTH).forEach(o -> {
            Map<Long, Integer> countMap = dayMap.getOrDefault(o.weekOfYear(), new HashMap<>());
            List<CountDto> typeMap = new ArrayList<>();
            CountDto countDto = new CountDto();
            dicMap.forEach((k, v) -> {
                CountDto build = CountDto.builder().build();
                build.setCount(countMap.getOrDefault(k.longValue(), 0));
                build.setType1(k);
                build.setName(v);
                typeMap.add(build);
            });
            countDto.setWeekOfYear(o.weekOfYear());
            countDto.setWeekOfMonth(o.weekOfMonth());
            countDto.setCountList(typeMap);
            res.add(countDto);
        });
        return res;
    }

    @Override
    public List<CountDto> groupByMonthAndDeviceType(DeviceEntity entity) {
        if (entity.getTurnOnTime() == null) {
            entity.setTurnOnTime(new Date());
        }
        AccessWayType[] values = AccessWayType.values();

        List<CountDto> countDtos = super.baseMapper.groupByMonthAndDeviceType(entity);
        List<CountDto> res = new ArrayList<>();

        // 周、类型、数量
        Map<Integer, Map<Long, Integer>> dayMap = countDtos.stream().collect(Collectors.groupingBy(CountDto::getMonth, Collectors.toMap(CountDto::getTypeLong, CountDto::getCount, (k1, k2) -> k1)));
        Map<Integer, String> dicMap = Arrays.stream(values).collect(Collectors.toMap(AccessWayType::getType, AccessWayType::getName, (k1, k2) -> k1));
        DateUtil.rangeToList(DateUtil.beginOfYear(entity.getTurnOnTime()),
                DateUtil.endOfYear(entity.getTurnOnTime()), DateField.MONTH).forEach(o -> {
            int month = DateUtil.month(o) + 1;
            Map<Long, Integer> countMap = dayMap.getOrDefault(month, new HashMap<>());
            List<CountDto> typeMap = new ArrayList<>();
            CountDto countDto = new CountDto();
            dicMap.forEach((k, v) -> {
                CountDto build = CountDto.builder().build();
                build.setCount(countMap.getOrDefault(k.longValue(), 0));
                build.setType1(k);
                build.setName(v);
                typeMap.add(build);
            });
            countDto.setMonth(month);
            countDto.setCountList(typeMap);
            res.add(countDto);
        });
        return res;
    }

    @Override
    public List<CountDto> groupByDay(DeviceEntity entity) {
        if (entity.getTurnOnTime() == null) {
            entity.setTurnOnTime(new Date());
        }
        List<CountDto> countDtos = super.baseMapper.groupByDay(entity);

        List<CountDto> res = new ArrayList<>();
        // 天、数量
        Map<String, Integer> dayMap = countDtos.stream().collect(Collectors.toMap(CountDto::getDays, CountDto::getCount));
        DateUtil.rangeToList(DateUtil.beginOfWeek(entity.getTurnOnTime(), true),
                DateUtil.endOfWeek(entity.getTurnOnTime(), true), DateField.DAY_OF_WEEK).forEach(o -> {
            CountDto countDto = new CountDto();
            countDto.setDays(DateUtil.format(o, "yyyy-MM-dd"));
            countDto.setCount(dayMap.getOrDefault(DateUtil.format(o, "yyyy-MM-dd"), 0));
            res.add(countDto);
        });
        return res;
    }

    @Override
    public List<CountDto> groupByWeek(DeviceEntity entity) {
        if (entity.getTurnOnTime() == null) {
            entity.setTurnOnTime(new Date());
        }
        List<CountDto> countDtos = super.baseMapper.groupByWeek(entity);

        List<CountDto> res = new ArrayList<>();
        // 天、数量
        Map<Integer, Integer> dayMap = countDtos.stream().collect(Collectors.toMap(CountDto::getWeekOfYear, CountDto::getCount));
        DateUtil.rangeToList(DateUtil.beginOfMonth(entity.getTurnOnTime()),
                DateUtil.endOfMonth(entity.getTurnOnTime()), DateField.WEEK_OF_YEAR).forEach(o -> {
            CountDto countDto = new CountDto();
            countDto.setWeekOfMonth(o.weekOfMonth());
            countDto.setWeekOfYear(o.weekOfYear());
            countDto.setCount(dayMap.getOrDefault(o.weekOfYear(), 0));
            res.add(countDto);
        });
        return res;
    }

    @Override
    public List<CountDto> groupByMonth(DeviceEntity entity) {
        if (entity.getTurnOnTime() == null) {
            entity.setTurnOnTime(new Date());
        }
        List<CountDto> countDtos = super.baseMapper.groupByMonth(entity);

        List<CountDto> res = new ArrayList<>();
        // 天、数量
        Map<Integer, Integer> dayMap = countDtos.stream().collect(Collectors.toMap(CountDto::getMonth, CountDto::getCount));
        DateUtil.rangeToList(DateUtil.beginOfYear(entity.getTurnOnTime()),
                DateUtil.endOfYear(entity.getTurnOnTime()), DateField.MONTH).forEach(o -> {
            CountDto countDto = new CountDto();
            int month = DateUtil.month(o) + 1;
            countDto.setMonth(month);
            countDto.setCount(dayMap.getOrDefault(month, 0));
            res.add(countDto);
        });
        return res;
    }

    @Override
    public void updateByDeviceMessage(DeviceMessage message, DeviceEntity device) {
        try {
            LambdaUpdateWrapper<DeviceEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getId, device.getId());
            if (device.getTurnOnTime() == null) {
                lambdaUpdateWrapper.set(DeviceEntity::getTurnOnTime, DateTime.now());
            }
            if (Objects.equals(device.getAccessWay(), AccessWayType.VIRTUALLY.getType())) {
                lambdaUpdateWrapper.set(DeviceEntity::getLastMsgTime, DateTime.now());
            }
            // 状态是否变更
            boolean isStatusUpdate = !Objects.equals(device.getStatus(), message.getStatus());
            lambdaUpdateWrapper.set(DeviceEntity::getStatus, message.getStatus());
            lambdaUpdateWrapper.set(StringUtil.isNotEmpty(message.getEip()), DeviceEntity::getEip, message.getEip());
            lambdaUpdateWrapper.set(StringUtil.isNotEmpty(message.getIip()), DeviceEntity::getIip, message.getIip());
            lambdaUpdateWrapper.set(DeviceEntity::getCatalogSubscribe,
                    message.getCatalog_subscribe() == null ? 2 : message.getCatalog_subscribe() == 1 ? 1 : 2);
            lambdaUpdateWrapper.set(DeviceEntity::getAlarmSubscribe,
                    message.getAlarm_subscribe() == null ? 2 : message.getAlarm_subscribe() == 1 ? 1 : 2);
            lambdaUpdateWrapper.set(DeviceEntity::getPositionSubscribe,
                    message.getPosition_subscribe() == null ? 2 : message.getPosition_subscribe() == 1 ? 1 : 2);
            DeviceModelEntity deviceModelEntity = deviceModelVersionService.buildDeviceVersion(message.getModel(), message.getVersion());
            if (deviceModelEntity != null) {
                lambdaUpdateWrapper.set(DeviceEntity::getManufactor, deviceModelEntity.getManufactor());
            }
            if (StringUtil.isNotEmpty(message.getModel())) {
                lambdaUpdateWrapper.set(DeviceEntity::getModel, message.getModel());
            }
            if (StringUtil.isNotEmpty(message.getVersion())) {
                lambdaUpdateWrapper.set(DeviceEntity::getVersion, message.getVersion());
            }
            if (StringUtil.isNotEmpty(message.getDevice_name())) {
                lambdaUpdateWrapper.set(DeviceEntity::getEndSideName, message.getDevice_name());
            }
            lambdaUpdateWrapper.set(DeviceEntity::getLastMsgTime, DateTime.now());
            this.update(lambdaUpdateWrapper);
            log.info("更新设备：isStatusUpdate:{} deviceCode:{}", isStatusUpdate, device.getDeviceCode());
            if (isStatusUpdate) {
                OpsDeviceOnlineRecordEntity deviceOnlineRecord = new OpsDeviceOnlineRecordEntity();
                deviceOnlineRecord.setDeviceId(device.getId());
                deviceOnlineRecord.setDeviceSn(device.getSn());
                deviceOnlineRecord.setContent(message.getStatus() == 1 ? "设备上线" : "设备下线");
                deviceOnlineRecord.setCreateTime(LocalDateTime.now());
                deviceOnlineRecord.setUpdateTime(LocalDateTime.now());
                opsDeviceOnlineRecordService.save(deviceOnlineRecord);

                // 2024年08月02日16:11:28  加了通道概念  这个地方只能让通道全部下线
                try {
                    FeignUpdateDeviceOnlineDto feignUpdateDeviceOnlineDto = new FeignUpdateDeviceOnlineDto();
                    feignUpdateDeviceOnlineDto.setDeviceSn(device.getSn());
                    feignUpdateDeviceOnlineDto.setOnline(device.getStatus());
                    if (device.getStatus() == 0) {
                        opsDeviceChannelService.update(new LambdaUpdateWrapper<OpsDeviceChannelEntity>()
                                .eq(OpsDeviceChannelEntity::getDeviceSn, device.getSn())
                                .set(OpsDeviceChannelEntity::getStatus, 0)
                        );
                        try {
                            DtoResult<Void> voidDtoResult = iFeignOpenSystemApiController.updateDeviceOnline(feignUpdateDeviceOnlineDto);
                            log.info("更新设备在线状态：{}", voidDtoResult);
                        } catch (Exception e) {
                            log.info("feign 更新设备在线状态异常...msg={}", e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    log.error("feign 更新设备在线状态异常...msg={}", e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("更新设备异常...msg={}", e.getMessage(), e);
            throw new BizRuntimeException("更新设备异常");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        List<DeviceEntity> devices = baseMapper.selectBatchIds(ids);
        if (devices != null && !devices.isEmpty()) {
            // 根据源ID分组，检查是否存在相同源ID但名称或代码不同的情况
            Map<String, List<DeviceEntity>> listMap = devices.stream()
                    .collect(Collectors.groupingBy(DeviceEntity::getNodeId));
            listMap.keySet().forEach(e -> {
                SignalNodeEntity signalNode = signalNodeService.getById(e);
                if (signalNode != null) {
                    List<DeviceEntity> deviceEntities = listMap.get(e);
                    deviceEntities.forEach(device -> {
                        videoCommonService.delDevice(device, signalNode);
                    });
                }
            });
        }
        removeByIds(ids);
        // 删除录制计划
        deviceRecordPlanService.remove(new LambdaQueryWrapper<DeviceRecordPlanEntity>()
                .in(DeviceRecordPlanEntity::getDeviceId, ids));
        // 删除通道
        opsDeviceChannelService.remove(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .in(OpsDeviceChannelEntity::getDeviceId, ids));
    }

    @Override
    public OpsDeviceDto getVersionInfo(Long deviceId) {
        DeviceEntity device = getById(deviceId);
        if (device == null) {
            throw new BizRuntimeException("设备不存在");
        }
        if (StringUtil.isEmpty(device.getModel())) {
            throw new BizRuntimeException("未找到设备型号");
        }
        DeviceModelEntity model = deviceModelService.getOne(new LambdaQueryWrapper<DeviceModelEntity>().eq(DeviceModelEntity::getModel, device.getModel()).last("limit 1"));
        if (model == null) {
            throw new BizRuntimeException("未找到设备型号");
        }
        DeviceModelVersionEntity lastVersion = deviceModelVersionService.getOne(
                new LambdaQueryWrapper<DeviceModelVersionEntity>()
                        .eq(DeviceModelVersionEntity::getModelId, model.getId())
                        .isNotNull(DeviceModelVersionEntity::getNumLong)
                        .orderByDesc(DeviceModelVersionEntity::getNumLong)
                        .last("limit 1"));

        return new OpsDeviceDto() {{
            setId(deviceId);
            setVersion(device.getVersion());
            if (lastVersion != null) {
                setLastVersion(lastVersion.getVersionNum());
                setLastVersionId(lastVersion.getId());
            }
        }};
    }

    @Autowired
    private ConvSysOrgService convSysOrgService;

    @Override
    public Result regionalRanking(DeviceDto entity) {
        ConvSysOrgEntity one = convSysOrgService.getOne(new LambdaQueryWrapper<ConvSysOrgEntity>().eq(ConvSysOrgEntity::getParentId, 0));
        if (one == null) {
            return Result.error("未找到顶级机构");
        }

        if (entity.getTurnOnTime() == null) {
            entity.setTurnOnTime(new Date());
        }
        if (entity.getSort() == null) {
            entity.setSort(1);
        }
        if (entity.getType() == null) {
            entity.setType(1);
        }
        List<ConvSysOrgEntity> list = convSysOrgService.list(new LambdaQueryWrapper<ConvSysOrgEntity>()
                .eq(ConvSysOrgEntity::getParentId, one.getId()));
        List<String> orgIds = list.stream().map(o -> one.getId() + "," + o.getId()).collect(Collectors.toList());
        if (orgIds.isEmpty()) {
            return Result.ok(new ArrayList<>());
        }
        // 全量
        List<CountDto> countDtos = super.baseMapper.regionalRanking(orgIds);
        if (countDtos.isEmpty()) {
            return Result.ok(new ArrayList<>());
        }
        Map<String, Integer> collect = countDtos.stream().collect(Collectors.toMap(o -> o.getType().replaceAll(one.getId() + ",", ""), CountDto::getCount, (k1, k2) -> k1));
        // 增量
        List<CountDto> addCountList;
        if (entity.getType() == 1) {
            // 周
            addCountList = super.baseMapper.regionalRankingByWeek(orgIds, entity.getTurnOnTime());
        } else if (entity.getType() == 2) {
            // 月
            addCountList = super.baseMapper.regionalRankingByMonth(orgIds, entity.getTurnOnTime());
        } else {
            // 年if(entity.getType() == 3)
            addCountList = super.baseMapper.regionalRankingByYear(orgIds, entity.getTurnOnTime());
        }

        Map<String, Integer> addCollect = addCountList.stream().collect(Collectors.toMap(o -> o.getType().replaceAll(one.getId() + ",", ""), CountDto::getCount, (k1, k2) -> k1));

        List<CountDto> res = new ArrayList<>();
        list.forEach(o -> {
            CountDto countDto = new CountDto();
            countDto.setId(o.getId());
            countDto.setName(o.getName());
            countDto.setCount(collect.getOrDefault(o.getId().toString(), 0));
            countDto.setCount1(addCollect.getOrDefault(o.getId().toString(), 0));
            res.add(countDto);
        });
        res.sort(Comparator.comparing(CountDto::getCount));
        if (entity.getSort() == 2) {
            Collections.reverse(res);
        }
        // 只要前5条
        if (res.size() > 5) {
            return Result.ok(res.subList(0, 5));
        }
        return Result.ok(res);
    }

    @Override
    public List<ChannelAndDeviceResultDto> getChannelDeviceList(ChannelAndDeviceParamDto dto) {
        return baseMapper.getChannelDeviceList(dto);
    }

    @Override
    public Result deviceDiagnostics(Long deviceId) {
        DeviceEntity device = this.getById(deviceId);
        if (device == null) {
            return Result.error("设备不存在");
        }
        if (!Objects.equals(device.getAccessWay(), AccessWayType.GB28181.getType())) {
            return Result.error("非国标设备无法诊断！");
        }
        SignalNodeEntity node = signalNodeService.getById(device.getNodeId());
        if (node == null) {
            throw new BizRuntimeException("未绑定节点");
        }
        DeviceDiagnosticsReq deviceDiagnosticsReq = new DeviceDiagnosticsReq();
        deviceDiagnosticsReq.setDeviceId(device.getDeviceCode());
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.DEVICE_DIAGNOSTICS, node, deviceDiagnosticsReq);
        if (responseDto.getHttpCode() != 200) {
            return Result.error(QxNodeApiEnum.DEVICE_DIAGNOSTICS.getDes() + "失败");
        }
        DeviceDiagnosticsResp deviceDiagnosticsResp = JSON.parseObject(responseDto.getRes(), DeviceDiagnosticsResp.class);
        return Result.ok(deviceDiagnosticsResp);
    }

    @Override
    public Result deviceChannelDiagnostics(Long deviceId, String channelId) {
        DeviceEntity device = this.getById(deviceId);
        if (device == null) {
            return Result.error("设备不存在");
        }
        if (!Objects.equals(device.getAccessWay(), AccessWayType.GB28181.getType())) {
            return Result.error("非国标设备无法诊断！");
        }
        SignalNodeEntity node = signalNodeService.getById(device.getNodeId());
        if (node == null) {
            throw new BizRuntimeException("未绑定节点");
        }
        DeviceChannelDiagnosticsReq deviceChannelDiagnosticsReq = new DeviceChannelDiagnosticsReq();
        deviceChannelDiagnosticsReq.setChannelId(channelId);
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.VIDEO_DIAGNOSTICS, node, deviceChannelDiagnosticsReq);
        if (responseDto.getHttpCode() != 200) {
            return Result.error(QxNodeApiEnum.VIDEO_DIAGNOSTICS.getDes() + "失败");
        }
        DeviceChannelDiagnosticsResp deviceChannelDiagnosticsResp = JSON.parseObject(responseDto.getRes(), DeviceChannelDiagnosticsResp.class);
        return Result.ok(deviceChannelDiagnosticsResp);
    }

    private final ThreadPoolExecutor STORAGE_LIST_POOL = new ThreadPoolExecutor(28,
            84,
            3,
            TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(100),
            new CustomizableThreadFactory("storage_list_pool"),
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Autowired
    private QxNodeReqService qxNodeReqService;

    @Override
    public List<StorageListVo> storageList(OpsDeviceParams params) {
        // 查询设备
        DeviceEntity deviceEntity = getBaseMapper().selectById(params.getId());
        if (Objects.isNull(deviceEntity)) {
            return new ArrayList<>();
        }

        // 查询设备录像计划
        DeviceRecordPlanEntity recordPlan = deviceRecordPlanService.getOne(new LambdaQueryWrapper<DeviceRecordPlanEntity>().eq(DeviceRecordPlanEntity::getDeviceId, params.getId()));
        // 根据设备ID查询信号节点信息
        SignalNodeEntity node = signalNodeService.getById(deviceEntity.getNodeId());
//        SignalNodeEntity node = new SignalNodeEntity();
//        node.setDispatchUrl("https://demo-vlinker-xl-gb.sdccx.cn:58801/api");
//        params.setChannelId("34020000001320000203203");

        List<Date> dateList = getDatesBetweenDate(params.getStartTime(), params.getEndTime());
        List<Future<?>> futureList = new ArrayList<>();

        // 结果集
        List<StorageListVo> result = new ArrayList<>();
        Map<String, Map<String, BigDecimal>> res = new HashMap<>();
        for (Date date : dateList) {
            res.put(DateUtil.formatDate(date), new HashMap<>());
        }

        for (Date date : dateList) {
//            Future<?> localFuture = STORAGE_LIST_POOL.submit(() -> {
//                StorageListVo local = getStorageListVo(params.getId(), params.getChannelId(), recordPlan, node, date, "LOCAL");
//                res.get(DateUtil.formatDate(date)).put("LOCAL", local.getCloudStorageRate());
//            });

            Future<?> cloudFuture = STORAGE_LIST_POOL.submit(() -> {
                StorageListVo cloud = getStorageListVo(params.getId(), params.getChannelId(), recordPlan, node, date, "CLOUD");
                res.get(DateUtil.formatDate(date)).put("CLOUD", cloud.getCloudStorageRate());
            });

            futureList.add(cloudFuture);
//            futureList.add(localFuture);
        }

        for (Future<?> future : futureList) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("查询云存信息异常, e:", e);
            }
        }

        for (Map.Entry<String, Map<String, BigDecimal>> entry : res.entrySet()) {
            String date = entry.getKey();
            Map<String, BigDecimal> value = entry.getValue();
            result.add(StorageListVo.builder()
                    .date(date)
                    .localStorageRate(value.get("LOCAL"))
                    .cloudStorageRate(value.get("CLOUD"))
                    .build());
        }

        result.sort(Comparator.comparing(StorageListVo::getDate).reversed());
        return result;
    }

    @Override
    public List<StorageViewListVo> storageViewList(OpsDeviceParams params) {
        // 查询设备
        DeviceEntity deviceEntity = getBaseMapper().selectById(params.getId());
//        if (Objects.isNull(deviceEntity)) {
//            return new ArrayList<>();
//        }

        // 根据设备ID查询信号节点信息
        SignalNodeEntity node = signalNodeService.getById(deviceEntity.getNodeId());
//        SignalNodeEntity node = new SignalNodeEntity();
//        node.setDispatchUrl("https://demo-vlinker-xl-gb.sdccx.cn:58801/api");
//        if (params.getSource().equals("CLOUD")) {
//            params.setChannelId("34020000001320000203203");
//        } else if (params.getSource().equals("LOCAL")) {
//            params.setChannelId("34020000001180000220001");
//        }
        List<Date> dateList = getDatesBetweenDate(params.getStartTime(), params.getEndTime());

        List<Future<?>> futureList = new ArrayList<>();
        List<StorageViewListVo> result = new ArrayList<>();
        for (Date date : dateList) {
            Future<?> future = STORAGE_LIST_POOL.submit(() -> {
                DateTime start = DateUtil.beginOfDay(date);
                DateTime end = DateUtil.endOfDay(date);
                List<ChannelRecordTimeLine> timeLines = qxNodeReqService.getChannelRecordTimeLine(node, params.getChannelId(), start.getTime(), end.getTime(), params.getSource(), UUID.randomUUID().toString());
                log.info("timeLines:{}", JSONObject.toJSONString(timeLines));
                result.add(StorageViewListVo.builder()
                        .date(DateUtil.formatDate(date))
                        .timeLines(timeLines)
                        .build());
            });
            futureList.add(future);
        }

        for (Future<?> future : futureList) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("查询存储信息异常, e:", e);
            }
        }

        result.sort(Comparator.comparing(StorageViewListVo::getDate).reversed());
        return result;
    }

    @Override
    public void deleteByDeviceCode(String deviceCode) {
        LambdaQueryWrapper<DeviceEntity> query = Wrappers.lambdaQuery();
        query.eq(DeviceEntity::getDeviceCode, deviceCode);
        getBaseMapper().delete(query);
    }

    @Override
    public List<DeviceEntity> getListByDeviceCode(String deviceCode) {
        LambdaQueryWrapper<DeviceEntity> query = Wrappers.lambdaQuery();
        query.eq(DeviceEntity::getDeviceCode, deviceCode);
        return getBaseMapper().selectList(query);
    }

    @Override
    public DeviceEntity getBySn(String sn) {
        LambdaQueryWrapper<DeviceEntity> query = Wrappers.lambdaQuery();
        query.eq(DeviceEntity::getSn, sn);
        return getBaseMapper().selectOne(query);
    }

    private StorageListVo getStorageListVo(Long deviceId, String channelId, DeviceRecordPlanEntity recordPlan, SignalNodeEntity node, Date date, String source) {
        // 查询redis
//        String redisKey = "device_storage_rate:" + deviceId + ":" + DateUtil.formatDate(date);
//        Object dateData = redisUtil.get(redisKey);
//        if (Objects.nonNull(dateData)) {
//            return JSONObject.parseObject((String) dateData, StorageListVo.class);
//        }

//        if (source.equals("CLOUD")) {
//            channelId = "34020000001320000203203";
//        } else if (source.equals("LOCAL")) {
//            channelId = "34020000001180000220001";
//        }

        DateTime start = DateUtil.beginOfDay(date);
        DateTime end = DateUtil.endOfDay(date);
        log.info("开始调用:{}", new Date().getTime());
        GetChannelRecordTimeLineReq getChannelRecordTimeLineReq = new GetChannelRecordTimeLineReq();
        getChannelRecordTimeLineReq.setChannelId(channelId);
        getChannelRecordTimeLineReq.setStart(start.getTime());
        getChannelRecordTimeLineReq.setEnd(end.getTime());
        getChannelRecordTimeLineReq.setSource(source);
        ResponseDto responseDto = qxNodeReqUtil.sendRequest(QxNodeApiEnum.GET_CHANNEL_RECORD_TIME_LINE, node, getChannelRecordTimeLineReq);
        if (responseDto.getHttpCode() != 200) {
            throw new BizRuntimeException(QxNodeApiEnum.GET_CHANNEL_RECORD_TIME_LINE.getDes() + "失败");
        }
        ChannelRecordTimeLineResp resp = JSON.parseObject(responseDto.getRes(), ChannelRecordTimeLineResp.class);
        List<ChannelRecordTimeLine> timeLines = resp.getItems();
        log.info("调用结束:{}", new Date().getTime());

        String weekPlanDetail = "";
        if (Objects.nonNull(recordPlan)) {
            switch (getWeeks(date)) {
                case 0:
                    weekPlanDetail = Optional.ofNullable(recordPlan.getMon()).orElse("");
                    break;
                case 1:
                    weekPlanDetail = Optional.ofNullable(recordPlan.getTues()).orElse("");
                    break;
                case 2:
                    weekPlanDetail = Optional.ofNullable(recordPlan.getWed()).orElse("");
                    break;
                case 3:
                    weekPlanDetail = Optional.ofNullable(recordPlan.getThur()).orElse("");
                    break;
                case 4:
                    weekPlanDetail = Optional.ofNullable(recordPlan.getFri()).orElse("");
                    break;
                case 5:
                    weekPlanDetail = Optional.ofNullable(recordPlan.getSat()).orElse("");
                    break;
                case 6:
                    weekPlanDetail = Optional.ofNullable(recordPlan.getSun()).orElse("");
                    break;
            }
        }

        // 录像计划时长
        int hours = StringUtil.isEmpty(weekPlanDetail) ? 0 : weekPlanDetail.split(",").length;
        // 录像计划总秒数(毫秒)
        Long seconds = hours * 60 * 60 * 1000L;
        if (DateUtil.formatDate(date).equals(DateUtil.formatDate(new Date()))) {
            seconds = new Date().getTime() - DateUtil.beginOfDay(new Date()).getTime();
        }
        Long timeLineSeconds = CollectionUtil.isEmpty(timeLines) ? 0L : timeLines.stream().mapToLong(ChannelRecordTimeLine::getDuration).sum();
        log.info("录像计划时长:{}, 录像时长:{}, SOURCE:{}", seconds, timeLineSeconds, source);
        BigDecimal cloudStorageRate;
        if (timeLineSeconds.compareTo(seconds) > 0) {
            cloudStorageRate = BigDecimal.ONE.multiply(new BigDecimal(100));
        } else {
            cloudStorageRate = timeLineSeconds == 0 ? BigDecimal.ZERO : new BigDecimal(timeLineSeconds).divide(new BigDecimal(seconds), 2, BigDecimal.ROUND_DOWN).multiply(new BigDecimal(100));
        }
        StorageListVo storageVo = StorageListVo.builder()
                .date(DateUtil.formatDate(date))
                .cloudStorageRate(cloudStorageRate)
                .build();

        // 保存到redis
//        if (!DateUtil.formatDate(date).equals(DateUtil.formatDate(new Date()))) {
//            // 当天数据不保存redis
//            redisUtil.set(redisKey, JSONObject.toJSONString(storageVo), 7, TimeUnit.DAYS);
//        }
        return storageVo;
    }

    private Integer getWeeks(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_WEEK);
    }

    /**
     * 获取两个日期之间的所有天数
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public List<Date> getDatesBetweenDate(Date startDate, Date endDate) {
        List<Date> dates = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        while (!calendar.getTime().after(endDate)) {
            dates.add(calendar.getTime());
            calendar.add(Calendar.DATE, 1); // 每次加一天
        }

        return dates;
    }

    @Resource
    private GrpcConfig grpcConfig;

    @Override
    public Result deviceUtilize(DeviceUtilizeDto deviceUtilizeDto) {
        DeviceEntity deviceEntity = super.getById(deviceUtilizeDto.getDeviceId());
        if (deviceEntity == null) {
            return Result.error("设备不存在");
        }
        ConvSysOrgEntity sysOrg = orgService.getById(deviceEntity.getOrgId());
        if (sysOrg == null) {
            return Result.error("设备所属区域不存在");
        }
        SignalNodeEntity oldNode = signalNodeService.getById(deviceEntity.getNodeId());
        if (oldNode == null) {
            return Result.error("原始节点不存在");
        }
        SignalNodeEntity signalNode = signalNodeService.getById(deviceUtilizeDto.getNodeId());
        if (signalNode == null) {
            return Result.error("节点不存在");
        }
        if (signalNode.getVersion() != 2) {
            return Result.error("节点版本不匹配");
        }
        List<OpsDeviceChannelEntity> list = opsDeviceChannelService.list(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .isNull(OpsDeviceChannelEntity::getNodeXId)
                .eq(OpsDeviceChannelEntity::getDeleteFlag, 1));
        if (list.isEmpty()) {
            return Result.error("该设备没有老版本的通道? 无法利旧");
        }
        Map<OpsDeviceChannelEntity, String> errorList = new HashMap<>();
        String platformId = sysOrg.getGbCode();
        if (StringUtil.isEmpty(platformId)) {
            platformId = oldNode.getSipId();
        }
        if (StringUtil.isEmpty(platformId)) {
            platformId = "LIJIU-CONV";
        }
        try {
            // 把国标节点新增到新节点上
            ManagedChannel managedChannel = grpcConfig.getGrpcChannel(signalNode.getId());
            if (managedChannel == null) {
                return Result.error("节点不存在");
            }
            SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = SunOpenGrpc.newBlockingStub(managedChannel);
            OpenCommonMessage.RequestAuthInfo authUser = grpcConfig.getAuthUser();
            for (OpsDeviceChannelEntity opsDeviceChannel : list) {
                OpenSunSaida.BindAncientGBChannelRequest bindAncientGBChannel = OpenSunSaida.BindAncientGBChannelRequest
                        .newBuilder()
                        .setAuthInfo(authUser)
                        .setPlatformId(platformId)
                        .setDeviceId(deviceEntity.getDeviceCode())
                        .setChannelId(opsDeviceChannel.getBid())
                        .setPlanetServerTag(oldNode.getId())
                        .build();
                OpenSunSaida.BindAncientGBChannelReply reply = sunOpenBlockingStub.bindAncientGBChannel(bindAncientGBChannel);
                log.info("新增国标节点到新节点上：{}  {}", reply.getStatus(), reply.getDesc());
                if (reply.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
                    errorList.put(opsDeviceChannel, reply.getDesc());
                } else {
                    opsDeviceChannelService.update(new LambdaUpdateWrapper<OpsDeviceChannelEntity>()
                            .eq(OpsDeviceChannelEntity::getId, opsDeviceChannel.getId())
                            .set(OpsDeviceChannelEntity::getNodeXId, reply.getXId()));
                }
            }
        } catch (Exception e) {
            log.error("设备利旧失败！", e);
            return Result.error("设备利旧失败");
        }
        if (errorList.isEmpty()) {
            deviceService.update(new LambdaUpdateWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getId, deviceEntity.getId())
                    .set(DeviceEntity::getUtilizeNode, deviceUtilizeDto.getNodeId()));
            return Result.ok("利旧成功");
        } else {
            return Result.error("利旧失败-当前设备的节点不支持利旧！", errorList);
        }
    }
}
