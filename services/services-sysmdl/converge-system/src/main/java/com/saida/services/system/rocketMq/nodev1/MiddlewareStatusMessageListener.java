package com.saida.services.system.rocketMq.nodev1;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.common.mq.message.MiddlewareStatusMessage;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.OpsHostDataEntity;
import com.saida.services.converge.entity.OpsMiddlewareStatusEntity;
import com.saida.services.converge.entity.OpsMiddlewareStatusRecordEntity;
import com.saida.services.system.ops.service.OpsHostDataService;
import com.saida.services.system.ops.service.OpsMiddlewareStatusRecordService;
import com.saida.services.system.ops.service.OpsMiddlewareStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Date;

@Slf4j
@Component

public class MiddlewareStatusMessageListener implements VLinkerMqMessageListener {

    /**
     * 2024年04月11日10:07:38
     * 已经不通过mq上报
     */
    @Autowired
    private OpsMiddlewareStatusService opsMiddlewareStatusService;
    @Autowired
    private OpsMiddlewareStatusRecordService opsMiddlewareStatusRecordService;
    @Autowired
    private OpsHostDataService opsHostDataService;


    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("middleware_status")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        MiddlewareStatusMessage message = JSON.parseObject(messageBody, MiddlewareStatusMessage.class);
        handleMessage(message);
    }


    protected void handleMessage(MiddlewareStatusMessage message) {
        // 此时这里才是最终的业务处理，代码只需要处理资源类关闭异常，其他的可以交给父类重试
        if (StringUtil.isEmpty(message.getMac())) {
            return;
        }
        if (StringUtil.isEmpty(message.getIp())) {
            return;
        }
        log.info("[mq:middleware_status] message:{}", message);
        OpsHostDataEntity one = opsHostDataService.getOne(new LambdaQueryWrapper<OpsHostDataEntity>()
                .eq(OpsHostDataEntity::getMac, message.getMac()));
        if (one == null) {
            return;
        }
        OpsMiddlewareStatusEntity middlewareStatusEntity = opsMiddlewareStatusService.getOne(new LambdaQueryWrapper<OpsMiddlewareStatusEntity>()
                .eq(OpsMiddlewareStatusEntity::getName, message.getName())
                .eq(OpsMiddlewareStatusEntity::getHostId, one.getId()));
        if (middlewareStatusEntity == null) {
            middlewareStatusEntity = new OpsMiddlewareStatusEntity();
            middlewareStatusEntity.setCreateTime(new Date());
            middlewareStatusEntity.setHostId(one.getId());
        }
        middlewareStatusEntity.setNodeId(one.getNodeId());
        middlewareStatusEntity.setUpdateTime(new Date());
        BeanUtil.copyProperties(message, middlewareStatusEntity);
        opsMiddlewareStatusService.saveOrUpdate(middlewareStatusEntity);
        OpsMiddlewareStatusRecordEntity recordEntity = new OpsMiddlewareStatusRecordEntity();
        BeanUtil.copyProperties(middlewareStatusEntity, recordEntity);
        recordEntity.setId(null);
        opsMiddlewareStatusRecordService.save(recordEntity);
    }
}

