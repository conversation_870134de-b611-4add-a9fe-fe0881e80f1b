package com.saida.services.system.rocketMq.nodev1;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.OpsRecordingServicesEntity;
import com.saida.services.converge.entity.OpsRecordingServicesRecordEntity;
import com.saida.services.system.ops.mapper.OpsRecordingServicesRecordMapper;
import com.saida.services.system.ops.service.OpsRecordingServicesService;
import com.saida.services.system.sys.dto.RecordingServicesDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

@Slf4j
@Component

public class RecordingServicesListener implements VLinkerMqMessageListener {


    @Autowired
    private OpsRecordingServicesService opsRecordingServicesService;
    @Autowired
    private OpsRecordingServicesRecordMapper opsRecordingServicesRecordMapper;

    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("record_server_status")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        RecordingServicesDto message = JSON.parseObject(messageBody, RecordingServicesDto.class);
        handleMessage(message);
    }
    

    protected void handleMessage(RecordingServicesDto message) {
        // 此时这里才是最终的业务处理，代码只需要处理资源类关闭异常，其他的可以交给父类重试
        if (message == null) {
            return;
        }
//        log.info("[mq:record_server_status]收到消息：{}", message);
        if (StringUtil.isEmpty(message.getServer_name()) || StringUtil.isEmpty(message.getNode_id())) {
            return;
        }
        OpsRecordingServicesEntity entity = opsRecordingServicesService.getOne(new LambdaQueryWrapper<OpsRecordingServicesEntity>()
                .eq(OpsRecordingServicesEntity::getServerName, message.getServer_name())
                .eq(OpsRecordingServicesEntity::getNodeId, message.getNode_id()));
        if (entity == null) {
            entity = new OpsRecordingServicesEntity();
            entity.setCreateTime(LocalDateTime.now());
            entity.setCreateBy(1L);
            entity.setUpdateBy(2L);
        }
        entity.setServerName(message.getServer_name());
        entity.setNodeId(message.getNode_id());
        entity.setDomainName(message.getDomain_name());
        entity.setExternalIp(message.getExternal_ip());
        entity.setInternalIp(message.getInternal_ip());
        entity.setNowRoutes(message.getNow_routes());
        entity.setMaxRoutes(message.getMax_routes());
        entity.setServiceId(message.getService_id());
        entity.setFlow(message.getFlow());
        entity.setStatus(1);
        entity.setUpdateTime(LocalDateTime.now());
        opsRecordingServicesService.saveOrUpdate(entity);

        OpsRecordingServicesRecordEntity entity1 = new OpsRecordingServicesRecordEntity();
        BeanUtils.copyProperties(entity, entity1);
        entity1.setId(null);
        entity1.setCreateTime(LocalDateTime.now());
        entity1.setUpdateTime(LocalDateTime.now());
        entity1.setCreateBy(1L);
        entity1.setUpdateBy(2L);
        opsRecordingServicesRecordMapper.insert(entity1);
    }


}

