package com.saida.services.system.rocketMq.message;

import com.saida.services.algorithm.entity.AlgorithmMappingEntity;
import com.saida.services.common.mq.vlinker.BaseMessage;
import com.saida.services.system.analyse.pojo.entity.TaskDispatchEntity;
import lombok.Data;

@Data
public class TaskDispatchMessage extends BaseMessage {

    private String action;

    private TaskDispatchEntity taskDispatchEntity;

    private String timePlan;

    private String deviceCode;

    private String channelId;

    private AlgorithmMappingEntity algorithmMappingEntity;
}
