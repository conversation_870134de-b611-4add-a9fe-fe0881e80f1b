package com.saida.services.system.basicData.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.service.FileService;
import com.saida.services.constant.RedisConstants;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.basicData.dto.*;
import com.saida.services.system.basicData.entity.BasicGroupEntity;
import com.saida.services.system.basicData.entity.BasicPeopleInfoEntity;
import com.saida.services.system.basicData.entity.BasicPeoplePhotoEntity;
import com.saida.services.system.basicData.mapper.BasicPeopleInfoMapper;
import com.saida.services.system.basicData.mapper.BasicPeoplePhotoMapper;
import com.saida.services.system.basicData.service.BasicGroupService;
import com.saida.services.system.basicData.service.BasicPeopleFaceFeatureService;
import com.saida.services.system.basicData.service.BasicPeopleInfoService;
import com.saida.services.system.basicData.vo.BasicPeoplePageQueryVo;
import com.saida.services.system.basicData.vo.PeopleFaceCompareVo;
import com.saida.services.system.basicData.vo.PeoplePhotoGroupVo;
import com.saida.services.system.basicData.vo.PeopleRelateVo;
import com.saida.services.system.face.dto.FaceDto;
import com.saida.services.system.face.dto.MatchResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基础信息-人员信息表(BasicPeopleInfoEntity)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-05 15:20:01
 */
@Slf4j
@Service("basicPeopleInfoService")
public class BasicPeopleInfoServiceImpl extends ServiceImpl<BasicPeopleInfoMapper, BasicPeopleInfoEntity> implements BasicPeopleInfoService {
    @Autowired
    private BasicPeoplePhotoMapper basicPeoplePhotoMapper;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private BasicPeopleFaceFeatureService basicPeopleFaceFeatureService;
    @Autowired
    private FileService fileService;
    @Resource
    private BasicGroupService basicGroupService;

    /**
     * 分页查询
     *
     * @param dto         筛选条件
     * @param pageRequest 分页对象
     * @return 查询结果
     */
    @Override
    public BasePageInfoEntity<BasicPeoplePageQueryVo> queryByPage(BasicPeoplePageQueryDto dto, BaseRequest pageRequest) {
        try (Page<BasicPeoplePageQueryVo> page = PageHelper.startPage(pageRequest.getPageNum(), pageRequest.getPageSize())) {
            List<BasicPeoplePageQueryVo> list = this.baseMapper.queryByPage(dto);
            return new BasePageInfoEntity<>(page);
        }
    }

    /**
     * 新增数据
     *
     * @param dto 实例对象
     * @return 实例对象
     */
    @Override
    public DtoResult<Integer> insert(BasicPeopleAddDto dto) {
        BasicGroupEntity byId = basicGroupService.getById(dto.getGroupId());
        if (ObjectUtil.isNotNull(byId) && byId.getType() == 0) {
            return DtoResult.error("当前分组禁止添加人员数据！");
        }
        BasicPeopleInfoEntity basicPeopleInfoEntity = new BasicPeopleInfoEntity();
        BeanUtils.copyProperties(dto, basicPeopleInfoEntity);
        //工号自增,照片主图取第一张
        if (ObjectUtil.isNull(basicPeopleInfoEntity.getNumber())) {
            Object o = redisTemplate.opsForValue().get(RedisConstants.CONV_BASIC_INFO_PEOPLE_NUMBER);
            if (ObjectUtil.isNull(o)) {
                redisTemplate.opsForValue().set(RedisConstants.CONV_BASIC_INFO_PEOPLE_NUMBER, "1000000");
            }
            basicPeopleInfoEntity.setNumber(String.valueOf(redisTemplate.opsForValue().increment(RedisConstants.CONV_BASIC_INFO_PEOPLE_NUMBER)));
        } else {
            //校验工号是否存在
            BasicPeopleInfoEntity basicPeopleInfoEntityDB = this.baseMapper.selectOne(new LambdaQueryWrapper<BasicPeopleInfoEntity>().eq(BasicPeopleInfoEntity::getNumber, basicPeopleInfoEntity.getNumber()));
            if (ObjectUtil.isNotNull(basicPeopleInfoEntityDB)) {
                return DtoResult.error("工号已存在");
            }
        }
        Long peopleId = IdWorker.getId();
        ArrayList<BasicPeoplePhotoEntity> peoplePhotoEntities = new ArrayList<>();
        for (int i = 0; i < dto.getPeoplePhotos().size(); i++) {
            try {
                BasicPeoplePhotoEntity basicPeoplePhotoEntityDto = dto.getPeoplePhotos().get(i);
                BasicPeoplePhotoEntity peoplePhotoEntity = new BasicPeoplePhotoEntity();
                Long photoId = IdWorker.getId();
                peoplePhotoEntity.setId(photoId);
                peoplePhotoEntity.setPeopleId(peopleId);
                peoplePhotoEntity.setPhotoUrl(basicPeoplePhotoEntityDto.getPhotoUrl());
                peoplePhotoEntity.setCreateTime(LocalDateTime.now());
                byte[] bytes = fileService.downloadFileAsByteArrayByCount(peoplePhotoEntity.getPhotoUrl(), 5);
                DtoResult<FaceDto> faceFeature = basicPeopleFaceFeatureService.getFaceFeature(dto.getGroupId(), peopleId, peoplePhotoEntity.getId(), null, Base64.getEncoder().encodeToString(bytes));
                if (!faceFeature.success()) {
                    continue;
                }
                basicPeoplePhotoMapper.insert(peoplePhotoEntity);
                peoplePhotoEntities.add(peoplePhotoEntity);
            } catch (Exception e) {
                log.debug("新增人员数据-人脸特征处理失败", e);
            }
        }
        if (CollectionUtils.isEmpty(peoplePhotoEntities)) {
            return DtoResult.error("人员照片处理失败，没有满足的人员照片信息");
        }
        //获取主图
        basicPeopleInfoEntity.setId(peopleId);
        basicPeopleInfoEntity.setMainPhotoId(peoplePhotoEntities.get(0).getId());
        return DtoResult.ok(this.baseMapper.insert(basicPeopleInfoEntity));
    }

    /**
     * 修改数据
     *
     * @param dto 实例对象
     * @return 实例对象
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public DtoResult<Integer> update(BasicPeopleEditDto dto) {
        BasicGroupEntity byId = basicGroupService.getById(dto.getGroupId());
        if (ObjectUtil.isNotNull(byId) && byId.getType() == 0) {
            return DtoResult.error("当前分组禁止添加人员数据！");
        }
        //处理用户图片在数据库中存在的和不存在的
        List<BasicPeoplePhotoEntity> peoplePhotoEntitiesDB = basicPeoplePhotoMapper.selectList(new LambdaQueryWrapper<BasicPeoplePhotoEntity>().eq(BasicPeoplePhotoEntity::getPeopleId, dto.getId()));
        List<BasicPeoplePhotoEntity> deletePhotoList = peoplePhotoEntitiesDB.stream().filter(basicPeoplePhotoEntity ->
                dto.getPeoplePhotos().stream().filter(basicPeoplePhotoEntityDto -> Objects.nonNull(basicPeoplePhotoEntityDto.getId()))
                        .noneMatch(basicPeoplePhotoEntityDto -> Objects.equals(basicPeoplePhotoEntityDto.getId(), basicPeoplePhotoEntity.getId()))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deletePhotoList)) {
            basicPeoplePhotoMapper.deleteBatchIds(deletePhotoList.stream().map(BasicPeoplePhotoEntity::getId).collect(Collectors.toList()));
            deletePhotoList.forEach(basicPeoplePhotoEntity -> {
                try {
                    basicPeopleFaceFeatureService.delFace(dto.getGroupId(), basicPeoplePhotoEntity.getId());
                } catch (Exception e) {
                    log.debug("删除人员人脸图片失败", e);
                }
            });
        }
        //不需要删除的
        List<BasicPeoplePhotoEntity> updatePhotoList = peoplePhotoEntitiesDB.stream().filter(basicPeoplePhotoEntity ->
                dto.getPeoplePhotos().stream().filter(basicPeoplePhotoEntityDto -> Objects.nonNull(basicPeoplePhotoEntityDto.getId()))
                        .anyMatch(basicPeoplePhotoEntityDto -> Objects.equals(basicPeoplePhotoEntityDto.getId(), basicPeoplePhotoEntity.getId()))).collect(Collectors.toList());
        ArrayList<BasicPeoplePhotoEntity> addPhotoList = new ArrayList<>();
        dto.getPeoplePhotos().stream().filter(basicPeoplePhotoEntity -> Objects.isNull(basicPeoplePhotoEntity.getId())).forEach(basicPeoplePhotoEntity -> {
            BasicPeoplePhotoEntity peoplePhotoEntity = new BasicPeoplePhotoEntity();
            Long photoId = IdWorker.getId();
            peoplePhotoEntity.setId(photoId);
            peoplePhotoEntity.setPeopleId(dto.getId());
            peoplePhotoEntity.setPhotoUrl(basicPeoplePhotoEntity.getPhotoUrl());
            peoplePhotoEntity.setCreateTime(LocalDateTime.now());
            byte[] bytes = fileService.downloadFileAsByteArrayByCount(peoplePhotoEntity.getPhotoUrl(), 5);
            DtoResult<FaceDto> faceFeature = basicPeopleFaceFeatureService.getFaceFeature(dto.getGroupId(), dto.getId(), peoplePhotoEntity.getId(), null, Base64.getEncoder().encodeToString(bytes));
            if (!faceFeature.success()) {
                return;
            }
            addPhotoList.add(peoplePhotoEntity);
            basicPeoplePhotoMapper.insert(peoplePhotoEntity);
        });
        BasicPeopleInfoEntity basicPeopleInfoEntity = new BasicPeopleInfoEntity();
        BeanUtils.copyProperties(dto, basicPeopleInfoEntity);
        if (CollectionUtils.isEmpty(addPhotoList) && CollectionUtils.isEmpty(updatePhotoList)) {
            log.info("人员信息更新，没有满足的人员照片");
            basicPeopleInfoEntity.setMainPhotoId(null);
        } else {
            //主图处理
            if (ObjectUtil.isNull(basicPeoplePhotoMapper.selectById(dto.getMainPhotoId()))) {
                //原来的主图被删掉就换个图
                if (!CollectionUtils.isEmpty(updatePhotoList)) {
                    basicPeopleInfoEntity.setMainPhotoId(updatePhotoList.get(0).getId());
                } else {
                    basicPeopleInfoEntity.setMainPhotoId(addPhotoList.get(0).getId());
                }
            }
        }
        //更新用户信息
        return DtoResult.ok(this.baseMapper.updateById(basicPeopleInfoEntity));
    }

    /**
     * 通过主键删除数据
     *
     * @param ids 主键
     * @return 是否成功
     */
    @Override
    public DtoResult<Integer> deleteById(List<String> ids) {
        List<PeoplePhotoGroupVo> list = this.baseMapper.getPeoplePhotoAndGroupIdByPhotoId(ids);
        if (CollectionUtils.isEmpty(list)) {
            return DtoResult.error("人员数据不存在");
        }
        list.forEach(vo -> {
            try {
                basicPeopleFaceFeatureService.delFace(vo.getGroupId(), vo.getPhotoId());
            } catch (Exception e) {
                log.debug("删除人员人脸图片失败", e);
            }
        });
        basicPeoplePhotoMapper.deleteBatchIds(list.stream().map(PeoplePhotoGroupVo::getPhotoId).collect(Collectors.toList()));
        return DtoResult.ok(this.baseMapper.deleteBatchIds(ids));
    }

    @Override
    public DtoResult faceCompare(MultipartFile imageLeft, MultipartFile imageRight) {
        try {
            //判断空
            if (imageLeft == null || imageRight == null) {
                return DtoResult.error("图片不能为空");
            }
            //图片大小不能大于4M
            if (imageLeft.getSize() > 4 * 1024 * 1024 || imageRight.getSize() > 4 * 1024 * 1024) {
                return DtoResult.error("图片大小不能大于4M");
            }
            String stringLeft = Base64.getEncoder().encodeToString(imageLeft.getBytes());
            String stringRight = Base64.getEncoder().encodeToString(imageRight.getBytes());
            DtoResult<FaceDto> dtoDtoResultLeft = basicPeopleFaceFeatureService.saidaFaceFeature(null, stringLeft);
            if (!dtoDtoResultLeft.success()) {
                return DtoResult.error(dtoDtoResultLeft.getMessage());
            }
            DtoResult<FaceDto> dtoDtoResultRight = basicPeopleFaceFeatureService.saidaFaceFeature(null, stringRight);
            if (!dtoDtoResultRight.success()) {
                return DtoResult.error(dtoDtoResultRight.getMessage());
            }
            return basicPeopleFaceFeatureService.faceComparisonOne(dtoDtoResultLeft.getData(), dtoDtoResultRight.getData());
        } catch (IOException e) {
            log.error("人脸比对图片转换异常", e);
            return DtoResult.error("图片转换异常");
        }
    }

    @Override
    public DtoResult<BasePageInfoEntity<PeopleFaceCompareVo>> faceSearch(MultipartFile image, List<Long> groupIds, double similarity, int pageSize, int pageNum) {
        if (image.getSize() > 4 * 1024 * 1024) {
            return DtoResult.error("图片大小不能大于4M");
        }
        //查询分组下的所有图片
        List<BasicPeoplePhotoEntity> basicPeoplePhotoEntities = basicPeoplePhotoMapper.getPhotoByGroupIds(groupIds);
        if (CollectionUtil.isEmpty(basicPeoplePhotoEntities)) {
            return DtoResult.error("当前分组下未查询到图片数据");
        }
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            String originalImage = Base64.getEncoder().encodeToString(image.getBytes());
            DtoResult<FaceDto> dtoDtoResultOriginalImage = basicPeopleFaceFeatureService.saidaFaceFeature(null, originalImage);
            if (!dtoDtoResultOriginalImage.success()) {
                return DtoResult.error(dtoDtoResultOriginalImage.getMessage());
            }
            DtoResult<MatchResult[]> dtoResult = basicPeopleFaceFeatureService.faceComparisons(groupIds.toArray(new Long[0]), dtoDtoResultOriginalImage.getData(), basicPeoplePhotoEntities.size());
            if (!dtoResult.success()) {
                return DtoResult.error(dtoResult.getMessage());
            }
            List<PeopleFaceCompareVo> vos = Arrays.stream(dtoResult.getData()).map(matchResult -> {
                        BasicPeoplePhotoEntity basicPeoplePhotoEntity = basicPeoplePhotoEntities.stream().filter(basicPeoplePhotoEntity1 -> Objects.equals(basicPeoplePhotoEntity1.getId(), matchResult.getFaceDto().getFaceId())).findFirst().orElse(null);
                        if (basicPeoplePhotoEntity != null) {
                            return PeopleFaceCompareVo.builder().photoId(basicPeoplePhotoEntity.getId()).photoUrl(basicPeoplePhotoEntity.getPhotoUrl()).similarity(matchResult.getSimilarity()).build();
                        }
                        return null;
                    }).filter(Objects::nonNull)
                    .filter(peopleFaceCompareVo -> peopleFaceCompareVo.getSimilarity() >= similarity)
                    .sorted(Comparator.comparing(PeopleFaceCompareVo::getSimilarity).reversed())
                    .collect(Collectors.toList());
            stopWatch.stop();
            long lastTaskTimeNanos = stopWatch.getTotalTimeNanos();
            if(CollectionUtil.isEmpty(vos)){
                vos = new ArrayList<>();
                vos.add(PeopleFaceCompareVo.builder().costTime(lastTaskTimeNanos).build());
                return DtoResult.ok(new BasePageInfoEntity<>(new Page<>()));
            }
            vos.get(0).setCostTime(lastTaskTimeNanos);
            // 手动构造分页对象
            Page<PeopleFaceCompareVo> resultPage = new Page<>();
            resultPage.setPageNum(pageNum);
            resultPage.setPageSize(pageSize);
            if (vos.size() > resultPage.getPageNum() * resultPage.getPageSize()) {
                resultPage.addAll(vos.subList((resultPage.getPageNum() - 1) * resultPage.getPageSize(), resultPage.getPageNum() * resultPage.getPageSize()));
            } else {
                resultPage.addAll(vos.subList((resultPage.getPageNum() - 1) * resultPage.getPageSize(), vos.size()));
            }
            resultPage.setTotal(vos.size());
            return DtoResult.ok(new BasePageInfoEntity<>(resultPage));
        } catch (IOException e) {
            log.error("以图搜图处理系统异常", e);
        }
        return DtoResult.error("图片转换异常");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DtoResult<Boolean> updateGroups(BasicBatchUpdateGroupDto dto) {
        BasicGroupEntity byId = basicGroupService.getById(dto.getGroupId());
        if (ObjectUtil.isNotNull(byId) && byId.getType() == 0) {
            return DtoResult.error("当前分组禁止添加人员数据！");
        }
        //修改人员分组，修改人员人脸特征分组
        LambdaUpdateWrapper<BasicPeopleInfoEntity> updateWrapper = new LambdaUpdateWrapper<BasicPeopleInfoEntity>().in(BasicPeopleInfoEntity::getId, dto.getPeopleIds()).set(BasicPeopleInfoEntity::getGroupId, dto.getGroupId());
        boolean update = this.update(updateWrapper);
        if (update) {
            return DtoResult.ok(basicPeopleFaceFeatureService.updateGroup(dto));
        }
        return DtoResult.ok(true);
    }

    @Override
    public DtoResult<Integer> addStrangerPhoto(BasicDefaultPeopleAddDto dto) {
        if (CollectionUtil.isEmpty(dto.getPeoplePhotos()) || StringUtils.isEmpty(dto.getAppId())) {
            return DtoResult.error("参数有误！");
        }
        BasicGroupEntity byId = basicGroupService.getById(dto.getGroupId());
        if (ObjectUtil.isNotNull(byId) && byId.getType() != 0) {
            return DtoResult.error("请使用陌生人分组新增数据");
        }
        //查询是否存在默认人员
        BasicPeopleInfoEntity defaultPeople = this.getOne(new LambdaQueryWrapper<BasicPeopleInfoEntity>()
                .eq(BasicPeopleInfoEntity::getGroupId, dto.getGroupId())
                .eq(BasicPeopleInfoEntity::getType, 0)
                .last("limit 1"));
        if (ObjectUtil.isNull(defaultPeople)) {
            //初始化默认人员
            defaultPeople = new BasicPeopleInfoEntity();
            defaultPeople.setName("陌生人");
            defaultPeople.setType(0);
            defaultPeople.setGroupId(dto.getGroupId());
            defaultPeople.setRemark("初始化默认的陌生人信息");
            defaultPeople.setCreateTime(LocalDateTime.now());
            this.save(defaultPeople);
        }
        //陌生人脸处理
        ArrayList<BasicPeoplePhotoEntity> peoplePhotoEntities = new ArrayList<>();
        for (int i = 0; i < dto.getPeoplePhotos().size(); i++) {
            try {
                BasicPeoplePhotoEntity basicPeoplePhotoEntityDto = dto.getPeoplePhotos().get(i);
                BasicPeoplePhotoEntity peoplePhotoEntity = new BasicPeoplePhotoEntity();
                Long photoId = IdWorker.getId();
                peoplePhotoEntity.setId(photoId);
                peoplePhotoEntity.setPeopleId(defaultPeople.getId());
                peoplePhotoEntity.setPhotoUrl(basicPeoplePhotoEntityDto.getPhotoUrl());
                peoplePhotoEntity.setCreateTime(LocalDateTime.now());
                byte[] bytes = fileService.downloadFileAsByteArrayByCount(peoplePhotoEntity.getPhotoUrl(), 5);
                DtoResult<FaceDto> faceFeature = basicPeopleFaceFeatureService.getFaceFeature(dto.getGroupId(), defaultPeople.getId(), peoplePhotoEntity.getId(), null, Base64.getEncoder().encodeToString(bytes));
                if (!faceFeature.success()) {
                    continue;
                }
                basicPeoplePhotoMapper.insert(peoplePhotoEntity);
                peoplePhotoEntities.add(peoplePhotoEntity);
            } catch (Exception e) {
                log.debug("新增陌生人员数据-人脸特征处理失败", e);
            }
        }
        if (CollectionUtils.isEmpty(peoplePhotoEntities)) {
            return DtoResult.error("陌生人员照片处理失败，没有满足的人员照片信息");
        }
        return DtoResult.ok(peoplePhotoEntities.size());
    }

    @Override
    public PeopleRelateVo selectPeopleRelateInfo(Long peopleId, Long faceId) {
        return this.baseMapper.selectPeopleRelateInfo(peopleId, faceId);
    }


}
