package com.saida.services.system.basicData;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.algorithm.entity.ThirdPartyEntity;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.system.sys.service.ThirdPartyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 初始化算法中台默认的人脸库
 *
 * <AUTHOR>
 * @version AlgorithmAppidInitRunner v1.0.0
 * @since 2025/6/16 19:23
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AlgorithmAppidInitRunner {
    private final ThirdPartyService thirdPartyService;
    private final ThreadPoolConfig threadPoolConfig;

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        threadPoolConfig.taskRunner(this::run);
    }

    public void run() {
        try {
            ThirdPartyEntity one = thirdPartyService.getOne(new LambdaQueryWrapper<ThirdPartyEntity>()
                    .eq(ThirdPartyEntity::getInnerSystem, 1)
                    .eq(ThirdPartyEntity::getName, "算法中台")
                    .eq(ThirdPartyEntity::getAccount, "algorithmPlatform")
                    .eq(ThirdPartyEntity::getStatus, 1)
                    .eq(ThirdPartyEntity::getInnerSystem, 1));
            if (null == one) {
                ThirdPartyEntity thirdPartyEntity = new ThirdPartyEntity();
                thirdPartyEntity.setName("算法中台");
                thirdPartyEntity.setAccount("algorithmPlatform");
                thirdPartyEntity.setInnerSystem(1);
                thirdPartyEntity.setStatus(1);
                thirdPartyService.save(thirdPartyEntity);
            }
        } catch (Exception e) {
            log.error("初始化算法中台三方平台数据失败", e);
        }

    }
}
