package com.saida.services.system.rocketMq.nodev2;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.config.oss.OSSBean;
import com.saida.services.common.config.oss.impl.DefS3Impl;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.common.service.FileService;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.*;
import com.saida.services.entities.pojo.FileModel;
import com.saida.services.feign.srv.system.IFeignSrvSystemApiController;
import com.saida.services.srv.dto.VlinkConvMsg;
import com.saida.services.system.ops.service.*;
import com.saida.services.system.ops.service.impl.DeviceModelVersionServiceImpl;
import com.saida.services.system.pb.OpenSunMessage;
import com.saida.services.system.sys.dto.AlarmNotifyDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Objects;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component

public class DeviceAlarmMessageListener implements VLinkerMqMessageListener {

    @Resource
    private DeviceService deviceService;
    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;
    @Resource
    private DeviceModelVersionServiceImpl deviceModelVersionService;



    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("node_v2_device_alarm")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    /**
     * method:
     * enum{
     * SD_ALARM_ALL = 0 , //: "全部"
     * SD_ALARM_DEVICE = 1,  // "设备报警"
     * SD_ALARM_VIDEO = 2,  //"视频画面报警"
     * SD_ALARM_DEVICE_ERROR = 3,  //"设备故障报警"
     * SD_ALARM_AI_EVENT =4 , //事件报警
     * SD_ALARM_OTHER = 5 //"其他报警"
     * }
     *
     * method为 SD_ALARM_DEVICE(设备报警)时，alarm_type的取值
     * enum {
     * SD_ALARM_DEVICE_LOST = 1, //"视频丢失报警"
     * SD_ALARM_DEVICE_OPEND = 2, //"设备防拆报警"
     * SD_ALARM_DEVICE_SD_FULL = 3, //"存储设备磁盘满报警"
     * SD_ALARM_DEVICE_HIGH_TEMP = 4, // "设备⾼温报警"
     * SD_ALARM_DEVICE_LOW_TEMP = 5 // "设备低温报警"
     * }
     *
     *  method为 SD_ALARM_VIDEO(视频画面报警)时，alarm_type的取值
     * enum
     * {
     * SD_ALARM_FAULT_BLACK_SCREEN = 1, //⿊屏
     * SD_ALARM_FAULT_SNOW_SCREEN = 2 //雪花屏
     * SD_ALARM_FAULT_COVER_SCREEN = 3 //遮挡报警
     *
     * }
     *
     *  method为 SD_ALARM_AI_EVENT(事件报警)时，alarm_type的取值
     * enum {
     * SD_ALARM_VIDEO_MANUAL = 1,// "⼈⼯视频报警"
     * SD_ALARM_VIDEO_MOVE = 2, //运动检测报警"
     * SD_ALARM_VIDEO_R = 3, // "⼊侵检测报警"
     * SD_ALARM_VIDEO_E = 4, // "视频异常检测报警"
     * SD_ALARM_VIDEO_M = 5, // "快速移动报警"
     * SD_ALARM_VIDEO_I = 6 // "⼈型侦测报警"
     * }
     * ？？
     *
     *
     *  赛达  人形检测
     *  赛达  移动检测
     *  赛达  视频遮挡
     *  赛达  区域入侵
     *
     *  method为 SD_ALARM_DEVICE_ERROR (设备故障报警)时，alarm_type的取值
     * enum {
     * SD_ALARM_FAULT_SDCARD = 1 , //"存储设备磁盘故障报警"
     * SD_ALARM_FAULT_FAN = 2, //"存储设备⻛扇故障报警"
     * }
     * method 100，type 101 按钮1 102 按钮2
     * 处理一下 直接 method * 100 + alarm_type的取值
     */

    @Resource
    private OpsDeviceAlarmService opsDeviceAlarmService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private IFeignSrvSystemApiController feignSrvSystemApiController;

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        byte[] body = vlinkerMqMessage.getData();
        OpenSunMessage.DeviceAlarmSubscribeBody deviceAlarm = null;
        try {
            deviceAlarm = OpenSunMessage.DeviceAlarmSubscribeBody.parseFrom(body);
        } catch (InvalidProtocolBufferException e) {
            log.error("mqDeviceStatusSubscribeBody parse error", e);
            return;
        }
        if (StringUtil.isEmpty(vlinkerMqMessage.getTag())) {
            log.error("参数错误");
            return;
        }
        log.info("[mq:{}]收到消息：{}，xid:{}", vLinkerTopicConfig().getTopic(), vlinkerMqMessage.getTag(), deviceAlarm.getDeviceXId());
        DeviceEntity deviceEntity = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                .eq(DeviceEntity::getNodeId,vlinkerMqMessage.getTag())
                .eq(DeviceEntity::getNodeXId, deviceAlarm.getDeviceXId()));
        if (deviceEntity == null) {
            log.error("[mq:{}]设备不存在：{}", vLinkerTopicConfig().getTopic(), deviceAlarm.getDeviceXId());
            return;
        }
        OpsDeviceChannelEntity channelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceId, deviceEntity.getId())
                .eq(OpsDeviceChannelEntity::getNodeXId, deviceAlarm.getXIdInMessage()));
        if (channelEntity == null) {
            log.error("[mq:{}]通道不存在：{}", vLinkerTopicConfig().getTopic(), deviceAlarm.getXIdInMessage());
            return;
        }
        AlarmNotifyDto dto = new AlarmNotifyDto();
        dto.setId(UUID.randomUUID().toString());
        dto.setSn(channelEntity.getDeviceSn());
        dto.setMsg_id(dto.getId());
        dto.setPriority(1);
        dto.setMethod(5);
        dto.setTimestamp(System.currentTimeMillis());
        dto.setType((deviceAlarm.getMethod() * 100) + deviceAlarm.getAlarmType());
        String key = "CONV:alarm:saida:" + deviceEntity.getId() + ":" + channelEntity.getId() + ":" + dto.getType();
        if (redisUtil.hasKey(key)) {
            log.info("redisUtil.hasKey({})", key);
            return;
        }
        if (deviceAlarm.getMethod() == 100) {
            log.info("[mq:{}]收到设备按钮消息：{} alarmType:{}", vLinkerTopicConfig().getTopic(), deviceAlarm.getDeviceXId(), deviceAlarm.getAlarmType());
            DeviceModelVersionEntity deviceModelVersion = deviceModelVersionService.getOne(new LambdaQueryWrapper<DeviceModelVersionEntity>()
                    .eq(DeviceModelVersionEntity::getModelStr, deviceEntity.getModel())
                    .eq(DeviceModelVersionEntity::getVersionNum, deviceEntity.getVersion()));
            if (deviceModelVersion != null) {
                Integer i = deviceModelVersion.getDeviceBtnDic().get(deviceAlarm.getAlarmType() - 100);
                log.info("[mq:{}]设备：{}收到设备按钮消息：{}", vLinkerTopicConfig().getTopic(), deviceEntity.getDeviceCode(), i);
                if (Objects.equals(i, 1)) {
                    // 视频通话
                    DtoResult<Void> dtoResult = feignSrvSystemApiController.vlinkConvMsg(VlinkConvMsg
                            .builder()
                            .msgId(dto.getMsg_id())
                            .deviceCode(deviceEntity.getDeviceCode())
                            .channelCode(channelEntity.getChannelId())
                            .msgType(1)
                            .build());
                    log.info("[mq:{}]设备：{}收到视频通话消息：{}", vLinkerTopicConfig().getTopic(), deviceEntity.getDeviceCode(), dtoResult);
                }else if (Objects.equals(i, 2)) {
                    // 视频通话
                    DtoResult<Void> dtoResult = feignSrvSystemApiController.vlinkConvMsg(VlinkConvMsg
                            .builder()
                            .msgId(dto.getMsg_id())
                            .deviceCode(deviceEntity.getDeviceCode())
                            .channelCode(channelEntity.getChannelId())
                            .msgType(2)
                            .build());
                    log.info("[mq:{}]设备：{}收到视频通话挂断消息：{}", vLinkerTopicConfig().getTopic(), deviceEntity.getDeviceCode(), dtoResult);
                }
            }else {
                log.info("[mq:{}]设备：{}未找到设备型号 {},{}", vLinkerTopicConfig().getTopic(), deviceEntity.getDeviceCode(),deviceEntity.getModel(),deviceEntity.getVersion());
            }
            return;
        }
        dto.setSnap_paths(new ArrayList<>());
        String imageUrl = null;
        if (StringUtil.isNotEmpty(deviceAlarm.getImageUrl())) {
            //查询s3
            DeviceRecordPlanEntity recordPlanEntity = deviceRecordPlanService.getOne(new LambdaQueryWrapper<DeviceRecordPlanEntity>()
                    .eq(DeviceRecordPlanEntity::getDeviceId, deviceEntity.getId())
                    .eq(DeviceRecordPlanEntity::getChannelId, channelEntity.getChannelId()), false);
            if (recordPlanEntity != null) {
                CloudStorageEntity cloudStorageEntity = cloudStorageService.getById(recordPlanEntity.getStorageId());
                if (cloudStorageEntity != null) {
                    OSSBean build = OSSBean
                            .builder()
                            .accessKey(cloudStorageEntity.getKeyId())
                            .secretKey(cloudStorageEntity.getSecret())
                            .endPoint(cloudStorageEntity.getEndPoint())
                            .returnPoint(cloudStorageEntity.getEndPoint())
                            .bucket(cloudStorageEntity.getBucket())
                            .region(cloudStorageEntity.getRegion())
                            .build();
                    //https://test-vlinker-minio-api.sdvideo.cn:48801/vlinker/r/1890245783440478209/d9HM61520077/20250219/007cc7a47c81e5e0dc1232cfe9a1f2e7.jpg
                    // 匹配 "vlinker" 后面的路径部分
                    String s3Prefix = (
                            cloudStorageEntity.getEndPoint().endsWith("/") ?
                                    cloudStorageEntity.getEndPoint() : cloudStorageEntity.getEndPoint() + "/"
                    ) + "/" + cloudStorageEntity.getBucket() + "/";
                    String regex = "(?<=" + s3Prefix + ")(.*)";
                    Pattern pattern = Pattern.compile(regex);
                    Matcher matcher = pattern.matcher(deviceAlarm.getImageUrl());
                    String path = ""; // 获取匹配的路径部分
                    if (!matcher.find()) {
                        log.error("没找到这个s3信息：{} ,s3Prefix:{}", deviceAlarm.getImageUrl(), s3Prefix);
                        String s3Prefix_2 = (
                                cloudStorageEntity.getEndPoint().endsWith("/") ?
                                        cloudStorageEntity.getEndPoint() : cloudStorageEntity.getEndPoint() + "/"
                        ) + cloudStorageEntity.getBucket() + "/";
                        String regex_2 = "(?<=" + s3Prefix_2 + ")(.*)";
                        Pattern pattern_2 = Pattern.compile(regex_2);
                        Matcher matcher_2 = pattern_2.matcher(deviceAlarm.getImageUrl());
                        if (matcher_2.find()) {
                            path = matcher_2.group(1);
                        } else {
                            log.error("还是 没找到这个s3信息：{},s3Prefix_2{}", deviceAlarm.getImageUrl(), s3Prefix_2);
                            return;
                        }
                    } else {
                        path = matcher.group(1);
                    }

                    log.info("找到 ossKey:{}", path);
                    String downloadUrl = defS3.downloadUrl(build, path);
                    byte[] bytes = fileService.downloadFileAsByteArrayByCount(downloadUrl, 5);
                    if (bytes == null) {
                        return;
                    }
                    String timePrefix = DateUtil.format(LocalDateTime.now(), "yyyy/MM/dd/HH/mm");
                    DtoResult<FileModel> fileModelDtoResult = fileService.uploadByte(bytes,
                            "sdkAlarm/" + recordPlanEntity.getStrategyId() + "/" + deviceEntity.getDeviceCode() + "/" + channelEntity.getChannelId() +
                                    "/" + timePrefix + "/" + IdWorker.getId() + ".jpg");
                    if (fileModelDtoResult.success()) {
                        imageUrl = fileModelDtoResult.getData().getUrl();
                    }
                }
            }
        }
        dto.getSnap_paths().add(imageUrl);
        ByteString jsonData = deviceAlarm.getJsonData();
        JSONObject ext = JSONObject.parseObject(jsonData.toStringUtf8());
        dto.setExt(ext);
        dto.setOriginalAlarmStr("sd:" + dto.getType());
        dto.setChannel_id(channelEntity.getChannelId());
        log.info("saida私有告警发送：dto:{}", JSON.toJSONString(dto));
        try {
            opsDeviceAlarmService.handleMessage(dto);
            redisUtil.set(key, "1", 30);
        } catch (Exception e) {
            log.error("saida私有发送失败：dto:{}", JSON.toJSONString(dto), e);
        }
    }

    public static void main(String[] args) {
        String a = "https://oos-xiongan.ctyunapi.cn/apkbao/r/1724271193536364546/d9HM61520079/20250222/4c5111ed613baa166cf0e8d989c2854f.jpg";
        CloudStorageEntity cloudStorageEntity = new CloudStorageEntity();
        cloudStorageEntity.setEndPoint("https://oos-xiongan.ctyunapi.cn");
        cloudStorageEntity.setBucket("apkbao");
        String s3Prefix = (
                cloudStorageEntity.getEndPoint().endsWith("/") ?
                        cloudStorageEntity.getEndPoint() : cloudStorageEntity.getEndPoint() + "/"
        ) + "/" + cloudStorageEntity.getBucket() + "/";
        String regex = "(?<=" + s3Prefix + ")(.*)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(a);
        String path = ""; // 获取匹配的路径部分
        if (!matcher.find()) {
            log.error("没找到这个s3信息：{}", a);
            String s3Prefix_2 = (
                    cloudStorageEntity.getEndPoint().endsWith("/") ?
                            cloudStorageEntity.getEndPoint() : cloudStorageEntity.getEndPoint() + "/"
            ) + cloudStorageEntity.getBucket() + "/";
            String regex_2 = "(?<=" + s3Prefix_2 + ")(.*)";
            Pattern pattern_2 = Pattern.compile(regex_2);
            Matcher matcher_2 = pattern_2.matcher(a);
            if (matcher_2.find()) {
                path = matcher_2.group(1);
            } else {
                log.error("还是 没找到这个s3信息：{}", a);
                return;
            }
        } else {
            path = matcher.group(1);
        }

        log.info("找到 ossKey:{}", path);
    }


    @Resource
    private FileService fileService;
    @Resource
    private DefS3Impl defS3;

    @Resource
    private DeviceRecordPlanService deviceRecordPlanService;
    @Resource
    private CloudStorageService cloudStorageService;
}

