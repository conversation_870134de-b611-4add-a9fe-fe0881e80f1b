package com.saida.services.system.rocketMq.nodev1;

import com.saida.services.common.mq.message.InOutPlatformMessage;
import com.saida.services.common.mq.vlinker.SendResultWrapper;
import com.saida.services.common.mq.vlinker.VLinkerMqTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class InOutPlatformProduce {

    //注入增强后的模板，可以自动实现环境隔离，日志记录
    @Autowired(required = false)
    private VLinkerMqTemplate vLinkerMqTemplate;

    public static final String GB_DOWN_TOPIC = "gb_down_platform";
    public static final String GB_UP_TOPIC = "gb_up_platform";
    public static final String VIRTUAL_ORGAN_TREE = "virtual_organ_tree";

    //新增、编辑
    public static final Integer MESSAGE_TYPE_1 = 1;
    //删除
    public static final Integer MESSAGE_TYPE_2 = 2;
    public static final Integer MESSAGE_TYPE_3 = 3;


    /**
     * 同步消息
     */
    public void send(String topic, InOutPlatformMessage message) {
        if (!GB_DOWN_TOPIC.equals(topic) && !GB_UP_TOPIC.equals(topic) && !VIRTUAL_ORGAN_TREE.equals(topic)) {
            log.error("topic is error");
            return;
        }
        SendResultWrapper send = vLinkerMqTemplate.send(topic, message);
        log.info("发送结果：{}", send);
    }
}
