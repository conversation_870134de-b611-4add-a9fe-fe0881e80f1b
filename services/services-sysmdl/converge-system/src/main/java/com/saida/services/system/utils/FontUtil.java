package com.saida.services.system.utils;

import com.saida.services.common.config.ThreadPoolConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.awt.*;
import java.io.InputStream;

/**
 * <a href="https://transfonter.org/ttc-unpack">ttc转ttf</a>
 */
@Slf4j
@Component
public class FontUtil {

    public static Font font = null;

    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        threadPoolConfig.taskRunner(this::loadFont);
    }




    public void loadFont() {
        log.info("开始加载字体");
        // 假设我们要使用一个常见的中文字体（例如："SimSun" 或 "Microsoft YaHei"）
        try (InputStream is = FontUtil.class.getClassLoader().getResourceAsStream("static/fonts/WenQuanYiZenHei-01.ttf")) {
            if (is == null) {
                log.error("加载字体失败： 字体文件不存在");
                return;
            }
            font = Font.createFont(Font.TRUETYPE_FONT, is);
            //文泉驿正黑
            log.info("字体加载成功： fontName:{}", font.getName());
        } catch (Exception e) {
            font = new Font("SimSun", Font.PLAIN, 12);
            log.error("加载字体失败： {} ", e.getMessage(), e);
        }
    }
}
