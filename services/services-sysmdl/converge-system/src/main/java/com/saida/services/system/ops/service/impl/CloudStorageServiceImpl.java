package com.saida.services.system.ops.service.impl;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.common.config.S3Config;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.config.oss.*;
import com.saida.services.common.config.oss.impl.DefS3Impl;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.common.mq.rocketMq.RocketMQEnhanceTemplate;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.CloudStorageEntity;
import com.saida.services.converge.entity.DeviceRecordPlanEntity;
import com.saida.services.converge.entity.StorageStrategyEntity;
import com.saida.services.common.mq.message.CloudStorageManagerMessage;
import com.saida.services.system.ops.mapper.CloudStorageMapper;
import com.saida.services.system.ops.service.CloudStorageService;
import com.saida.services.system.ops.service.DeviceRecordPlanService;
import com.saida.services.system.ops.service.StorageStrategyService;
import com.saida.services.system.sys.service.AttributeDetailService;
import com.saida.services.tools.attr.AttrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Service("cloudStorageService")
public class CloudStorageServiceImpl extends ServiceImpl<CloudStorageMapper, CloudStorageEntity> implements CloudStorageService {

    @Autowired
    private AttributeDetailService attributeDetailService;
    @Autowired(required = false)
    private RocketMQEnhanceTemplate rocketMQEnhanceTemplate;
    @Autowired
    private OSSUtil ossUtil;
    @Autowired
    private StorageStrategyService storageStrategyService;

    @Autowired(required = false)
    private S3Config s3Config;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdate(CloudStorageEntity entity) {
        if (entity.getType() == null) {
            throw new BizRuntimeException("服务类型必选");
        }
        if (OSSTypeEnum.getByTypeId(entity.getType()) == null) {
            throw new BizRuntimeException("服务类型错误");
        }
        if (StringUtil.isEmpty(entity.getRegion())) {
            throw new BizRuntimeException("区域必传");
        }
        if (entity.getId() == null) {// 新增
            entity.setCreateTime(DateTime.now());
            entity.setCreateUser(JwtUtil.getUserId());

            checkCloudStorage(entity);
            save(entity);
            addCloudStorageRule(entity);
            syncMq(entity.getId(), ActionEnum.EDIT);
        } else {// 更新
            entity.setUpdateTime(DateTime.now());
            entity.setUpdateUser(JwtUtil.getUserId());
            checkCloudStorage(entity);
            updateById(entity);
            addCloudStorageRule(entity);
            syncMq(entity.getId(), ActionEnum.EDIT);
        }
    }

    @Resource
    private ThreadPoolConfig threadPoolConfig;

    /**
     * 初始化存储策略
     * 每天1点也重写一次
     */
    @EventListener(ApplicationReadyEvent.class)
    @Scheduled(cron = "0 0 1 * * ? ")
    public void init() {
        threadPoolConfig.taskRunner(() -> {
            List<CloudStorageEntity> list = this.list();
            if (list != null && !list.isEmpty()) {
                list.forEach(this::addCloudStorageRule);
            }
        });
    }

    @Autowired
    private DefS3Impl defS3Impl;

    @Override
    public void addCloudStorageRule(CloudStorageEntity entity) {
        OSSBean build = OSSBean
                .builder()
                .accessKey(entity.getKeyId())
                .secretKey(entity.getSecret())
                .endPoint(entity.getEndPoint())
                .internalEndPoint(entity.getInternalEndPoint())
                .bucket(entity.getBucket())
                .region(entity.getRegion())
                .build();

        List<StorageStrategyEntity> list = storageStrategyService.list();
        List<S3Rule> s3Rules = new ArrayList<>();
        list.forEach(storageStrategyEntity -> {
            S3Rule r = new S3Rule();
            r.setPrefix(String.format("r/%s", storageStrategyEntity.getId()));
            r.setId(r.getPrefix());
            r.setExpirationInDays(storageStrategyEntity.getNumValue());
            s3Rules.add(r);
        });
        list.forEach(storageStrategyEntity -> {
            S3Rule r = new S3Rule();
            r.setPrefix(String.format("sdkAlarm/%s", storageStrategyEntity.getId()));
            r.setId(r.getPrefix());
            r.setExpirationInDays(storageStrategyEntity.getNumValue());
            s3Rules.add(r);
        });
        OSSMethodInterface ossMethodInterface = ossUtil.getOSSMethodInterface(OSSTypeEnum.getByTypeId(entity.getType()));
        ossMethodInterface.setBucketLifecycleConfiguration(build, s3Rules);
    }

    private void checkCloudStorage(CloudStorageEntity entity) {
        OSSBean build = OSSBean
                .builder()
                .accessKey(entity.getKeyId())
                .secretKey(entity.getSecret())
                .endPoint(entity.getEndPoint())
                .bucket(entity.getBucket())
                .region(entity.getRegion())
                .build();
        boolean exsit = false;
        try {
            exsit = defS3Impl.doesBucketExist(build);
        } catch (Exception e) {
            log.error("服务不可用...e.msg{}", e.getMessage(), e);
            throw new BizRuntimeException("服务不可用");
        }
        if (!exsit) {
            throw new BizRuntimeException("服务不可用");
        }
    }

    @Override
    public void syncMq(Long id, ActionEnum action) {
        CloudStorageEntity cloudStorage = getById(id);
        if (cloudStorage == null) {
            log.error("存储服务不存在");
            return;
        }
        String mq_topic = "cloud_storage_manager";
        SendResult sendRet = rocketMQEnhanceTemplate.send(mq_topic, new CloudStorageManagerMessage() {{
            setId(cloudStorage.getId());
            setAction(action.getAction());
            setName(cloudStorage.getName());
            OSSTypeEnum byTypeId = OSSTypeEnum.getByTypeId(cloudStorage.getType());
            if (byTypeId != null) {
                setType(String.valueOf(byTypeId.getTypeId()));
            }
            setEnd_point(cloudStorage.getEndPoint());
            setInternal_end_point(cloudStorage.getInternalEndPoint());
            setBucket(cloudStorage.getBucket());
            setKey_id(cloudStorage.getKeyId());
            setSecret(cloudStorage.getSecret());
            setRegion(cloudStorage.getRegion());
        }});
        log.info("同步云存服务结果：{}", sendRet);
    }

    @Override
    public IPage<CloudStorageEntity> listPage(CloudStorageEntity entity) {
        IPage<CloudStorageEntity> page = page(new Page<>(entity.getPageNum(), entity.getPageSize()),
                new LambdaQueryWrapper<CloudStorageEntity>()
                        .eq(entity.getType() != null, CloudStorageEntity::getType, entity.getType())
                        .like(!StringUtil.isEmpty(entity.getName()), CloudStorageEntity::getName, entity.getName())
                        .eq(entity.getStatus() != null, CloudStorageEntity::getStatus, entity.getStatus())
        );
        if (page == null || page.getRecords() == null || page.getRecords().isEmpty()) {
            return page;
        }
        fillAttr(page.getRecords());
        return page;
    }

    private List<CloudStorageEntity> fillAttr(List<CloudStorageEntity> records) {
        if (records == null || records.isEmpty()) {
            return records;
        }
        Map<Object, Object> dicMap = new HashMap<>();
        Map<Long, String> attrMap = attributeDetailService.getAllIdNameMap();
        if (attrMap != null && !attrMap.isEmpty()) {
            dicMap.putAll(attrMap);
        }
        records.replaceAll(o -> AttrUtil.putAttr(o, dicMap));
        return records;
    }

    @Override
    public List<CloudStorageEntity> getList(CloudStorageEntity entity) {
        List<CloudStorageEntity> records = list(new LambdaQueryWrapper<CloudStorageEntity>()
                .eq(entity.getType() != null, CloudStorageEntity::getType, entity.getType())
                .like(!StringUtil.isEmpty(entity.getName()), CloudStorageEntity::getName, entity.getName())
                .eq(entity.getStatus() != null, CloudStorageEntity::getStatus, entity.getStatus())
        );
        if (records == null || records.isEmpty()) {
            return records;
        }
        fillAttr(records);
        return records;
    }

    @Override
    public CloudStorageEntity getInfo(Long id) {
        CloudStorageEntity storage = getById(id);
        if (storage == null) {
            return null;
        }
        OSSBean build = OSSBean
                .builder()
                .accessKey(storage.getKeyId())
                .secretKey(storage.getSecret())
                .endPoint(storage.getEndPoint())
                .bucket(storage.getBucket())
                .region(storage.getRegion())
                .build();
        OSSMethodInterface ossMethodInterface = ossUtil.getOSSMethodInterface(OSSTypeEnum.getByTypeId(storage.getType()));
        storage.setNowCapacity(ossMethodInterface.getBucketNowSize(build));
        StorageStrategyEntity byId = storageStrategyService.getById(storage.getStorageStrategyId());
        if (byId != null) {
            storage.setStorageStrategyDays(byId.getNumValue());
        }
        return fillAttr(new ArrayList<CloudStorageEntity>() {{
            add(storage);
        }}).get(0);
    }


    @Resource
    private DeviceRecordPlanService deviceRecordPlanService;

    @Override
    public void delete(Long id) {
        long count = deviceRecordPlanService.count(new LambdaQueryWrapper<DeviceRecordPlanEntity>()
                .eq(DeviceRecordPlanEntity::getStrategyId, id));
        if (count > 0) {
            throw new BizRuntimeException("有设备正在使用该服务，无法删除");
        }
        syncMq(id, ActionEnum.DEL);
        removeById(id);
    }
}