package com.saida.services.system.rocketMq.alg;

import com.alibaba.fastjson.JSON;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.system.analyse.mapper.TaskDispatchGrpcStatusMapper;
import com.saida.services.system.analyse.pojo.entity.TaskDispatchGrpcStatusEntity;
import com.saida.services.system.rocketMq.message.GrpcTaskStatusMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.stream.Collectors;

/**
 * 视频流地址监听更新
 */
@Slf4j
@Component

public class GrpcTaskStatusMessageListener implements VLinkerMqMessageListener {


    @Resource
    private TaskDispatchGrpcStatusMapper taskDispatchGrpcStatusMapper;


    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("grpc_task_status")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        onMessage(messageBody);
    }


    public void onMessage(String message) {
        log.info("[mq:grpc_task_status]接收到消息：{}", message);
        GrpcTaskStatusMessage grpcTaskStatusMessage = JSON.parseObject(message, GrpcTaskStatusMessage.class);
        if (grpcTaskStatusMessage.getTaskIds() == null) {
            log.error("[mq:grpc_task_status] 没有任务id");
            return;
        }
        String collect = grpcTaskStatusMessage.getTaskIds().stream().map(String::valueOf).collect(Collectors.joining(","));

        TaskDispatchGrpcStatusEntity entity = new TaskDispatchGrpcStatusEntity();
        entity.setTaskIds(collect);
        entity.setTime(grpcTaskStatusMessage.getTime());
        entity.setNodeId(grpcTaskStatusMessage.getNodeId());
        entity.setCreateTime(LocalDateTime.now());
        taskDispatchGrpcStatusMapper.insert(entity);


    }
}

