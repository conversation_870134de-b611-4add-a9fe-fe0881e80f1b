package com.saida.services.system.callback;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.saida.services.algorithm.dto.PushAlarmDto;
import com.saida.services.algorithm.entity.AlgAlgorithmReviewConfigEntity;
import com.saida.services.algorithm.entity.ThirdPartyDeviceEntity;
import com.saida.services.algorithm.entity.ThirdPartyEntity;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.dto.AlarmNormalizationExt;
import com.saida.services.common.service.FileService;
import com.saida.services.common.tools.ImageCropperUtil;
import com.saida.services.common.tools.LocalUrlUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.constant.RedisConstants;
import com.saida.services.entities.pojo.FileModel;
import com.saida.services.system.alarm.entity.AlarmEntity;
import com.saida.services.system.alarm.entity.AlarmManageEntity;
import com.saida.services.system.alarm.mapper.AlarmManageMapper;
import com.saida.services.system.alarm.service.AlarmService;
import com.saida.services.system.algorithm.service.AlgorithmReviewConfigService;
import com.saida.services.system.basicData.mapper.BasicPeoplePhotoMapper;
import com.saida.services.system.basicData.service.BasicPeopleFaceFeatureService;
import com.saida.services.system.basicData.service.BasicPeopleInfoService;
import com.saida.services.system.basicData.vo.PeopleRelateVo;
import com.saida.services.system.face.dto.FaceDto;
import com.saida.services.system.face.dto.MatchResult;
import com.saida.services.system.peopleDeployControl.entity.AlarmPeopleControlCompareRecord;
import com.saida.services.system.peopleDeployControl.entity.DeployControlGroupRef;
import com.saida.services.system.peopleDeployControl.entity.PeopleIdentifyDeployControlEntity;
import com.saida.services.system.peopleDeployControl.mapper.AlarmPeopleControlCompareRecordMapper;
import com.saida.services.system.peopleDeployControl.mapper.DeployControlGroupRefMapper;
import com.saida.services.system.peopleDeployControl.service.PeopleIdentifyDeployControlService;
import com.saida.services.system.peopleDeployControl.vo.PeopleDeployControlFaceCompareVo;
import com.saida.services.system.sys.service.ThirdPartyDeviceService;
import com.saida.services.system.sys.service.ThirdPartyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CallBackInvoke {

    @Resource
    private ThirdPartyService thirdPartyService;
    @Resource
    private ThirdPartyDeviceService thirdPartyDeviceService;
    @Resource
    private CallBackFeign callBackFeign;
    @Resource
    private AlgorithmReviewConfigService algorithmReviewConfigService;
    @Resource
    private AlarmManageMapper alarmManageMapper;
    @Resource
    private AlarmService alarmService;
    @Resource
    private PeopleIdentifyDeployControlService peopleIdentifyDeployControlService;
    @Autowired
    private BasicPeopleFaceFeatureService basicPeopleFaceFeatureService;
    @Resource
    private DeployControlGroupRefMapper deployControlGroupRefMapper;
    @Resource
    private BasicPeopleInfoService basicPeopleInfoService;
    @Resource
    private AlarmPeopleControlCompareRecordMapper alarmPeopleControlCompareRecordMapper;
    @Autowired
    private BasicPeoplePhotoMapper basicPeoplePhotoMapper;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private FileService fileService;
    @Lazy
    @Resource
    private ThreadPoolTaskExecutor taskExecutor;
    //存放告警裁剪的图片url
    private static final ConcurrentHashMap<Integer, String> FACE_MAP = new ConcurrentHashMap<>(16);

    @Transactional(rollbackFor = Exception.class)
    public void callBack(AlarmEntity alarmEntity, CallBackMessage message, boolean enableReview, boolean saveAlarm) {
        alarmEntity.setPush(1);
        try {
            log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据，程序开始...message={}", JSON.toJSON(message));
            if (message == null || message.getDeviceCode() == null || message.getAlertType() == null) {
                return;
            }
            //人员布控
            List<PeopleIdentifyDeployControlEntity> deployControlList = peopleIdentifyDeployControlService.queryIsExist(alarmEntity);
            //存放人员布控涉及到的人员编号，推送第三方
            List<String> peopleNumberList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(deployControlList)) {
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                log.info("V-LINKER算法平台.接收第三方平台告警，开始处理人员布控数据...deployControlList={}", JSON.toJSON(deployControlList));
                dealAlarmByPeopleDeployControl(alarmEntity, deployControlList, peopleNumberList);
                stopWatch.stop();
                log.info("V-LINKER算法平台.接收第三方平台告警，人员布控数据处理完成，...耗时={}", stopWatch.getTotalTimeMillis());
            }
            if (enableReview) {
                // 判断是否进入审核流程，是否需要进行推送
                boolean needReview;
                AlgAlgorithmReviewConfigEntity algAlgorithmReviewConfigEntity = algorithmReviewConfigService.getAny(new LambdaQueryWrapper<>());
                if (Objects.nonNull(algAlgorithmReviewConfigEntity)) {
                    log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据，告警审核配置不为空，判断是否需要审核...algAlgorithmReviewConfigEntity={}", JSON.toJSON(algAlgorithmReviewConfigEntity));
                    needReview = this.needReview(message, algAlgorithmReviewConfigEntity);
                    if (needReview) {
                        log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据，符合审核规则，进入审核流程...algAlgorithmReviewConfigEntity={}", JSON.toJSON(algAlgorithmReviewConfigEntity));
                        AlarmManageEntity alarmManageEntity = new AlarmManageEntity();
                        alarmManageEntity.setAlarmId(alarmEntity.getId());
                        alarmManageEntity.setStatus(2);
                        alarmManageEntity.setCreateTime(new Date());
                        alarmManageEntity.setUpdateTime(new Date());
                        alarmManageMapper.insert(alarmManageEntity);
                        log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据，符合审核规则，保存审核数据...程序结束");

                        alarmEntity.setPush(0);
                        return;
                    }
                }
            }

            if (StringUtil.isEmpty(message.getMsgId())) {
                message.setMsgId(UUID.randomUUID().toString());
            }
            PushAlarmDto pushAlarmDto = this.getPushAlarmDto(message);
            pushAlarmDto.setMsgType(1);
            log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据...message={}, pushAlarmDto={}", JSON.toJSON(message), JSON.toJSON(pushAlarmDto));

            callBackFeign.feignPush(pushAlarmDto);

            List<ThirdPartyDeviceEntity> thirdPartyDeviceEntityList = thirdPartyDeviceService.list(new LambdaQueryWrapper<ThirdPartyDeviceEntity>()
                    .eq(ThirdPartyDeviceEntity::getCameraId, message.getDeviceId())
                    .like(ThirdPartyDeviceEntity::getSubscribe, message.getAlertType())
                    .groupBy(ThirdPartyDeviceEntity::getThirdId)
            );
            if (CollectionUtil.isEmpty(thirdPartyDeviceEntityList)) {
                log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据，第三方平台设备或告警订阅为空，程序结束...message={}", JSON.toJSON(message));
                return;
            }
            Set<Long> thirdIdSet = thirdPartyDeviceEntityList.stream().map(ThirdPartyDeviceEntity::getThirdId).collect(Collectors.toSet());

            List<ThirdPartyEntity> thirdPartyEntityList = thirdPartyService.list(new LambdaQueryWrapper<ThirdPartyEntity>()
                    .in(ThirdPartyEntity::getId, thirdIdSet)
                    .eq(ThirdPartyEntity::getStatus, 1)
                    .eq(ThirdPartyEntity::getFeignReq, 0)
                    .eq(ThirdPartyEntity::getInnerSystem, 0)
            );
            if (CollectionUtil.isEmpty(thirdPartyEntityList)) {
                log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据，第三方平台为空，程序结束...message={}, thirdIdSet={}", JSON.toJSON(message), thirdIdSet);
                return;
            }

            // 遍历
            for (ThirdPartyEntity thirdPartyEntity : thirdPartyEntityList) {
                try {
                    log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据...thirdPartyEntity={}", JSON.toJSON(thirdPartyEntity));
                    if (!LocalUrlUtil.validUrl(thirdPartyEntity.getPushUrl())) {
                        log.info("V-LINKER算法中台.向第三方推送告警..pushUrl不合法..程序continue...thirdPartyEntity={}", JSON.toJSON(thirdPartyEntity));
                        continue;
                    }
                    callBackFeign.httpPush(thirdPartyEntity, pushAlarmDto);
                } catch (Exception e) {
                    log.info("V-LINKER算法中台.向第三方推送告警，错误..程序continue...thirdPartyEntity={}, message={}, msg={}", JSON.toJSON(thirdPartyEntity), JSON.toJSON(message), e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据，错误....message={}, msg={}", JSON.toJSON(message), e.getMessage(), e);
        } finally {
            if (saveAlarm) {
                alarmService.save(alarmEntity);
            }
            log.info("V-LINKER算法平台.接收第三方平台告警..推送告警数据，推送完成，程序结束....message={}", JSON.toJSON(message));
        }
    }

    /**
     * 人脸识别告警对比布控记录生成人脸比对告警及比对记录
     * 每一条布控记录就生成最多一条人脸对比的告警数据，每个人脸对比告警数据关联最多n条人脸对比记录（n=告警图片提取的人脸数量）
     * 缓存人员告警数据，
     *
     * @param alarmEntity       告警实体
     * @param deployControlList 满足条件的布控记录
     * @param peopleNumberList  匹配上的人员编号
     */
    private boolean dealAlarmByPeopleDeployControl(AlarmEntity alarmEntity, List<PeopleIdentifyDeployControlEntity> deployControlList, List<String> peopleNumberList) {
        boolean result = false;
        String dateFormat = cn.hutool.core.date.DateUtil.format(new Date(), "yyyyMMdd");
        byte[] bytes = fileService.downloadFileAsByteArrayByCount(alarmEntity.getOriginalImageUrl(), 5);
        String base64AlarmPic = Base64.getEncoder().encodeToString(bytes);
        DtoResult<List<FaceDto>> saidaFaceFeaturesResult = basicPeopleFaceFeatureService.saidaFaceFeatures(null, base64AlarmPic);
        log.info("V-LINKER算法平台.人脸识别告警,源告警id:{},告警图片识别出人脸数据结果={}", alarmEntity.getId(), saidaFaceFeaturesResult.success());
        if (!saidaFaceFeaturesResult.success()) {
            return result;
        }
        List<FaceDto> faceDtos = saidaFaceFeaturesResult.getData();
        if (CollectionUtil.isEmpty(faceDtos)) {
            log.info("V-LINKER算法平台.人脸识别告警,源告警id:{},告警图片未识别出人脸数据，人脸布控识别程序结束", alarmEntity.getId());
            return result;
        }
        //裁剪人脸
//        faceDtos.forEach(faceDto -> {
//            tailorFace(alarmEntity, faceDto, base64AlarmPic, dateFormat);
//        });
        List<DeployControlGroupRef> controlGroupRefs = deployControlGroupRefMapper.selectList(new LambdaQueryWrapper<DeployControlGroupRef>()
                .in(DeployControlGroupRef::getDeployControlId, deployControlList.stream().map(PeopleIdentifyDeployControlEntity::getId).collect(Collectors.toList())));
        if (CollectionUtil.isEmpty(controlGroupRefs)) {
            log.info("V-LINKER算法平台.人脸识别告警,源告警id:{},布控数据未查询到布控分组关联数据，人脸布控识别程序结束", alarmEntity.getId());
            return result;
        }
        //初始化存放本次告警对比的人员列表
        Map<Long, List<PeopleIdentifyDeployControlEntity>> deployControlIdMap = deployControlList.stream().collect(Collectors.groupingBy(PeopleIdentifyDeployControlEntity::getId));
        //对比特征比对分组api，获取满足条件的图片列表
        Map<Long, List<DeployControlGroupRef>> groupIdsByControlIdMap = controlGroupRefs.stream().collect(Collectors.groupingBy(DeployControlGroupRef::getDeployControlId));
        for (Long deployControlId : groupIdsByControlIdMap.keySet()) {
            //每一条布控记录就生成一条对比告警数据
            PeopleIdentifyDeployControlEntity peopleIdentifyDeployControl = deployControlIdMap.get(deployControlId).get(0);
            switch (peopleIdentifyDeployControl.getSamePersonAlarmIntervalUnit()) {
                case 2:
                    peopleIdentifyDeployControl.setSamePersonAlarmInterval(peopleIdentifyDeployControl.getSamePersonAlarmInterval() * 60);
                    break;
                case 3:
                    peopleIdentifyDeployControl.setSamePersonAlarmInterval(peopleIdentifyDeployControl.getSamePersonAlarmInterval() * 60 * 60);
                    break;
                case 4:
                    peopleIdentifyDeployControl.setSamePersonAlarmInterval(peopleIdentifyDeployControl.getSamePersonAlarmInterval() * 60 * 60 * 24);
                    break;
                default:
                    break;
            }
            //多缓存些时间作为特殊情况的数据补偿
            long redisExpire = peopleIdentifyDeployControl.getSamePersonAlarmInterval() > 86400 ? peopleIdentifyDeployControl.getSamePersonAlarmInterval() : 86400;
            List<Long> groupIds = groupIdsByControlIdMap.get(deployControlId).stream().map(DeployControlGroupRef::getGroupId).collect(Collectors.toList());
            //每个布控任务初始化一个集合存放匹配的人员信息
            List<PeopleDeployControlFaceCompareVo> controlFaceCompareVos = new ArrayList<>();

            // 并行处理所有人脸
//            CountDownLatch latch = new CountDownLatch(faceDtos.size());
            faceDtos.forEach(faceDto -> {
//                taskExecutor.execute(() -> {
//                    try {
                DtoResult<MatchResult[]> dtoResult = basicPeopleFaceFeatureService.faceComparisons(groupIds.toArray(new Long[0]), faceDto, 100000 * groupIds.size());
                if (!dtoResult.success() || dtoResult.getData().length == 0) {
                    return;
                }
                List<PeopleDeployControlFaceCompareVo> faceCompareVos = Arrays.stream(dtoResult.getData())
                        .map(match -> PeopleDeployControlFaceCompareVo.builder()
                                .peopleId(match.faceDto.getPeopleId())
                                .faceId(match.faceDto.getFaceId())
                                .similarity(match.getSimilarity())
                                .faceDto(faceDto)
                                .build())
                        .filter(vo -> vo.getSimilarity() >= Double.parseDouble(peopleIdentifyDeployControl.getFaceSimilarity()))
                        .sorted(Comparator.comparing(PeopleDeployControlFaceCompareVo::getSimilarity).reversed())
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(faceCompareVos)) {
                    //整合一下这个sql
                    PeopleRelateVo peopleRelateVo = basicPeopleInfoService.selectPeopleRelateInfo(faceCompareVos.get(0).getPeopleId(),faceCompareVos.get(0).getFaceId());
                    if(ObjectUtil.isNotNull(peopleRelateVo)){
                        faceCompareVos.get(0).setAppId(peopleRelateVo.getAppId());
                        faceCompareVos.get(0).setName(peopleRelateVo.getName());
                        faceCompareVos.get(0).setSex(peopleRelateVo.getSex());
                        faceCompareVos.get(0).setPeopleId(peopleRelateVo.getId());
                        faceCompareVos.get(0).setPeopleNumber(peopleRelateVo.getNumber());
                        faceCompareVos.get(0).setPhotoUrl(peopleRelateVo.getPhotoUrl());
                    }
//                    synchronized (controlFaceCompareVos) {
                        controlFaceCompareVos.add(faceCompareVos.get(0));
//                    }
                }
//                    } finally {
//                        // 任务完成后计数减一
//                        latch.countDown();
//                    }
//                });
            });
//            try {
//                // 等待所有任务完成
//                latch.await();
//            } catch (InterruptedException e) {
//                log.error("V-LINKER算法平台.人脸识别告警..人脸匹配异常...msg={}", e.getMessage(), e);
//            }

            if (CollectionUtil.isEmpty(controlFaceCompareVos)) {
                log.info("V-LINKER算法平台.人脸识别告警,源告警id:{},当前布控布控数据未查询到满足条件的数据,布控任务id:{}", alarmEntity.getId(), peopleIdentifyDeployControl.getId());
                continue;
            }
            //过滤掉布控配置-同人间隔之内的数据
            List<PeopleDeployControlFaceCompareVo> filterFaceCompareVos = controlFaceCompareVos.stream()
                    .filter(vo -> {
                        Object redisAlarmTimeStr = redisTemplate.opsForValue().get(RedisConstants.CONV_PEOPLE_DEPLOY_CONTROL_SAME_PERSON_ALARM_INTERVAL + vo.getPeopleNumber());
                        if (ObjectUtil.isNotNull(redisAlarmTimeStr)) {
                            //不为空时校验缓存里的告警时间和本次告警时间差是否在同人间隔之内,如果在就过滤掉
                            LocalDateTime redisAlarmTime = LocalDateTimeUtil.parse(redisAlarmTimeStr.toString(), "yyyy-MM-dd HH:mm:ss");
                            LocalDateTime alarmTime = LocalDateTimeUtil.parse(alarmEntity.getAlarmTime(), "yyyy-MM-dd HH:mm:ss");
                            //判断时间间隔是否在同人间隔之内
                            return !alarmTime.isAfter(redisAlarmTime) || !alarmTime.isBefore(redisAlarmTime.plusSeconds(peopleIdentifyDeployControl.getSamePersonAlarmInterval()));
                        }
                        return true;
                    })
                    .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(filterFaceCompareVos)) {
                //所有人脸在缓存中都未失效，继续处理下一个布控配置
                log.info("V-LINKER算法平台.人脸识别告警,源告警id:{},当前布控任务匹配的人脸数据均在同人告警时间间隔内,布控任务id:{}", alarmEntity.getId(), peopleIdentifyDeployControl.getId());
                continue;
            }
            //所有人脸最高相似度的数据汇总结果关联告警数据入库
            AlarmEntity alarmEntityByDeployControl = new AlarmEntity();
            BeanUtil.copyProperties(alarmEntity, alarmEntityByDeployControl);
            alarmEntityByDeployControl.setId(null);
            alarmEntityByDeployControl.setPeopleDeployId(deployControlId);
            alarmService.save(alarmEntityByDeployControl);
            filterFaceCompareVos.forEach(vo -> {
                if (FACE_MAP.containsKey(vo.getFaceDto().hashCode())) {
                    vo.getFaceDto().setPhotoUrl(FACE_MAP.get(vo.getFaceDto().hashCode()));
                } else {
                    tailorFace(alarmEntity, vo.getFaceDto(), base64AlarmPic, dateFormat);
                }
                peopleNumberList.add(vo.getPeopleNumber());
                alarmPeopleControlCompareRecordMapper.insert(AlarmPeopleControlCompareRecord.builder()
                        .alarmId(alarmEntityByDeployControl.getId())
                        .appId(vo.getAppId())
                        .peopleId(vo.getPeopleId())
                        .name(vo.getName())
                        .sex(vo.getSex())
                        .peoplePhotoId(vo.getFaceId())
                        .captureImage(vo.getFaceDto().getPhotoUrl())
                        .matchPeopleImage(vo.getPhotoUrl())
                        .similarity(String.valueOf(vo.getSimilarity()))
                        .build());
                redisTemplate.opsForValue().set(RedisConstants.CONV_PEOPLE_DEPLOY_CONTROL_SAME_PERSON_ALARM_INTERVAL + vo.getPeopleNumber(), alarmEntity.getAlarmTime(), redisExpire, TimeUnit.SECONDS);
            });
            log.info("V-LINKER算法平台.人脸识别告警,源告警id:{},当前布控任务匹配的人脸数据处理完成,布控任务id:{},告警id:{},关联人脸比对记录数量:{}", alarmEntity.getId(), peopleIdentifyDeployControl.getId(), alarmEntityByDeployControl.getId(), filterFaceCompareVos.size());
        }
        log.info("V-LINKER算法平台.接收第三方平台告警,源告警id:{},人员布控数据处理完成,本次比对采集的人员编号列表为,peopleNumberList={}", alarmEntity.getId(), JSON.toJSON(peopleNumberList));
        if (!CollectionUtils.isEmpty(peopleNumberList)) {
            AlarmNormalizationExt alarmNormalizationExt = JSON.parseObject(alarmEntity.getNormalizationExt(), AlarmNormalizationExt.class);
            peopleNumberList.stream().forEach(peopleNumber -> {
                alarmNormalizationExt.getPeopleInfoDto().add(AlarmNormalizationExt.PeopleInfoDto.builder().peopleNumber(peopleNumber).build());
            });
            alarmEntity.setNormalizationExt(JSON.toJSONString(alarmNormalizationExt));
        }
        //FACE_MAP清空
        FACE_MAP.clear();
        return result;
    }

    /**
     * 裁剪人脸图片
     *
     * @param alarmEntity
     * @param faceDto
     * @param base64AlarmPic
     * @param dateFormat
     */
    private void tailorFace(AlarmEntity alarmEntity, FaceDto faceDto, String base64AlarmPic, String dateFormat) {
        try {
            String base64Crop = ImageCropperUtil.cropImage(base64AlarmPic, faceDto.getX(), faceDto.getY(), faceDto.getW(), faceDto.getH());
            String ossObjKey = "alarm/peopleDeployControl/" + alarmEntity.getDeviceId() + "/" + dateFormat + "/" + IdWorker.getId() + ".jpg";
            DtoResult<FileModel> uploadResult = fileService.uploadByte(Base64.getDecoder().decode(base64Crop), ossObjKey);
            log.info("V-LINKER算法平台.人脸识别告警.源告警id:{},裁剪人脸图片成功，上传图片到s3结果，uploadResult={}", alarmEntity.getId(), uploadResult.success());
            if (uploadResult.success()) {
                faceDto.setPhotoUrl(uploadResult.getData().getUrl());
                //成功后存内存中
                FACE_MAP.put(faceDto.hashCode(), faceDto.getPhotoUrl());
            }
        } catch (Exception e) {
            log.error("V-LINKER算法平台.人脸识别告警.源告警id:{},裁剪人脸图片失败，message={}", alarmEntity.getId(), e.getMessage(), e);
        }
    }

    private List<PeopleDeployControlFaceCompareVo> filterSamePersonInterval(List<PeopleDeployControlFaceCompareVo> controlFaceCompareVos) {
        return controlFaceCompareVos.stream()
                .filter(vo -> redisTemplate.opsForHash().hasKey(RedisConstants.CONV_PEOPLE_DEPLOY_CONTROL_SAME_PERSON_ALARM_INTERVAL, vo.getPeopleNumber()))
                .collect(Collectors.toList());
    }

    /**
     * 只是处理告警图片推送
     */
    public void callBackOnlyImg(CallBackMessage message) {
        log.info("V-LINKER算法平台.接收第三方平台图片告警..向第三方平台推送图片告警数据，程序开始...message={}", JSON.toJSON(message));
        if (message == null || message.getAlertType() == null) {
            return;
        }
        if (StringUtil.isEmpty(message.getMsgId())) {
            return;
        }
        PushAlarmDto pushAlarmDto = this.getPushAlarmDto(message);
        pushAlarmDto.setMsgType(2);
        log.info("V-LINKER算法平台.向第三方推送告警图片..推送告警数据...message={}, pushAlarmDto={}", JSON.toJSON(message), JSON.toJSON(pushAlarmDto));
        // 1.内部推送
        callBackFeign.feignPush(pushAlarmDto);

        List<ThirdPartyDeviceEntity> thirdPartyDeviceEntityList = thirdPartyDeviceService.list(new LambdaQueryWrapper<ThirdPartyDeviceEntity>()
                .eq(ThirdPartyDeviceEntity::getCameraId, message.getDeviceId())
                .like(ThirdPartyDeviceEntity::getSubscribe, message.getAlertType())
                .groupBy(ThirdPartyDeviceEntity::getThirdId)
        );
        if (CollectionUtil.isEmpty(thirdPartyDeviceEntityList)) {
            log.info("V-LINKER算法平台.接收第三方平台图片告警..向第三方平台推送图片告警数据，第三方设备为空，程序结束...message={}", JSON.toJSON(message));
            return;
        }
        Set<Long> thirdIdSet = thirdPartyDeviceEntityList.stream().map(ThirdPartyDeviceEntity::getThirdId).collect(Collectors.toSet());

        List<ThirdPartyEntity> thirdPartyEntityList = thirdPartyService.list(new LambdaQueryWrapper<ThirdPartyEntity>()
                .in(ThirdPartyEntity::getId, thirdIdSet)
                .eq(ThirdPartyEntity::getStatus, 1)
                .eq(ThirdPartyEntity::getFeignReq, 0)
                .eq(ThirdPartyEntity::getInnerSystem, 0)
        );

        if (CollectionUtil.isNotEmpty(thirdPartyEntityList)) {
            // 2.外部推送
            for (ThirdPartyEntity thirdPartyEntity : thirdPartyEntityList) {
                try {
                    log.info("V-LINKER算法平台.向第三方推送告警图片..推送告警数据...thirdPartyEntity={}", JSON.toJSON(thirdPartyEntity));
                    if (!LocalUrlUtil.validUrl(thirdPartyEntity.getPushUrl())) {
                        log.info("V-LINKER算法中台.向第三方推送告警图片..pushUrl不合法，程序continue...thirdPartyEntity={}", JSON.toJSON(thirdPartyEntity));
                        continue;
                    }
                    PushAlarmDto dto = new PushAlarmDto();
                    dto.setMsgType(2);
                    dto.setMsgReqNo(message.getMsgId());
                    dto.setSrcUrl(message.getSrcUrl());
                    log.info("V-LINKER算法中台.向第三方推送告警图片...thirdPartyEntity={}, data={}", JSON.toJSON(thirdPartyEntity), JSON.toJSON(dto));
                    callBackFeign.httpPush(thirdPartyEntity, dto);
                } catch (Exception e) {
                    log.info("V-LINKER算法中台.向第三方推送告警图片，错误，程序continue...msg={}", e.getMessage(), e);
                }
            }
        }
    }

    private PushAlarmDto getPushAlarmDto(CallBackMessage message) {
        PushAlarmDto pushAlarmDto = new PushAlarmDto();
        pushAlarmDto.setMsgReqNo(message.getMsgId());
        pushAlarmDto.setDeviceCode(message.getDeviceCode());
        pushAlarmDto.setChannelId(message.getChannelId());
        pushAlarmDto.setAlertType(message.getAlertType());
        pushAlarmDto.setAlertTypeName(message.getAlertTypeName());
        pushAlarmDto.setAlertTime(message.getCreateTime());
        pushAlarmDto.setAlertSource(message.getAlertSource());
        pushAlarmDto.setAlertSourceName(message.getAlertSourceName());
        pushAlarmDto.setOriginalSrcUrl(message.getOriginalSrcUrl());
        pushAlarmDto.setSrcUrl(message.getSrcUrl());
        pushAlarmDto.setMetadata(message.getExt());
        pushAlarmDto.setNormalizationExt(message.getNormalizationExt());
        pushAlarmDto.setOriginalAlarmStr(message.getOriginalAlarmStr());
        return pushAlarmDto;
    }

    private boolean needReview(CallBackMessage message, AlgAlgorithmReviewConfigEntity algAlgorithmReviewConfigEntity) {
        Long algId = message.getAlgId();
        String deviceId = message.getDeviceId();

        Integer enable = algAlgorithmReviewConfigEntity.getEnable();
        Integer allAlgorithm = algAlgorithmReviewConfigEntity.getAllAlgorithm();
        Integer allDevice = algAlgorithmReviewConfigEntity.getAllDevice();
        Set<String> algorithmList = CollectionUtil.isEmpty(algAlgorithmReviewConfigEntity.getAlgorithmList()) ? new HashSet<>() : algAlgorithmReviewConfigEntity.getAlgorithmList();
        Set<String> deviceList = CollectionUtil.isEmpty(algAlgorithmReviewConfigEntity.getDeviceList()) ? new HashSet<>() : algAlgorithmReviewConfigEntity.getDeviceList();

        if (!Objects.equals(enable, 1)) {
            return false;
        }
        if (Objects.equals(allAlgorithm, 1) && Objects.equals(allDevice, 1)) {
            return true;
        } else if (Objects.equals(allAlgorithm, 1) && Objects.equals(allDevice, 0)) {
            return CollectionUtil.isNotEmpty(deviceList) && deviceList.contains(deviceId);
        } else if (Objects.equals(allAlgorithm, 0) && Objects.equals(allDevice, 1)) {
            return CollectionUtil.isNotEmpty(algorithmList) && algorithmList.contains(String.valueOf(algId));
        } else if (Objects.equals(allAlgorithm, 0) && Objects.equals(allDevice, 0)) {
            return (CollectionUtil.isNotEmpty(algorithmList) && algorithmList.contains(String.valueOf(algId))) && (CollectionUtil.isNotEmpty(deviceList) && deviceList.contains(deviceId));
        }
        return false;
    }
}