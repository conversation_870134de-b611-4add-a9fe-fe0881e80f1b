package com.saida.services.system.ops.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.entity.BaseEntity;
import com.saida.services.common.mq.message.SharePlatformMessage;
import com.saida.services.common.mq.message.VirtualOrganTreeMessage;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.*;
import com.saida.services.converge.entity.dto.OnlineStatusCalculator;
import com.saida.services.converge.entity.dto.VirtualOrganTreeDto;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.enums.SharePlatformMessageType;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.system.ops.dto.*;
import com.saida.services.system.ops.mapper.VirtualOrganDeviceRelativeMapper;
import com.saida.services.system.ops.mapper.VirtualOrganTreeMapper;
import com.saida.services.system.ops.service.*;
import com.saida.services.system.rocketMq.nodev1.VirtualOrganTreeProduce;
import com.saida.services.tools.excel.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 虚拟设备树
 */
@Slf4j
@Service("virtualOrganTreeService")
@Transactional(rollbackFor = Exception.class)
public class VirtualOrganTreeServiceImpl extends ServiceImpl<VirtualOrganTreeMapper, VirtualOrganTreeEntity> implements VirtualOrganTreeService {

    @Resource
    private VirtualOrganDeviceRelativeMapper virtualOrganDeviceRelativeMapper;

    @Resource
    private VirtualOrganTreeProduce virtualOrganTreeProduce;

    @Resource
    private VirtualOrganTreeService virtualOrganTreeService;

    @Resource
    private VirtualOrganDeviceRelativeService virtualOrganDeviceRelativeService;

    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;

    @Resource
    private OpsPlatformService opsPlatformService;
    @Resource
    private RedisUtil redisUtil;

    /**
     * 获取虚拟组织树列表
     *
     * @param entity 虚拟组织树实体，用于过滤和构建树结构
     * @return Result对象，包含构建好的虚拟组织树列表
     */
    @Override
    public Result getVirtualOrganTreeList(VirtualOrganTreeEntity entity) {
        VirtualOrganTreeEntity byId = super.getById(entity.getId());
        if (byId == null) {
            return Result.error("请选择一个组织");
        }
        entity.setIdChain(byId.getIdChain());
        // 从数据库获取满足条件的虚拟组织树列表
        List<VirtualOrganTreeDto> list = super.baseMapper.getTreeListByOnline(entity);
        OnlineStatusCalculator onlineStatusCalculator = new OnlineStatusCalculator();
        list.forEach(onlineStatusCalculator::addVirtualOrganTreeDto);
        onlineStatusCalculator.buildTreeRelations();
        onlineStatusCalculator.calculateOnlineStatusFromLeaves(entity.getId());
        // 返回构建好的虚拟组织树列表
        return Result.ok(onlineStatusCalculator.getVirtualOrganTreeDtoMap().get(entity.getId()));
    }

    @Override
    public Result info(VirtualOrganTreeEntity entity) {
        if (entity.getId() == null) {
            return Result.error("id不可为空");
        }
        VirtualOrganTreeEntity visualDeviceTreeEntity = this.getById(entity.getId());
        if (null == visualDeviceTreeEntity) {
            return Result.error("数据不存在，请检查后重试！");
        }
        VirtualOrganTreeEntity organTreeEntity = this.getById(visualDeviceTreeEntity.getParentId());
        if (null != organTreeEntity) {
            visualDeviceTreeEntity.setParentName(organTreeEntity.getName());
        }
        return Result.ok(visualDeviceTreeEntity);
    }

    @Override
    public Result add(VirtualOrganTreeEntity entity) {
        if (entity == null) {
            return Result.error("新增数据不可为空！");
        }
        if (entity.getOrganCode() == null) {
            return Result.error("组织编码不可为空！");
        }
        VirtualOrganTreeEntity infoByOrganCode = baseMapper.getInfoByOrganCode(entity);
        if (infoByOrganCode != null && infoByOrganCode.getId() != null) {
            return Result.error("编码已存在，请重新输入");
        }

        Long id = IdWorker.getId();
        entity.setId(id);
        entity.setIdChain(id.toString());
        entity.setParentId(0L);
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateBy(JwtUtil.getUserId());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateBy(JwtUtil.getUserId());
        this.save(entity);


        //同步信息
        VirtualOrganTreeMessage virtualOrganTreeMessage = new VirtualOrganTreeMessage();
        virtualOrganTreeMessage.setMessageType(1);
        VirtualOrganTreeMqDto virtualOrganTreeDto = new VirtualOrganTreeMqDto();
        virtualOrganTreeDto.setId(String.valueOf(entity.getId()));
        virtualOrganTreeDto.setName(entity.getName());
        virtualOrganTreeDto.setOrganCode(entity.getOrganCode());
        virtualOrganTreeDto.setParentId(String.valueOf(entity.getParentId()));
        virtualOrganTreeDto.setIdChain(entity.getIdChain());
        virtualOrganTreeDto.setSort(String.valueOf(entity.getSort() == null ? 0 : entity.getSort()));
        virtualOrganTreeMessage.setData(virtualOrganTreeDto);
        virtualOrganTreeProduce.sync(virtualOrganTreeMessage);
        return Result.ok("新增成功");
    }

    @Override
    public List<VirtualOrganTreeEntity> getList(VirtualOrganTreeEntity entity) {
        return getBaseMapper().getList(entity);
    }

    @Override
    public Result addChild(VirtualOrganTreeEntity entity) {
        if (entity == null) {
            return Result.error("新增数据不可为空！");
        }
        if (null == entity.getParentId()) {
            return Result.error("请选择一个父级节点！");
        }
        if (entity.getOrganCode() == null) {
            return Result.error("组织编码不可为空！");
        }
        VirtualOrganTreeEntity virtualOrganTree = this.getById(entity.getParentId());
        if (null == virtualOrganTree) {
            return Result.error("父级节点不存在，请检查后重试！");
        }
        VirtualOrganTreeEntity infoByOrganCode = baseMapper.getInfoByOrganCode(entity);
        if (infoByOrganCode != null && infoByOrganCode.getId() != null) {
            return Result.error("编码已存在，请重新输入");
        }
        Long id = IdWorker.getId();
        entity.setId(id);
        entity.setIdChain(virtualOrganTree.getIdChain() + "," + id);
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateBy(JwtUtil.getUserId());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateBy(JwtUtil.getUserId());
        this.save(entity);

        //同步信息
        VirtualOrganTreeMessage virtualOrganTreeMessage = new VirtualOrganTreeMessage();
        virtualOrganTreeMessage.setMessageType(1);
        VirtualOrganTreeMqDto virtualOrganTreeDto = new VirtualOrganTreeMqDto();
        virtualOrganTreeDto.setId(String.valueOf(entity.getId()));
        virtualOrganTreeDto.setName(entity.getName());
        virtualOrganTreeDto.setOrganCode(entity.getOrganCode());
        virtualOrganTreeDto.setParentId(String.valueOf(entity.getParentId()));
        virtualOrganTreeDto.setIdChain(entity.getIdChain());
        virtualOrganTreeDto.setSort(String.valueOf(entity.getSort() == null ? 0 : entity.getSort()));

        String topId = entity.getIdChain().split(",")[0];
        OpsPlatformEntity opsPlatformEntity = opsPlatformService.getOne(new LambdaQueryWrapper<OpsPlatformEntity>()
                .eq(OpsPlatformEntity::getVirtualOrgId, topId));
        if (opsPlatformEntity != null) {
            virtualOrganTreeDto.setSipId(opsPlatformEntity.getSipId());
        }
        virtualOrganTreeMessage.setData(virtualOrganTreeDto);
        virtualOrganTreeProduce.sync(virtualOrganTreeMessage);
        return Result.ok("新增子节点成功");
    }

    @Override
    public Result edit(VirtualOrganTreeEntity entity) {
        if (entity == null || entity.getId() == null) {
            return Result.error("id不可为空");
        }

        VirtualOrganTreeEntity treeEntity = this.getById(entity.getId());
        if (null == treeEntity) {
            return Result.error("数据不存在，请检查后重试！");
        }
        VirtualOrganTreeEntity infoByOrganCode = baseMapper.getInfoByOrganCode(entity);
        if (infoByOrganCode != null && infoByOrganCode.getId() != null && !entity.getId().equals(infoByOrganCode.getId())) {
            return Result.error("编码已存在，请重新输入");
        }
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateBy(JwtUtil.getUserId());
        this.updateById(entity);

        //同步信息
        VirtualOrganTreeMessage virtualOrganTreeMessage = new VirtualOrganTreeMessage();
        virtualOrganTreeMessage.setMessageType(1);
        VirtualOrganTreeMqDto virtualOrganTreeDto = new VirtualOrganTreeMqDto();
        virtualOrganTreeDto.setId(String.valueOf(treeEntity.getId()));
        virtualOrganTreeDto.setName(entity.getName());
        virtualOrganTreeDto.setOrganCode(entity.getOrganCode());
        virtualOrganTreeDto.setParentId(String.valueOf(treeEntity.getParentId()));
        virtualOrganTreeDto.setIdChain(treeEntity.getIdChain());
        virtualOrganTreeDto.setSort(String.valueOf(treeEntity.getSort() == null ? 0 : treeEntity.getSort()));
        String topId = treeEntity.getIdChain().split(",")[0];
        OpsPlatformEntity opsPlatformEntity = opsPlatformService.getOne(new LambdaQueryWrapper<OpsPlatformEntity>()
                .eq(OpsPlatformEntity::getVirtualOrgId, topId));
        if (opsPlatformEntity != null) {
            virtualOrganTreeDto.setSipId(opsPlatformEntity.getSipId());
        }
        virtualOrganTreeMessage.setData(virtualOrganTreeDto);
        virtualOrganTreeProduce.sync(virtualOrganTreeMessage);
        return Result.ok("修改成功");
    }

    @Override
    public Result delete(VirtualOrganTreeEntity entity) {
        if (null == entity.getId()) {
            return Result.error("id不能为空，请检查后重试！");
        }
        VirtualOrganTreeEntity visualDeviceTreeEntity = this.getById(entity.getId());
        String idChain = visualDeviceTreeEntity.getIdChain();
        // 查询当前目录及其所有下级目录
        List<VirtualOrganTreeEntity> virtualOrganTreeEntityList = this.list(new LambdaQueryWrapper<VirtualOrganTreeEntity>()
                .likeRight(VirtualOrganTreeEntity::getIdChain, idChain));
        Set<Long> deviceTreeIdSet = virtualOrganTreeEntityList.stream().map(VirtualOrganTreeEntity::getId).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(deviceTreeIdSet)) {
            // 查询当前目录及其所有下级目录关联的设备
            Integer num = virtualOrganDeviceRelativeMapper.getByVirtualOrganIds(deviceTreeIdSet);
            if (num > 0) {
                return Result.error("组织节点下存在设备，无法删除，请重新挂载设备后再删除目录！");
            }
            this.removeByIds(deviceTreeIdSet);
        }
        //同步删除
        List<String> idList = new ArrayList<>();
        deviceTreeIdSet.forEach(id -> {
            idList.add(id.toString());
        });

        // 查询是否绑定平台
        OpsPlatformEntity platform = opsPlatformService.findByVirtualOrgId(Long.valueOf(idChain.split(",")[0]));

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("ids", idList);
        if (Objects.nonNull(platform)) {
            dataMap.put("sip_id", platform.getSipId());
        }
        VirtualOrganTreeMessage virtualOrganTreeMessage = new VirtualOrganTreeMessage();
        virtualOrganTreeMessage.setMessageType(2);
        virtualOrganTreeMessage.setData(dataMap);

        virtualOrganTreeProduce.sync(virtualOrganTreeMessage);
        return Result.ok("删除成功");
    }

    @Override
    public Result deleteByIds(BaseRequest baseRequest) {
        if (StringUtils.isBlank(baseRequest.getIds())) {
            return Result.error("id不可为空");
        }
        if (StringUtils.isNotBlank(baseRequest.getIds())) {
            String[] parentIds = baseRequest.getIds().split(",");
            for (String treeId : parentIds) {
                VirtualOrganTreeEntity visualDeviceTreeEntity = this.getById(treeId);
                if (visualDeviceTreeEntity == null) {
                    continue;
                }
                String idChain = visualDeviceTreeEntity.getIdChain();
                // 查询当前目录及其所有下级目录
                List<VirtualOrganTreeEntity> virtualOrganTreeEntityList = this.list(new LambdaQueryWrapper<VirtualOrganTreeEntity>()
                        .likeRight(VirtualOrganTreeEntity::getIdChain, idChain));
                Set<Long> deviceTreeIdSet = virtualOrganTreeEntityList.stream().map(VirtualOrganTreeEntity::getId).collect(Collectors.toSet());
                if (CollectionUtil.isNotEmpty(deviceTreeIdSet)) {
                    // 查询当前目录及其所有下级目录关联的设备
                    Integer num = virtualOrganDeviceRelativeMapper.getByVirtualOrganIds(deviceTreeIdSet);
                    if (num > 0) {
                        return Result.error("[" + visualDeviceTreeEntity.getName() + "]组织节点下存在设备，无法删除，请重新挂载设备后再删除目录！");
                    }
                    this.removeByIds(deviceTreeIdSet);
                }
                //同步删除
                List<String> idList = new ArrayList<>();
                deviceTreeIdSet.forEach(id -> {
                    idList.add(id.toString());
                });
                // 查询是否绑定平台
                OpsPlatformEntity platform = opsPlatformService.findByVirtualOrgId(Long.valueOf(idChain.split(",")[0]));

                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("ids", idList);
                if (Objects.nonNull(platform)) {
                    dataMap.put("sip_id", platform.getSipId());
                }
                VirtualOrganTreeMessage virtualOrganTreeMessage = new VirtualOrganTreeMessage();
                virtualOrganTreeMessage.setMessageType(2);
                virtualOrganTreeMessage.setData(dataMap);
                virtualOrganTreeProduce.sync(virtualOrganTreeMessage);
            }
        }
        return Result.ok("删除成功");
    }

    @Override
    public Result getChildList(VirtualOrganTreeEntity entity) {
        IPage<VirtualOrganTreeEntity> childList = baseMapper.getChildList(new Page<>(entity.getPageNum(), entity.getPageSize()), entity);
        return Result.ok(childList);
    }

    @Override
    public void exportChildList(VirtualOrganTreeEntity entity, HttpServletResponse response) throws Exception {
        List<VirtualOrganTreeEntity> treeList = baseMapper.getChild(entity);
        List<VirtualOrgExport> exportList = treeList.stream().map(it -> {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return VirtualOrgExport.builder()
                    .orgCode(it.getOrganCode())
                    .nodeName(it.getName())
                    .createTime(it.getCreateTime().format(formatter))
                    .updateTime(it.getUpdateTime().format(formatter))
                    .deviceNum(Optional.ofNullable(it.getOnlineDeviceNum()).orElse(0) + "/" + Optional.ofNullable(it.getDeviceTotalNum()).orElse(0))
                    .build();
        }).collect(Collectors.toList());

        EasyExcelUtils.webWriteExcel(response, exportList,
                VirtualOrgExport.class, "组织导出清单", "组织清单");
    }

    @Override
    public DtoResult<Void> share(OrgShareDto dto) {
        // 查询虚拟组织
        List<VirtualOrganTreeEntity> orgList = getBaseMapper().selectBatchIds(dto.getIdList());
        if (CollectionUtil.isEmpty(orgList)) {
            return DtoResult.error("组织不存在");
        }

        // 查询平台信息
        OpsPlatformEntity platform = opsPlatformService.getById(dto.getPlatformId());
        if (Objects.isNull(platform)) {
            return DtoResult.error("平台不存在");
        }
        getBaseMapper().updateShareStatusByIds(dto.getIdList(), dto);

        // 资源共享
        sharePlatform(platform, dto.getIdList(), SharePlatformMessageType.ORG_SHARE.getCode(), dto.getShareStatus());
        return DtoResult.ok();
    }

    @Override
    public DtoResult<Void> deviceShare(DeviceShareDto dto) {
//        ListPageByVirtualParam param = new ListPageByVirtualParam();
//        param.setRelativeIds(dto.getIdList());
//        param.setSubBind(1);
//        List<ListPageByVirtualDto> deviceList = virtualOrganDeviceRelativeService.listPageByVirtual(param);

        List<ListPageByVirtualDto> deviceList = virtualOrganDeviceRelativeService.findByRelativeIds(dto.getIdList());
        if (CollectionUtil.isEmpty(deviceList)) {
            return DtoResult.ok();
        }

        // 更新设备信息
        virtualOrganDeviceRelativeService.updateShareInfo(dto);

        // 查询虚拟组织关联平台
        OpsPlatformEntity platform = opsPlatformService.getById(dto.getPlatformId());
        if (Objects.isNull(platform)) {
            return DtoResult.ok();
        }

        int shareType = 1;
        if (dto.getVideoShare() == 0 && dto.getPtzShare() == 0 && dto.getVcrShare() == 0) {
            shareType = 0;
        }
        deviceShare(platform, deviceList, dto, shareType);
        return DtoResult.ok();
    }

    @Override
    public List<VirtualOrganTreeEntity> getChildShareListByOrgId(Long virtualOrgId) {
        LambdaQueryWrapper<VirtualOrganTreeEntity> query = Wrappers.lambdaQuery();
        query.likeLeft(VirtualOrganTreeEntity::getIdChain, virtualOrgId)
                .eq(VirtualOrganTreeEntity::getShareStatus, 1);
        return getBaseMapper().selectList(query);
    }

    @Override
    public void updateChildShareStatus(Long virtualOrgId) {
        LambdaUpdateWrapper<VirtualOrganTreeEntity> update = Wrappers.lambdaUpdate();
        update.likeRight(VirtualOrganTreeEntity::getIdChain, virtualOrgId)
                .set(VirtualOrganTreeEntity::getShareStatus, 0);
        getBaseMapper().update(new VirtualOrganTreeEntity(), update);
    }

    @Override
    public List<VirtualOrganTreeEntity> findByIdChain(Long virtualOrgId) {
        LambdaQueryWrapper<VirtualOrganTreeEntity> query = Wrappers.lambdaQuery();
        query.likeLeft(VirtualOrganTreeEntity::getIdChain, virtualOrgId);
        return getBaseMapper().selectList(query);
    }

    @Override
    public void deviceTemplate(String virtualOrganId, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        String fileName = URLEncoder.encode("虚拟设备导入模板", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Access-Control-Expose-Headers", "fileName");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        response.setHeader("fileName", fileName + ".xlsx");

        List<VirtualDeviceExportDto> data = new ArrayList<>();

        // 一级和二级选项
        // 查询下级组织
        VirtualOrganTreeEntity entity = new VirtualOrganTreeEntity();
        entity.setIdChain(virtualOrganId);
        List<VirtualOrganTreeEntity> list = virtualOrganTreeService.getList(entity);
        Map<String, String[]> mainOptions = new HashMap<>();
        list.forEach(it -> {
            String name = it.getName();
            if (it.getName().matches("^[0-9].*")) {
                name = "_" + name;
            }
            mainOptions.put(name, new String[]{it.getOrganCode()});
        });

        EasyExcel.write(response.getOutputStream(), VirtualDeviceExportDto.class)
                .autoCloseStream(Boolean.FALSE)
                .sheet("设备导入模板")
                .registerWriteHandler(new CascadingDropdownWriteHandler(mainOptions))
                .doWrite(data);
    }

    @Override
    public Result deviceImport(MultipartFile file) throws IOException {
        VirtualDeviceExportDto excel = new VirtualDeviceExportDto();
        EasyExcelListener<VirtualDeviceExportDto> res = new EasyExcelListener<VirtualDeviceExportDto>("/ops/virtualOrganTree/deviceImport") {
            @Override
            public ExcelCheckResult<VirtualDeviceExportDto> checkImportExcel(List<VirtualDeviceExportDto> list, Map<String, String> map) {
                ExcelCheckResult<VirtualDeviceExportDto> result = super.getResult();

                List<VirtualDeviceExportDto> deviceList = new ArrayList<>();

                List<String> channelIdList = new ArrayList<>();
                List<String> orgCodeList = new ArrayList<>();
                List<String> concatenatedCodeList = new ArrayList<>();
                list.forEach(it -> {
                    channelIdList.add(it.getChannelId());
                    orgCodeList.add(it.getOrgCode());
                    concatenatedCodeList.add(it.getConcatenatedCode());
                });
//                List<OpsDeviceChannelEntity> channelList = opsDeviceChannelService.getByChannelIds(channelIdList);

                List<VirtualOrganDeviceRelativeEntity> concatenatedCodeEntityList = virtualOrganDeviceRelativeService.getByConcatenatedCodeList(concatenatedCodeList);
                Map<String, VirtualOrganDeviceRelativeEntity> concatenatedCodeEntityMap = concatenatedCodeEntityList.stream().collect(Collectors.toMap(VirtualOrganDeviceRelativeEntity::getConcatenatedCode, Function.identity(), (key1, key2) -> key1));

                // 表格数据校验
                for (VirtualDeviceExportDto infoExcel : list) {
                    Map<Integer, String> cellErrColMap = new HashMap<>();
                    if (StringUtil.isEmpty(infoExcel.getDeviceCode())) {
                        cellErrColMap.put(0, "设备SN码必填");
                        result.getErrDtos().add(new ExcelImportErrDto<>(infoExcel, cellErrColMap));
                        continue;
                    }
                    if (StringUtil.isEmpty(infoExcel.getChannelId())) {
                        cellErrColMap.put(1, "通道号必填");
                        result.getErrDtos().add(new ExcelImportErrDto<>(infoExcel, cellErrColMap));
                        continue;
                    }
//                    if (channelMap.containsKey(infoExcel.getChannelId())) {
//                        cellErrColMap.put(1, "通道号已存在");
//                        result.getErrDtos().add(new ExcelImportErrDto<>(infoExcel, cellErrColMap));
//                        continue;
//                    }
                    if (StringUtil.isEmpty(infoExcel.getOrgCode())) {
                        cellErrColMap.put(3, "组织编码必填");
                        result.getErrDtos().add(new ExcelImportErrDto<>(infoExcel, cellErrColMap));
                        continue;
                    }

                    if (StringUtil.isEmpty(infoExcel.getConcatenatedCode())) {
                        cellErrColMap.put(4, "国标编码必填");
                        result.getErrDtos().add(new ExcelImportErrDto<>(infoExcel, cellErrColMap));
                        continue;
                    }

                    if (concatenatedCodeEntityMap.containsKey(infoExcel.getConcatenatedCode())) {
                        cellErrColMap.put(4, "国标编码已存在");
                        result.getErrDtos().add(new ExcelImportErrDto<>(infoExcel, cellErrColMap));
                        continue;
                    }

                    if (StringUtil.isEmpty(infoExcel.getConcatenatedName())) {
                        cellErrColMap.put(5, "国标名称必填");
                        result.getErrDtos().add(new ExcelImportErrDto<>(infoExcel, cellErrColMap));
                        continue;
                    }

                    deviceList.add(infoExcel);
                }

                // 查询通道
                List<VirtualOrganTreeEntity> organList = virtualOrganTreeService.getByOrgCodes(orgCodeList);
                Map<String, VirtualOrganTreeEntity> organMap = organList.stream().collect(Collectors.toMap(VirtualOrganTreeEntity::getOrganCode, Function.identity(), (key1, key2) -> key1));

                List<OpsDeviceChannelEntity> deviceEntities = opsDeviceChannelService.list(
                        new LambdaQueryWrapper<OpsDeviceChannelEntity>().in(OpsDeviceChannelEntity::getChannelId, channelIdList)
                );

                Map<String, String> deviceCodeMap = new HashMap<>();
                Map<String, String> deviceSNMap = new HashMap<>();
                Map<String, OpsDeviceChannelEntity> deviceChannelMap = new HashMap<>();
                for (OpsDeviceChannelEntity deviceEntity : deviceEntities) {
                    deviceCodeMap.put(deviceEntity.getChannelId(), StringUtil.isNotEmpty(deviceEntity.getBid()) ? deviceEntity.getBid() : deviceEntity.getChannelId());
                    deviceSNMap.put(deviceEntity.getChannelId(), deviceEntity.getDeviceSn());
                    deviceChannelMap.put(deviceEntity.getChannelId(), deviceEntity);
                }


                // 发送消息
                List<VirtualOrganDeviceRelativeEntity> relativeEntityList = new ArrayList<>();
                Map<String, List<Map<String, Object>>> syncMap = new HashMap<>();
                for (VirtualDeviceExportDto relativeDto : deviceList) {
                    VirtualOrganTreeEntity orgEntity = organMap.get(relativeDto.getOrgCode());
                    Map<String, Object> deviceMap = new HashMap<>();

                    Long id = IdWorker.getId();
                    deviceMap.put("id", id.toString());
                    deviceMap.put("treeId", Objects.nonNull(orgEntity) ? orgEntity.getOrganCode() : null);
                    deviceMap.put("sn", deviceCodeMap.get(relativeDto.getChannelId()));
                    deviceMap.put("deviceId", deviceSNMap.get(relativeDto.getChannelId()));
                    deviceMap.put("gbConcatenatedCode", relativeDto.getConcatenatedCode());
                    deviceMap.put("gbConcatenatedName", relativeDto.getConcatenatedName());
                    deviceMap.put("1400ConcatenatedCode", relativeDto.getConcatenatedCode());
                    OpsDeviceChannelEntity opsDeviceChannelEntity = deviceChannelMap.get(relativeDto.getChannelId());
                    if (StringUtil.isNotEmpty(opsDeviceChannelEntity)) {
                        DeviceEntity deviceEntity = deviceService.getById(opsDeviceChannelEntity.getDeviceId());
                        if (deviceEntity != null) {
                            deviceMap.put("longitude", deviceEntity.getLongitude() == null ? "0.0" : deviceEntity.getLongitude());
                            deviceMap.put("latitude", deviceEntity.getLatitude() == null ? "0.0" : deviceEntity.getLatitude());
                        }
                    }

                    if (Objects.nonNull(orgEntity)) {
                        if (syncMap.containsKey(orgEntity.getId().toString())) {
                            syncMap.get(orgEntity.getId().toString()).add(deviceMap);
                        } else {
                            List<Map<String, Object>> listMap = new ArrayList<>();
                            listMap.add(deviceMap);
                            syncMap.put(orgEntity.getId().toString(), listMap);
                        }
                    }

                    VirtualOrganDeviceRelativeEntity relativeEntity = new VirtualOrganDeviceRelativeEntity();
                    relativeEntity.setId(id);
                    relativeEntity.setConcatenatedGbName(relativeDto.getConcatenatedName());
                    relativeEntity.setChannelId(relativeDto.getChannelId());
                    relativeEntity.setConcatenatedCode(relativeDto.getConcatenatedCode());
                    if (Objects.nonNull(orgEntity)) {
                        relativeEntity.setVirtualOrganId(orgEntity.getId());
                        relativeEntity.setIdChain(orgEntity.getIdChain());
                        String topOorId = orgEntity.getIdChain().split(",")[0];
                        relativeEntity.setTopOrganId(topOorId);
                    }
                    relativeEntityList.add(relativeEntity);
                }

                // 发送同步消息
                syncMap.forEach((k, v) -> {
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("id", k);
                    for (List<Map<String, Object>> maps : Lists.partition(v, 10)) {
                        dataMap.put("deviceInfos", maps);
                        VirtualOrganTreeMessage virtualOrganTreeMessage = new VirtualOrganTreeMessage();
                        virtualOrganTreeMessage.setMessageType(3);
                        virtualOrganTreeMessage.setData(dataMap);
                        virtualOrganTreeProduce.sync(virtualOrganTreeMessage);
                    }
                });

                // 批量新增虚拟设备
                virtualOrganDeviceRelativeService.saveBatch(relativeEntityList);
                result.setSuccessCount(deviceList.size());
                return result;
            }
        };

        EasyExcel.read(file.getInputStream(), excel.getClass(), res).sheet().headRowNumber(1).doRead();
        ExcelCheckResult<VirtualDeviceExportDto> result = res.getResult();


        if (StringUtil.isNotEmpty(result.getKey())) {
            redisUtil.set(result.getKey(), result.getErrorExcelJson(), 60 * 5);
            return Result.ok(result.getErrStr(), result.getEmptyBean());
        }
        return Result.ok(result.getEmptyBean());
    }
    @Resource
    private DeviceService deviceService;

    @Override
    public List<VirtualOrganTreeEntity> getByOrgCodes(List<String> orgCodeList) {
        if (CollectionUtil.isEmpty(orgCodeList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<VirtualOrganTreeEntity> query = Wrappers.lambdaQuery();
        query.in(VirtualOrganTreeEntity::getOrganCode, orgCodeList);
        return getBaseMapper().selectList(query);
    }

    @Override
    public void organTemplate(HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String fileName = URLEncoder.encode("虚拟组织导入模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "fileName");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            response.setHeader("fileName", fileName + ".xlsx");

            List<VirtualOrganExportDto> data = new ArrayList<>();

            EasyExcel.write(response.getOutputStream(), VirtualOrganExportDto.class)
                    .autoCloseStream(Boolean.FALSE)
                    .sheet("设备导入模板")
                    .doWrite(data);
        } catch (Exception e) {
            throw new BizRuntimeException(e);
        }
    }

    @Override
    public Result organImport(MultipartFile file) throws IOException {
        VirtualOrganExportDto excel = new VirtualOrganExportDto();
        EasyExcelListener<VirtualOrganExportDto> res = new EasyExcelListener<VirtualOrganExportDto>("/ops/virtualOrganTree/organImport") {
            @Override
            public ExcelCheckResult<VirtualOrganExportDto> checkImportExcel(List<VirtualOrganExportDto> list, Map<String, String> map) {
                ExcelCheckResult<VirtualOrganExportDto> result = super.getResult();

                List<VirtualOrganExportDto> orgList = new ArrayList<>();
                // 表格数据校验
                for (VirtualOrganExportDto infoExcel : list) {
                    Map<Integer, String> cellErrColMap = new HashMap<>();
                    if (StringUtil.isEmpty(infoExcel.getOrgName())) {
                        cellErrColMap.put(0, "组织名称必填");
                        result.getErrDtos().add(new ExcelImportErrDto<>(infoExcel, cellErrColMap));
                        continue;
                    }
                    if (StringUtil.isEmpty(infoExcel.getOrgPath())) {
                        cellErrColMap.put(1, "组织路径必填");
                        result.getErrDtos().add(new ExcelImportErrDto<>(infoExcel, cellErrColMap));
                        continue;
                    }
                    if (StringUtil.isEmpty(infoExcel.getOrgCode())) {
                        cellErrColMap.put(2, "组织编码必填");
                        result.getErrDtos().add(new ExcelImportErrDto<>(infoExcel, cellErrColMap));
                    }
                    orgList.add(infoExcel);
                }

                Map<String, Long> orgIdMap = new HashMap<>();
                list.forEach(it -> {
                    orgIdMap.put(it.getOrgName(), IdWorker.getId());
                });

                List<VirtualOrganTreeEntity> saveList = orgList.stream().map(it -> {
                    VirtualOrganTreeEntity entity = new VirtualOrganTreeEntity();
                    entity.setId(orgIdMap.getOrDefault(it.getOrgName(), 0L));
                    entity.setName(it.getOrgName());
                    entity.setOrganCode(it.getOrgCode());
                    String[] split = it.getOrgPath().split("/");
                    entity.setParentId(split.length == 0 ? 0L : orgIdMap.getOrDefault(split[split.length - 1], 0L));
                    entity.setSort(0);
                    return entity;
                }).collect(Collectors.toList());

                buildIdChain(saveList);

                // 发送同步组织信息
                for (VirtualOrganTreeEntity entity : saveList) {
                    VirtualOrganTreeMessage virtualOrganTreeMessage = new VirtualOrganTreeMessage();
                    virtualOrganTreeMessage.setMessageType(1);
                    VirtualOrganTreeMqDto virtualOrganTreeDto = new VirtualOrganTreeMqDto();
                    virtualOrganTreeDto.setId(String.valueOf(entity.getId()));
                    virtualOrganTreeDto.setName(entity.getName());
                    virtualOrganTreeDto.setOrganCode(entity.getOrganCode());
                    virtualOrganTreeDto.setParentId(String.valueOf(entity.getParentId()));
                    virtualOrganTreeDto.setIdChain(entity.getIdChain());
                    virtualOrganTreeDto.setSort(String.valueOf(entity.getSort() == null ? 0 : entity.getSort()));
                    virtualOrganTreeMessage.setData(virtualOrganTreeDto);
                    virtualOrganTreeProduce.sync(virtualOrganTreeMessage);
                }

                saveBatch(saveList);
                return result;
            }
        };

        EasyExcel.read(file.getInputStream(), excel.getClass(), res).sheet().headRowNumber(1).doRead();
        ExcelCheckResult<VirtualOrganExportDto> result = res.getResult();

        if (StringUtil.isNotEmpty(result.getKey())) {
            redisUtil.set(result.getKey(), result.getErrorExcelJson(), 60 * 5);
            return Result.ok(result.getErrStr(), result.getEmptyBean());
        }
        return Result.ok(result.getEmptyBean());
    }

    public static void buildIdChain(List<VirtualOrganTreeEntity> flatData) {
        // 用于快速查找节点的 Map
        Map<String, VirtualOrganTreeEntity> nodeMap = new HashMap<>();
        for (VirtualOrganTreeEntity node : flatData) {
            nodeMap.put(node.getId().toString(), node);
        }

        // 构建每个节点的 idChain
        for (VirtualOrganTreeEntity node : flatData) {
            node.setIdChain(buildChain(node, nodeMap));
        }
    }

    private static String buildChain(VirtualOrganTreeEntity node, Map<String, VirtualOrganTreeEntity> nodeMap) {
        if (node.getParentId() == null || node.getParentId() == 0L) {
            // 根节点
            return node.getId().toString();
        } else {
            // 获取父节点
            VirtualOrganTreeEntity parent = nodeMap.get(node.getParentId().toString());
            if (parent != null) {
                // 递归构建父节点的 idChain
                return buildChain(parent, nodeMap) + "," + node.getId();
            } else {
                // 如果父节点不存在，可能是数据不完整
                throw new IllegalArgumentException("Parent node not found for ID: " + node.getId());
            }
        }
    }

    private void deviceShare(OpsPlatformEntity platform, List<ListPageByVirtualDto> deviceList, DeviceShareDto dto, Integer shareType) {
        List<String> orgIdList = deviceList.stream().map(ListPageByVirtualDto::getVirtualOrganId).collect(Collectors.toList());
        List<VirtualOrganTreeEntity> orgList = virtualOrganTreeService.listByIds(orgIdList);
        Map<Long, String> orgMap = orgList.stream().collect(Collectors.toMap(BaseEntity::getId, VirtualOrganTreeEntity::getOrganCode));
        SharePlatformMessage message = SharePlatformMessage.builder()
                .nodeId(platform.getNodeId())
                .messageType(SharePlatformMessageType.DEVICE_SHARE.getCode())
                .data(SharePlatformMessage.SharePlatformData.builder()
                        .shareType(shareType)
                        .sipId(platform.getSipId())
                        .virtualOrganId(String.valueOf(platform.getVirtualOrgId()))
                        // .filter(it -> Objects.nonNull(it.getShareCodeType()))
                        .deviceList(deviceList.stream().map(it -> {
                            String code;
                            if (shareType == 1) {
                                code = dto.getShareCodeType() == 1 ? String.valueOf(it.getDeviceCode()) : it.getConcatenatedCode();
                            } else {
                                code = it.getShareCodeType() == null || it.getShareCodeType() == 1 ? String.valueOf(it.getDeviceCode()) : it.getConcatenatedCode();
                            }
                            return SharePlatformMessage.ShareDeviceData.builder()
                                    .id(String.valueOf(it.getConcatenatedId()))
                                    .customName(it.getConcatenatedName())
                                    .videoShare(dto.getVideoShare() == 1)
                                    .ptzShare(dto.getPtzShare() == 1)
                                    .vcrShare(dto.getVcrShare() == 1)
                                    .deviceId(it.getDeviceCode())
                                    .longitude(it.getLongitude())
                                    .latitude(it.getLatitude())
                                    .channelId(it.getBid())
                                    .customId(code)
                                    .parentId(orgMap.getOrDefault(Long.valueOf(it.getVirtualOrganId()), ""))
                                    .build();
                        }).collect(Collectors.toList()))
                        .build())
                .build();
        virtualOrganTreeProduce.sendPlatformShareMessage(message);
    }

    private void sharePlatform(OpsPlatformEntity platform, List<Long> orgIds, Integer messageType, Integer shareType) {
        // 共享 发送MQ消息
        SharePlatformMessage message = SharePlatformMessage.builder()
                .messageType(messageType)
                .nodeId(platform.getNodeId())
                .data(SharePlatformMessage.SharePlatformData.builder()
                        .sipId(platform.getSipId())
                        .virtualOrganIds(orgIds.stream().map(String::valueOf).collect(Collectors.toSet()))
                        .shareType(shareType)
                        .build())
                .build();
        virtualOrganTreeProduce.sendPlatformShareMessage(message);
    }

    /**
     * 基于给定的虚拟组织树实体列表和根节点ID，构建树结构。
     *
     * @param list   虚拟组织树实体列表，不可为null。
     * @param rootId 根节点的ID，用于指定构建树的根节点。
     * @return 返回一个树结构列表，其中每个树节点是根据虚拟组织树实体转换而来的。
     */
    private List<Tree<Long>> buildTree(List<VirtualOrganTreeDto> list, Long rootId) {
        // 配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        // 数据ID
        treeNodeConfig.setIdKey("id");
        // 父级id字段
        treeNodeConfig.setParentIdKey("parentId");
        // 排序字段，这个字段不能是null，不然会报错，默认最好是数字
        treeNodeConfig.setWeightKey("sort");
        // 转换器
        return TreeUtil.build(list, rootId, treeNodeConfig, ((virtualOrganTree, treeNode) -> {
            // id
            treeNode.setId(virtualOrganTree.getId());
            // 父id
            treeNode.setParentId(virtualOrganTree.getParentId());
            treeNode.putExtra("name", virtualOrganTree.getName());
            treeNode.putExtra("organCode", virtualOrganTree.getOrganCode());
            treeNode.putExtra("idChain", virtualOrganTree.getIdChain());
            treeNode.putExtra("nodeType", virtualOrganTree.getNodeType());
            treeNode.putExtra("channelId", virtualOrganTree.getChannelId());
            treeNode.putExtra("channelBid", virtualOrganTree.getChannelBid());
            treeNode.putExtra("deviceId", virtualOrganTree.getDeviceId());
            treeNode.putExtra("isOnline", virtualOrganTree.getIsOnline());
            treeNode.putExtra("deviceTotalNum", virtualOrganTree.getDeviceTotalNum());
            treeNode.putExtra("onlineDeviceNum", virtualOrganTree.getOnlineDeviceNum());
        }));
    }


}