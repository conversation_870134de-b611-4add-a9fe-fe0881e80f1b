package com.saida.services.system.rocketMq.nodev1;

import com.alibaba.fastjson.JSON;
import com.saida.services.common.mq.message.SignalNodeMessage;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.converge.entity.SignalNodeEntity;
import com.saida.services.system.ops.service.SignalNodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Slf4j
@Component("rocket_mq_listener_signal_node")

public class SignalNodeMessageListenerVLinker implements VLinkerMqMessageListener {

    @Autowired
    private SignalNodeService signalNodeService;


    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("signal_node")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        SignalNodeMessage message = JSON.parseObject(messageBody, SignalNodeMessage.class);
        SignalNodeEntity node = new SignalNodeEntity();
        BeanUtils.copyProperties(message, node);
        node.setStatus(1);//在线
        node.setVersion(1);
        if (message.getUniverse() != null){
            node.setUniversePort(message.getUniverse().getPort());
            node.setUniverseIp(message.getUniverse().getIp());
        }
        node.setNetworkInfo(JSON.toJSONString(message.getNetworks()));
        signalNodeService.addOrUpdate(node);
    }

}

