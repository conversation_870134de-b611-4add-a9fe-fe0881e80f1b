package com.saida.services.system.peopleDeployControl.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 布控-设备关联表(DeployControlDeviceChannelRef)实体类
 *
 * <AUTHOR>
 * @since 2025-06-17 09:42:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("deploy_control_device_channel_ref")
public class DeployControlDeviceChannelRef implements Serializable {
    private static final long serialVersionUID = -94970276013469162L;
    /**
     * 布控id
     */
   private Long deployControlId;
    /**
     * camera表主键
     */
   private Long cameraId;
}

