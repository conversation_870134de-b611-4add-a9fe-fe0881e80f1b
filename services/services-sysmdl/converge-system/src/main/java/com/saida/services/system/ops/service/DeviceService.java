package com.saida.services.system.ops.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.mq.message.DeviceMessage;
import com.saida.services.converge.dto.BatchSynchronizationNameDto;
import com.saida.services.converge.dto.SynchronousDeviceBasicDataDto;
import com.saida.services.converge.entity.DeviceEntity;
import com.saida.services.converge.entity.SignalNodeEntity;
import com.saida.services.converge.entity.dto.*;
import com.saida.services.converge.entity.params.OpsDeviceParams;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.entities.pojo.CountDto;
import com.saida.services.open.resp.VlinkerConvergeAddDeviceResp;
import com.saida.services.system.ops.dto.DeviceSubscribeDto;
import com.saida.services.system.ops.dto.DeviceUtilizeDto;
import com.saida.services.system.ops.vo.StorageListVo;
import com.saida.services.system.ops.vo.StorageViewListVo;

import java.util.List;

/**
 * 设备
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-10-24 14:41:21
 */
public interface DeviceService extends IService<DeviceEntity> {

    void changeMediaService(Long id, String channelId, String mediaServiceId);

    BasePageInfoEntity<DeviceAndChannelDto> deviceListPageByOpen(OpsDeviceParams entity, BaseRequest baseRequest);

    BasePageInfoEntity<DeviceAndChannelDto> listPageByOpen(OpsDeviceParams entity, BaseRequest baseRequest);

    DtoResult<Void> reboot(Long deviceId);

    Result createGbNumber(Long orgId);

    /**
     * 添加或修改设备信息
     */
    DtoResult<Void> addOrUpdate(DeviceEntity entity);

    Result updateDeviceTransport(Long deviceId, Integer transport);

    Result deviceSubscribe(DeviceSubscribeDto deviceSubscribeDto);

    Result synchronousDeviceBasicData(SynchronousDeviceBasicDataDto dto);

    Result batchSynchronizationName(BatchSynchronizationNameDto dto);


    List<CountDto> getAccessWayTypeList();


    List<CountDto> getSdkAccessTypeList();


    List<CountDto> getTransportProtocolList();

    /**
     * 分页查询设备信息
     */
    BasePageInfoEntity<OpsDeviceDto> listPage(OpsDeviceParams entity, BaseRequest baseRequest);

    List<OpsDeviceDto> getList(OpsDeviceParams entity);

    /**
     * 设备监测列表
     *
     */
    BasePageInfoEntity<OpsDeviceDto> monitorList(OpsDeviceParams params, BaseRequest baseRequest);

    /**
     * 查询设备详情
     */
    OpsDeviceDto getInfo(Long id);

    /**
     * 删除设备
     */
    void delete(Long id);

    List<CountDto> getDeviceSdkOnline(Long deviceId);

    List<CountDto> groupByDeviceType(DeviceEntity entity);

    List<CountDto> groupByDayAndDeviceType(DeviceEntity entity);

    List<CountDto> groupByWeekAndDeviceType(DeviceEntity entity);

    List<CountDto> groupByMonthAndDeviceType(DeviceEntity entity);

    List<CountDto> groupByDay(DeviceEntity entity);

    List<CountDto> groupByWeek(DeviceEntity entity);

    List<CountDto> groupByMonth(DeviceEntity entity);

    Result regionalRanking(DeviceDto entity);

    void updateByDeviceMessage(DeviceMessage message, DeviceEntity device);

    void delete(List<Long> ids);

    OpsDeviceDto getVersionInfo(Long deviceId);

    DtoResult<VlinkerConvergeAddDeviceResp> addDeviceToQxNode(DeviceEntity entity, SignalNodeEntity signalNode);


    List<ChannelAndDeviceResultDto> getChannelDeviceList(ChannelAndDeviceParamDto dto);

    // 设备诊断
    Result deviceDiagnostics(Long deviceId);

    // 设备视频诊断
    Result deviceChannelDiagnostics(Long deviceId, String channelId);

    List<StorageListVo> storageList(OpsDeviceParams params);

    List<StorageViewListVo> storageViewList(OpsDeviceParams params);

    void deleteByDeviceCode(String deviceCode);

    List<DeviceEntity> getListByDeviceCode(String deviceCode);

    DeviceEntity getBySn(String sn);

    Result deviceUtilize(DeviceUtilizeDto deviceUtilizeDto);
}