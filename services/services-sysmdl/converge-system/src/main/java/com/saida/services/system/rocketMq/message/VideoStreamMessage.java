package com.saida.services.system.rocketMq.message;

import com.alibaba.fastjson.annotation.JSONField;
import com.saida.services.common.mq.vlinker.BaseMessage;
import lombok.Data;

@Data
public class VideoStreamMessage extends BaseMessage {

    @J<PERSON><PERSON>ield(name = "reqId")
    private String reqId;
    @JSO<PERSON>ield(name = "protocolCode")
    private String protocolCode;
    @JSONField(name = "url")
    private String url;
    @JSONField(name = "sdPushUrl")
    private String sdPushUrl;
    @JSONField(name = "cameraId")
    private Long cameraId;
    private Long dcTaskDispatchId;
}
