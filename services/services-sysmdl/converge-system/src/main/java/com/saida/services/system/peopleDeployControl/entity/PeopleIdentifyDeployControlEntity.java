package com.saida.services.system.peopleDeployControl.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 人员识别布控(PeopleIdentifyDeployControlEntity)实体类
 *
 * <AUTHOR>
 * @since 2025-06-17 09:42:37
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("people_identify_deploy_control")
public class PeopleIdentifyDeployControlEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -38120927530189792L;

    /**
     * sys_third_party表account字段
     */
    private String appId;
    /**
     * 任务名称
     */
   private String name;
    /**
     * 时间计划id
     */
   private Long timeTemplateId;
    /**
     * 人脸相似度
     */
   private String faceSimilarity;
    /**
     * 相同人告警间隔
     */
   private Integer samePersonAlarmInterval;
    /**
     * 相同人告警间隔单位，1-秒，2-分，3-时，4-天
     */
   private Integer samePersonAlarmIntervalUnit;
    /**
     * 运行状态 0：未启用 1：运行中
     */
   private Integer status = 0;
    /**
     * 最后开始运行时间
     */
   private LocalDateTime runStartTime;

}

