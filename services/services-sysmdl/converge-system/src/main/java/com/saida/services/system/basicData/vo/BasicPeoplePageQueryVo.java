package com.saida.services.system.basicData.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.system.basicData.entity.BasicPeoplePhotoEntity;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * description your class purpose
 *
 * <AUTHOR>
 * @version BasicCarPageQueryDto v1.0.0
 * @since 2025/6/5 15:29
 */
@Data
public class BasicPeoplePageQueryVo {
    private Long id;
    /**
     * 分组id
     */
    private Long groupId;
    /**
     * 工号
     */
    private String number;
    /**
     * 姓名
     */
    private String name;
    /**
     * 性别，1-男，2-女
     */
    private Integer sex;
    /**
     * 人员照片主图
     */
    private Long mainPhotoId;

    private Long createBy;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    private Long updateBy;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;
    /**
     * 分组名称
     */
    private String groupName;

    private List<BasicPeoplePhotoEntity> peoplePhotos;
}
