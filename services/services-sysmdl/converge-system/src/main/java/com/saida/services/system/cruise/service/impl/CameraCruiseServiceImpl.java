package com.saida.services.system.cruise.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.algorithm.entity.CameraCruiseEntity;
import com.saida.services.algorithm.entity.CameraCruiseRecordEntity;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.mq.rocketMq.RocketMQEnhanceTemplate;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.system.algVideo.service.AlgVideoService;
import com.saida.services.system.cruise.mapper.CameraCruiseMapper;
import com.saida.services.system.cruise.pojo.dto.CameraCruiseDto;
import com.saida.services.system.cruise.service.CameraCruiseRecordService;
import com.saida.services.system.cruise.service.CameraCruiseService;
import com.saida.services.system.rocketMq.config.TaskMessageProducer;
import com.saida.services.system.thread.CameraCruiseThread;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class CameraCruiseServiceImpl extends ServiceImpl<CameraCruiseMapper, CameraCruiseEntity> implements CameraCruiseService {

    private static final Map<Long, CameraCruiseThread> taskMap = new HashMap<>();

    @Resource
    private TaskMessageProducer taskMessageProducer;

    @Resource
    private CameraCruiseRecordService cameraCruiseRecordService;
    @Resource
    private AlgVideoService algVideoService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Lazy
    @Resource(name = ThreadPoolConfig.TASK_EXECUTOR_BEAN_NAME)
    private Executor taskExecutor;

    @Override
    public DtoResult<Long> saveOrUpdateCruise(CameraCruiseDto dto) {
        Long cruiseId = IdUtil.getSnowflakeNextId();
        if (null == dto.getCruiseId()) {
            long count = this.count(new LambdaQueryWrapper<CameraCruiseEntity>()
                    .eq(CameraCruiseEntity::getName, dto.getName())
                    .eq(CameraCruiseEntity::getCameraId, dto.getCameraId())
            );
            if (count > 0) {
                return DtoResult.error("该巡航名称已存在！");
            }

            CameraCruiseEntity entity = new CameraCruiseEntity();
            entity.setId(cruiseId);
            entity.setCameraId(dto.getCameraId());
            entity.setName(dto.getName());
            entity.setStatus(dto.getStatus());
            entity.setAppKey(dto.getAppKey());
            super.save(entity);
        } else {
            cruiseId = dto.getCruiseId();
            CameraCruiseEntity entity = Optional.ofNullable(this.getById(cruiseId)).orElseThrow(() -> new BizRuntimeException("该巡航不存在！"));
            if (Objects.equals(entity.getStatus(), 1)) {
                return DtoResult.error("当前巡航正在运行，不能编辑！");
            }

            long count = this.count(new LambdaQueryWrapper<CameraCruiseEntity>()
                    .eq(CameraCruiseEntity::getName, dto.getName())
                    .eq(CameraCruiseEntity::getCameraId, dto.getCameraId())
                    .ne(CameraCruiseEntity::getId, cruiseId)
            );
            if (count > 0) {
                return DtoResult.error("该巡航名称已存在！");
            }

            entity.setId(dto.getCruiseId());
            entity.setName(dto.getName());
            super.updateById(entity);
            cameraCruiseRecordService.remove(new LambdaQueryWrapper<CameraCruiseRecordEntity>()
                    .eq(CameraCruiseRecordEntity::getCruiseId, dto.getCruiseId()));
        }
        Long finalCruiseId = cruiseId;
        dto.getCruiseRecordList().forEach(e -> {
            CameraCruiseRecordEntity recordEntity = new CameraCruiseRecordEntity();
            recordEntity.setCruiseId(finalCruiseId);
            recordEntity.setCameraId(dto.getCameraId());
            recordEntity.setPrePoint(e.getPrePoint());
            recordEntity.setCruiseTime(e.getCruiseTime());
            recordEntity.setRotationTime(e.getRotationTime());
            recordEntity.setSort(e.getSort());
            cameraCruiseRecordService.save(recordEntity);
        });
        return DtoResult.ok(cruiseId);
    }

    @Override
    public DtoResult<Void> delCruise(Long cruiseId) {
        CameraCruiseEntity cameraCruiseEntity = Optional.ofNullable(this.getById(cruiseId)).orElseThrow(() -> new BizRuntimeException("该巡航不存在！"));
        if (Objects.equals(cameraCruiseEntity.getStatus(), 1)) {
            return DtoResult.error("当前巡航正在运行，不能删除！");
        }

        super.remove(new LambdaQueryWrapper<CameraCruiseEntity>()
                .eq(CameraCruiseEntity::getId, cruiseId));

        cameraCruiseRecordService.remove(new LambdaQueryWrapper<CameraCruiseRecordEntity>()
                .eq(CameraCruiseRecordEntity::getCruiseId, cruiseId));

        if (taskMap.containsKey(cruiseId)) {
            CameraCruiseThread task = taskMap.get(cruiseId);
            task.stopRun();
            taskMap.remove(cruiseId);
        }
        return DtoResult.ok();
    }

    @Override
    public DtoResult<Void> updateCruiseStatus(Long cruiseId, Integer status) {
        CameraCruiseEntity byId = super.getById(cruiseId);
        if (Objects.equals(1, status)) {
            long count = this.count(new LambdaQueryWrapper<CameraCruiseEntity>()
                    .eq(CameraCruiseEntity::getCameraId, byId.getCameraId())
                    .eq(CameraCruiseEntity::getStatus, 1)
            );
            if (count > 0) {
                return DtoResult.error("当前摄像头有其他巡航任务正在运行");
            }
        }
        super.update(new LambdaUpdateWrapper<CameraCruiseEntity>()
                .eq(CameraCruiseEntity::getId, cruiseId)
                .set(CameraCruiseEntity::getStatus, status));
        if (status == 1) {
            addTask(byId);
            // 发送kafka消息
            Map<String, Object> map = new HashMap<>();
            map.put("operate", "start");
            map.put("type", "cruise");
            List<CameraCruiseRecordEntity> list = cameraCruiseRecordService.list(new LambdaQueryWrapper<CameraCruiseRecordEntity>()
                    .eq(CameraCruiseRecordEntity::getCruiseId, cruiseId));
            map.put("data", list);
            map.put("cameraId", byId.getCameraId());
            map.put("date", System.currentTimeMillis());
//            taskMessageProducer.sendSync(JSON.toJSONString(map), RocketMqTopic.NOTICE_SYNC);
        } else {
            if (taskMap.containsKey(cruiseId)) {
                CameraCruiseThread task = taskMap.get(cruiseId);
                task.stopRun();
                taskMap.remove(cruiseId);
            }
            Map<String, Object> map = new HashMap<>();
            map.put("operate", "stop");
            map.put("type", "cruise");
            map.put("cameraId", byId.getCameraId());
//            taskMessageProducer.sendSync(JSON.toJSONString(map), RocketMqTopic.NOTICE_SYNC);
        }
        return DtoResult.ok();
    }

    @Resource
    private ThreadPoolConfig threadPoolConfig;


    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        threadPoolConfig.taskRunner(() -> {
            List<CameraCruiseEntity> list = super.list(new LambdaQueryWrapper<CameraCruiseEntity>()
                    .eq(CameraCruiseEntity::getStatus, 1));
            log.info("启动巡航任务：{}", list.size());
            list.forEach(this::addTask);
        });
    }


    @Autowired(required = false)
    private RocketMQEnhanceTemplate rocketMQEnhanceTemplate;


    private void addTask(CameraCruiseEntity e) {
        log.info("启动巡航任务：devId:{} name:{}", e.getCameraId(), e.getName());
        if (taskMap.containsKey(e.getId())) {
            CameraCruiseThread task = taskMap.get(e.getId());
            task.stopRun();
            taskMap.remove(e.getId());
        }
        List<CameraCruiseRecordEntity> cameraCruiseRecordEntityList = cameraCruiseRecordService.list(new LambdaQueryWrapper<CameraCruiseRecordEntity>()
                .eq(CameraCruiseRecordEntity::getCruiseId, e.getId()));
        CameraCruiseThread task = new CameraCruiseThread(e.getId(), cameraCruiseRecordEntityList, algVideoService
                , redisTemplate,  rocketMQEnhanceTemplate);
        taskExecutor.execute(task);
        taskMap.put(e.getId(), task);
    }

    @PreDestroy
    public void destroy() {
        taskMap.forEach((k, v) -> {
            v.stopRun();
        });
    }
}