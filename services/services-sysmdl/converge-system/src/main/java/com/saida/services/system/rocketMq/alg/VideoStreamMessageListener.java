package com.saida.services.system.rocketMq.alg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.algorithm.dto.AlgorithmVideoLiveUrlDto;
import com.saida.services.algorithm.entity.AlgorithmMappingEntity;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.interceptor.FeignHeaderContextHolder;
import com.saida.services.open.biz.resp.ThirdPartyPlatformsVideoLiveUrlResp;
import com.saida.services.system.algVideo.service.AlgVideoService;
import com.saida.services.system.algorithm.service.AlgorithmMappingService;
import com.saida.services.system.analyse.mapper.TimeTemplateMapper;
import com.saida.services.system.analyse.pojo.entity.TaskDispatchEntity;
import com.saida.services.system.analyse.pojo.entity.TimeTemplateEntity;
import com.saida.services.system.analyse.service.TaskDispatchService;
import com.saida.services.system.device.entity.CloudServerEntity;
import com.saida.services.system.device.mapper.CloudServerMapper;
import com.saida.services.system.pb.OpenCommonEnum;
import com.saida.services.system.rocketMq.config.RocketMqTopic;
import com.saida.services.system.rocketMq.config.TaskMessageProducer;
import com.saida.services.system.rocketMq.dto.VideoStreamDTO;
import com.saida.services.system.rocketMq.enums.SyncActionEnum;
import com.saida.services.system.rocketMq.message.TaskDispatchMessage;
import com.saida.services.system.rocketMq.message.VideoStreamMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 视频流地址监听更新
 */
@Slf4j
@Component

public class VideoStreamMessageListener implements VLinkerMqMessageListener {

    @Resource
    private AlgVideoService algVideoService;
    @Resource
    private TaskDispatchService taskDispatchService;
    @Resource
    private TaskMessageProducer taskMessageProducer;
    @Resource
    private CloudServerMapper cloudServerMapper;
    @Resource
    private TimeTemplateMapper timeTemplateMapper;
    @Resource
    private AlgorithmMappingService algorithmMappingService;


    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("video_stream_get")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        log.info("[mq:video_stream_get] tags:{} 接收到消息：{}", vlinkerMqMessage.getTag(), messageBody);
        VideoStreamDTO videoStreamDTO = JSON.parseObject(messageBody, VideoStreamDTO.class, Feature.IgnoreNotMatch);

        TaskDispatchEntity taskDispatchEntity = taskDispatchService.getById(videoStreamDTO.getDcTaskDispatchId());
        if (taskDispatchEntity == null) {
            log.info("任务不存在！");
            if (StringUtil.isNotEmpty(vlinkerMqMessage.getTag())) {
                log.info("删除任务:{}", videoStreamDTO.getDcTaskDispatchId());
                TaskDispatchMessage taskDispatchMessage = new TaskDispatchMessage();
                taskDispatchMessage.setAction(SyncActionEnum.DELETE);
                taskDispatchEntity = new TaskDispatchEntity();
                taskDispatchEntity.setId(videoStreamDTO.getDcTaskDispatchId());
                taskDispatchMessage.setTaskDispatchEntity(taskDispatchEntity);
                taskDispatchMessage.setTag(vlinkerMqMessage.getTag());
                taskMessageProducer.sendSync(RocketMqTopic.TASK_DISPATCH, taskDispatchMessage);
            }
            return;
        }
        CloudServerEntity cloudServerEntity = cloudServerMapper.selectById(taskDispatchEntity.getCloudServerId());
        if (cloudServerEntity == null) {
            log.info("云服务不存在！");
            if (StringUtil.isNotEmpty(vlinkerMqMessage.getTag())) {
                log.info("删除没有云服务的任务:{}", videoStreamDTO.getDcTaskDispatchId());
                TaskDispatchMessage taskDispatchMessage = new TaskDispatchMessage();
                taskDispatchMessage.setAction(SyncActionEnum.DELETE);
                taskDispatchEntity = new TaskDispatchEntity();
                taskDispatchEntity.setId(videoStreamDTO.getDcTaskDispatchId());
                taskDispatchMessage.setTaskDispatchEntity(taskDispatchEntity);
                taskDispatchMessage.setTag(vlinkerMqMessage.getTag());
                taskMessageProducer.sendSync(RocketMqTopic.TASK_DISPATCH, taskDispatchMessage);
            }
            return;
        }
        log.info("[mq:video_stream_get] tag:{},tag:{}", vlinkerMqMessage.getTag(), cloudServerEntity.getUsername());
        if (taskDispatchEntity.getStatus() != 1) {
            log.info("[mq:video_stream_get]任务已经不是运行中了 不会返回流地址！");
            TaskDispatchMessage taskDispatchMessage = new TaskDispatchMessage();
            taskDispatchMessage.setAction(SyncActionEnum.SAVE_UPDATE);
            AlgorithmMappingEntity info = algorithmMappingService.getOne(new LambdaQueryWrapper<AlgorithmMappingEntity>()
                    .eq(AlgorithmMappingEntity::getAlgorithmId, taskDispatchEntity.getAlgorithmId())
                    .eq(AlgorithmMappingEntity::getSourceId, taskDispatchEntity.getCloudServerId())
                    .last("LIMIT 1"), false);
            if (info != null) {
                taskDispatchEntity.setAlgorithmName(info.getName());
            }
            taskDispatchMessage.setAlgorithmMappingEntity(info);
            taskDispatchMessage.setTaskDispatchEntity(taskDispatchEntity);
            taskDispatchMessage.setTag(cloudServerEntity.getUsername());
            if (taskDispatchEntity.getTimeTemplateId() != null) {
                TimeTemplateEntity timeTemplateEntity = timeTemplateMapper.selectById(taskDispatchEntity.getTimeTemplateId());
                if (timeTemplateEntity != null) {
                    taskDispatchMessage.setTimePlan(timeTemplateEntity.getTimePlan());
                }
            }
            taskMessageProducer.sendSync(RocketMqTopic.TASK_DISPATCH, taskDispatchMessage);
            return;
        }
        // 设置上下文 header 值
        FeignHeaderContextHolder.set("UserPlatformType", OpenCommonEnum.UserPlatformType.USER_PLATFORM_TYPE_ANCIENT_VERSION.getNumber() + "");
        // 解析消息，获取到对应的参数
        AlgorithmVideoLiveUrlDto dto = new AlgorithmVideoLiveUrlDto();
        dto.setCameraId(videoStreamDTO.getCameraId());
        dto.setProtocolCode(videoStreamDTO.getProtocolCode());
        dto.setStartAi(true);
        log.info("去获取流地址参数: {}", dto);
        DtoResult<ThirdPartyPlatformsVideoLiveUrlResp> liveUrl = algVideoService.getLiveUrl(dto);
        // 调用后清理，防止线程复用污染
        FeignHeaderContextHolder.clear();
        if (liveUrl.getCode() == 200) {
            // 获取到直播地址
            ThirdPartyPlatformsVideoLiveUrlResp data = liveUrl.getData();
            if (data != null) {
                VideoStreamMessage videoStreamMessage = new VideoStreamMessage();
                videoStreamMessage.setCameraId(videoStreamDTO.getCameraId());
                videoStreamMessage.setReqId(videoStreamDTO.getReqId());
                videoStreamMessage.setUrl(data.getUrl());
                videoStreamMessage.setSdPushUrl(data.getSdPushUrl());
                videoStreamMessage.setProtocolCode(data.getProtocolType());
                videoStreamMessage.setDcTaskDispatchId(videoStreamDTO.getDcTaskDispatchId());
                videoStreamMessage.setTag(cloudServerEntity.getUsername());
                taskMessageProducer.sendSync(RocketMqTopic.VIDEO_STREAM, videoStreamMessage);
                log.info("[mq:video_stream_get]发送视频流地址到mq：{}", videoStreamMessage);
                return;
            }
        }
        log.info("[mq:video_stream_get]获取视频流地址失败：{}", liveUrl);
    }
}

