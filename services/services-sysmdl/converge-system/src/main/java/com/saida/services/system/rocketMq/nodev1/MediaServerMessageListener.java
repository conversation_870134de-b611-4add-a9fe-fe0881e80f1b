package com.saida.services.system.rocketMq.nodev1;

import com.alibaba.fastjson.JSON;
import com.saida.services.common.mq.message.MediaServerMessage;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.converge.entity.MediaServerEntity;
import com.saida.services.system.ops.service.MediaServerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Slf4j
@Component

public class MediaServerMessageListener implements VLinkerMqMessageListener {

    @Autowired
    private MediaServerService mediaServerService;


    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("media_server_status")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        MediaServerMessage message = JSON.parseObject(messageBody, MediaServerMessage.class);
        handleMessage(message);
    }

    protected void handleMessage(MediaServerMessage message) {
        MediaServerEntity mediaServer = new MediaServerEntity();
        BeanUtils.copyProperties(message, mediaServer);
        mediaServer.setDomainUrl(message.getDomain());
        mediaServerService.addOrUpdate(mediaServer);
    }

}

