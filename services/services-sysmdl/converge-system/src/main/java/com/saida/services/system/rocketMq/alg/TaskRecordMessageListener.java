package com.saida.services.system.rocketMq.alg;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.system.analyse.controller.TaskCloudRecordController;
import com.saida.services.system.analyse.pojo.entity.TaskCloudRecordEntity;
import com.saida.services.system.analyse.service.TaskCloudRecordService;
import com.saida.services.system.rocketMq.message.TaskRecordMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 视频流地址监听更新
 */
@Slf4j
@Component

public class TaskRecordMessageListener implements VLinkerMqMessageListener {


    @Resource
    private TaskCloudRecordService taskCloudRecordService;


    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("task_record")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        onMessage(messageBody);
    }
    public void onMessage(String message) {
        TaskRecordMessage taskRecordMessage = JSON.parseObject(message, TaskRecordMessage.class);
        if (taskRecordMessage.getTaskId() == null) {
            log.error("[mq:task_record] 没有任务id");
            return;
        }

        TaskCloudRecordEntity taskCloudRecordEntity = new TaskCloudRecordEntity();
        taskCloudRecordEntity.setTaskDispatchId(taskRecordMessage.getTaskId());
        taskCloudRecordEntity.setContent(taskRecordMessage.getDesc());
        LocalDateTime localDateTime = LocalDateTimeUtil.of(taskRecordMessage.getTime());
        taskCloudRecordEntity.setCreateTime(localDateTime);
        taskCloudRecordEntity.setTime(taskRecordMessage.getTime());
        taskCloudRecordEntity.setSort(taskRecordMessage.getSort());
        taskCloudRecordEntity.setType(taskRecordMessage.getType());
        taskCloudRecordService.save(taskCloudRecordEntity);


        Map<String, SseEmitter> stringSseEmitterMap = TaskCloudRecordController.sseEmitters.get(taskRecordMessage.getTaskId());
        if (stringSseEmitterMap != null) {
            stringSseEmitterMap.forEach((k, sseEmitter) -> {
                try {
                    sseEmitter.send(SseEmitter.event().name("task_record").data(taskRecordMessage));
                } catch (Exception e) {
                    log.error("SSE-MSG-SSE连接异常！ error:", e);
                    sseEmitter.complete();
                    TaskCloudRecordController.sseEmitters.remove(taskRecordMessage.getTaskId());
                }
            });
        }
    }
}

