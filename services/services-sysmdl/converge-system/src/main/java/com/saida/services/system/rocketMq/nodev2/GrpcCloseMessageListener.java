package com.saida.services.system.rocketMq.nodev2;

import com.alibaba.fastjson.JSON;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.system.nodeGrpc.GrpcConfig;
import com.saida.services.system.rocketMq.dto.nodev2.GrpcCloseMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component

public class GrpcCloseMessageListener implements VLinkerMqMessageListener {

    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("node_v2_grpc_close")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        GrpcCloseMessage grpcCloseMessage = JSON.parseObject(messageBody, GrpcCloseMessage.class);
        onMessage(grpcCloseMessage);
    }


    @Resource
    private GrpcConfig grpcConfig;

    public void onMessage(GrpcCloseMessage message) {
        grpcConfig.closeGrpcChannel(message.getNodeId());
    }
}

