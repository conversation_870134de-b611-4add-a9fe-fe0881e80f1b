package com.saida.services.system.sys.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.enums.SyncDataActionEnum;
import com.saida.services.common.tools.VlinkerThrowableUtil;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.SDNumberUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.system.ConvSysOrgEntity;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.feign.open.system.IFeignOpenSystemApiController;
import com.saida.services.system.sys.entity.UserEntity;
import com.saida.services.system.sys.mapper.ConvSysOrgMapper;
import com.saida.services.system.sys.mapper.UserMapper;
import com.saida.services.system.sys.service.AttributeDetailService;
import com.saida.services.system.sys.service.ConvSysOrgService;
import com.saida.services.tools.attr.AttrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("orgService")
public class ConvSysOrgServiceImpl extends ServiceImpl<ConvSysOrgMapper, ConvSysOrgEntity> implements ConvSysOrgService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AttributeDetailService attributeDetailService;

    @Resource
    private IFeignOpenSystemApiController feignOpenSystemApiController;

    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        //同步区域信息到能开
        threadPoolConfig.taskRunner(() -> {
            List<ConvSysOrgEntity> parentList = super.list(new LambdaQueryWrapper<ConvSysOrgEntity>()
                    .eq(ConvSysOrgEntity::getParentId, 0));
            if (parentList.isEmpty()) {
                ConvSysOrgEntity parent = new ConvSysOrgEntity();
                long id = IdWorker.getId();
                parent.setParentId(0L);
                parent.setId(id);
                parent.setIdChain(String.valueOf(id));
                parent.setName("视频汇聚平台");
                parent.setType(1);
                super.save(parent);
                parent.setSyncDataActionEnum(SyncDataActionEnum.SAVE_OR_UPDATE);
                try {
                    DtoResult<Void> voidDtoResult = feignOpenSystemApiController.syncOrgFromConv(parent);
                    log.info("初始化 同步区域信息到能开：{}", JSON.toJSONString(voidDtoResult));
                } catch (Exception e) {
                    log.error("初始化 同步区域信息到能开异常...msg={}", VlinkerThrowableUtil.getMsg(e));
                }
            }
            log.info("同步区域信息到能开...");
            List<ConvSysOrgEntity> orgList = super.list();
            if (CollectionUtil.isEmpty(orgList)) {
                return;
            }
            orgList.forEach(p -> {
                p.setSyncDataActionEnum(SyncDataActionEnum.SAVE_OR_UPDATE);
                try {
                    DtoResult<Void> voidDtoResult = feignOpenSystemApiController.syncOrgFromConv(p);
                    log.info("同步区域信息到能开：{}", JSON.toJSONString(voidDtoResult));
                } catch (Exception e) {
                    log.error("同步区域信息到能开异常...msg={}", VlinkerThrowableUtil.getMsg(e));
                }
            });
        });
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Result addOrUpdate(ConvSysOrgEntity entity) {
        ConvSysOrgEntity parent = null;
        if (entity.getParentId() != null && entity.getParentId() != 0) {
            parent = baseMapper.selectById(entity.getParentId());
            if (parent == null) {
                return Result.error("父级组织不存在");
            }
            if (parent.getType() == 2) {
                return Result.error("级联组织下不允许创建数据！");
            }
        } else {
            entity.setParentId(0L);
        }

        if (SDNumberUtil.equals(entity.getId(), entity.getParentId())) {
            return Result.error("父级组织错误");
        }

        // 组织新增
        if (entity.getId() == null) {

            long orgCount = count(new LambdaQueryWrapper<ConvSysOrgEntity>()
                    .eq(ConvSysOrgEntity::getGbCode, entity.getGbCode()));
            if (orgCount > 0) {
                return Result.error("国标编码重复！");
            }
            entity.setCreateBy(JwtUtil.getUserId());
            entity.setCreateTime(LocalDateTime.now());
            entity.setId(IdWorker.getId());
            if (parent == null) {
                entity.setIdChain(String.valueOf(entity.getId()));
            } else {
                entity.setIdChain(String.format("%s,%s", parent.getIdChain(), entity.getId()));
            }
            entity.setSyncDataActionEnum(SyncDataActionEnum.SAVE_OR_UPDATE);
            DtoResult<Void> voidDtoResult = feignOpenSystemApiController.syncOrgFromConv(entity);
            if (!voidDtoResult.success()) {
                return Result.error(voidDtoResult.getMessage());
            }
            if (!save(entity)) {
                return Result.error("添加失败");
            }
        } else {
            entity.setCreateBy(JwtUtil.getUserId());
            entity.setUpdateTime(LocalDateTime.now());

            ConvSysOrgEntity oldOrg = baseMapper.selectById(entity.getId());
            if (oldOrg == null) {
                return Result.error("组织不存在");
            }
            long orgCount = count(new LambdaQueryWrapper<ConvSysOrgEntity>()
                    .eq(ConvSysOrgEntity::getGbCode, entity.getGbCode())
                    .ne(ConvSysOrgEntity::getId, entity.getId()));
            if (orgCount > 0) {
                return Result.error("国标编码重复！");
            }

            // 修改父级
            if (!SDNumberUtil.equals(oldOrg.getParentId(), entity.getParentId())) {
                if (SDNumberUtil.equals(entity.getParentId(), 0L)) {
                    entity.setIdChain(String.valueOf(entity.getId()));
                } else {
                    ConvSysOrgEntity pOrg = getById(entity.getParentId());
                    if (pOrg == null) {
                        return Result.error("父级组织不存在");
                    }
                    entity.setIdChain(String.format("%s,%s", parent.getIdChain(), entity.getId()));
                }
                List<ConvSysOrgEntity> childList = list(new LambdaQueryWrapper<ConvSysOrgEntity>().likeRight(ConvSysOrgEntity::getIdChain, oldOrg.getIdChain() + ","));
                if (childList != null && !childList.isEmpty()) {
                    childList.forEach(p -> {
                        if (!SDNumberUtil.equals(p.getId(), entity.getId()) && !StringUtil.isEmpty(p.getIdChain())) {
                            p.setIdChain(p.getIdChain().replace(oldOrg.getIdChain(), entity.getIdChain()));
                            p.setUpdateTime(entity.getUpdateTime());
                            p.setUpdateBy(entity.getUpdateBy());
                        }
                    });
                }
                if (updateById(entity)) {
                    if (childList != null && !childList.isEmpty()) {
                        if (!updateBatchById(childList)) {
                            return Result.error("更新失败");
                        }
                    }
                    return Result.ok();
                }
                return Result.error("更新失败");
            }

            entity.setSyncDataActionEnum(SyncDataActionEnum.SAVE_OR_UPDATE);
            DtoResult<Void> voidDtoResult = feignOpenSystemApiController.syncOrgFromConv(entity);
            if (!voidDtoResult.success()) {
                return Result.error(voidDtoResult.getMessage());
            }
            if (updateById(entity)) {
                return Result.ok();
            }
            return Result.error("更新失败");
        }
        return Result.ok();
    }

    @Override
    public List<Tree<Long>> getTree(ConvSysOrgEntity entity) {
        ConvSysOrgEntity org = getById(JwtUtil.getOrgId());
        if (org == null) {
            throw new BizRuntimeException("当前用户组织不存在");
        }
        List<Long> orgIds = new ArrayList<>();
        // 名称过滤 前端自己过滤
//        if (!StringUtil.isEmpty(entity.getName())) {
//            List<ConvSysOrgEntity> tmp = list(new LambdaQueryWrapper<ConvSysOrgEntity>()
//                    .eq(entity.getType() != null, ConvSysOrgEntity::getType, entity.getType())
//                    .likeRight(ConvSysOrgEntity::getIdChain, org.getIdChain())
//                    .likeRight(ConvSysOrgEntity::getName, entity.getName()));
//            if (tmp != null && !tmp.isEmpty()) {
//                orgIds = tmp.stream()
//                        .map(ConvSysOrgEntity::getIdChain)
//                        .filter(o -> !StringUtil.isEmpty(o))
//                        .map(o -> o.replace(org.getIdChain(), ""))
//                        .flatMap(o -> Arrays.stream(o.split(",")))
//                        .filter(o -> !StringUtil.isEmpty(o) && NumberUtil.isNumber(o))
//                        .map(Long::valueOf)
//                        .collect(Collectors.toList());
//                orgIds.add(org.getId());// 当前组织
//                orgIds = orgIds.stream().distinct().collect(Collectors.toList());
//            } else {
//                return new ArrayList<>();
//            }
//        }

        List<ConvSysOrgEntity> list = list(new LambdaQueryWrapper<ConvSysOrgEntity>()
                .eq(entity.getType() != null, ConvSysOrgEntity::getType, entity.getType())
                .likeRight(ConvSysOrgEntity::getIdChain, org.getIdChain())
                .like(StringUtil.isNotEmpty(entity.getName()), ConvSysOrgEntity::getName, entity.getName())
                .in(!orgIds.isEmpty(), ConvSysOrgEntity::getId, orgIds)
                .orderByAsc(ConvSysOrgEntity::getSort)
                .orderByDesc(ConvSysOrgEntity::getId));
        if (list.stream().noneMatch(e -> e.getParentId() == 0L)) {
            list.addAll(super.list(new LambdaQueryWrapper<ConvSysOrgEntity>().eq(ConvSysOrgEntity::getParentId, 0L)));
        }
        if (list.isEmpty()) {
            return new ArrayList<>();
        }
        TreeNodeConfig config = new TreeNodeConfig();
        config.setIdKey("id");
        config.setParentIdKey("parentId");
        config.setWeightKey("sort");
        return TreeUtil.build(list, org.getParentId(), config, (obj, node) -> {
            node.putExtra("id", obj.getId());
            node.putExtra("parentId", obj.getParentId());
            node.putExtra("name", obj.getName());
            node.putExtra("adminCode", obj.getAdminCode());
            node.putExtra("gbCode", obj.getGbCode());
            node.putExtra("type", obj.getType());
        });
    }

    @Override
    public IPage<ConvSysOrgEntity> listPage(ConvSysOrgEntity entity, BaseRequest baseRequest) {
        IPage<ConvSysOrgEntity> page = baseMapper.listPage(new Page<>(baseRequest.getPageNum(), baseRequest.getPageSize()), entity);
        if (page == null || page.getRecords() == null || page.getRecords().isEmpty()) {
            return page;
        }
        fillAttr(page.getRecords());
        return page;
    }

    private void fillAttr(List<ConvSysOrgEntity> records) {
        if (records == null || records.isEmpty()) {
            return;
        }
        Map<Object, Object> dicMap = new HashMap<>();
        dicMap.putAll(attributeDetailService.getAllIdNameMap());
        records.replaceAll(o -> AttrUtil.putAttr(o, dicMap));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(ConvSysOrgEntity entity) {
        if (entity.getId() == null) {
            throw new BizRuntimeException("ID必传");
        }
        ConvSysOrgEntity byId = super.getById(entity.getId());
        if (byId == null) {
            throw new BizRuntimeException("组织不存在");
        }
        if (byId.getType() != 1) {
            throw new BizRuntimeException("非汇聚组织，禁止删除");
        }
        long orgCount = count(new LambdaQueryWrapper<ConvSysOrgEntity>().eq(ConvSysOrgEntity::getParentId, entity.getId()));
        if (orgCount > 0) {
            throw new BizRuntimeException("存在下级组织，无法删除");
        }
        long userCount = userMapper.selectCount(new LambdaQueryWrapper<UserEntity>().eq(UserEntity::getOrgId, entity.getId()));
        if (userCount > 0) {
            throw new BizRuntimeException("组织下存在用户，无法删除");
        }
        entity.setSyncDataActionEnum(SyncDataActionEnum.DELETE);
        DtoResult<Void> voidDtoResult = feignOpenSystemApiController.syncOrgFromConv(entity);
        if (!voidDtoResult.success()) {
            throw new BizRuntimeException(voidDtoResult.getMessage());
        }
        if (!removeById(entity.getId())) {
            throw new BizRuntimeException("删除失败");
        }
    }

    @Override
    public List<Long> getChildIds(Long id, Boolean subLevel) {
        return getChildIds(id, subLevel, null);
    }

    @Override
    public List<Long> getChildIds(Long id, Boolean subLevel, String orgName) {
        if (id == null) {
            return null;
        }
        ConvSysOrgEntity org = getById(id);
        if (org == null) {
            log.error("组织不存在");
            return null;
        }
        List<Long> ids = new ArrayList<>();

        LambdaQueryWrapper<ConvSysOrgEntity> query = new LambdaQueryWrapper<>();
        if (subLevel != null && subLevel) {// 查询子级
            query.likeRight(ConvSysOrgEntity::getIdChain, org.getIdChain());
        } else {
            query.eq(ConvSysOrgEntity::getId, id);
        }
        if (!StringUtil.isEmpty(orgName)) {
            query.like(ConvSysOrgEntity::getName, orgName);
        }

        List<ConvSysOrgEntity> orgs = list(query);

        if (orgs != null && !orgs.isEmpty()) {
            ids = orgs.stream()
                    .map(ConvSysOrgEntity::getIdChain)
                    .filter(o -> !StringUtil.isEmpty(o))
                    .map(o -> o.replace(org.getIdChain(), ""))
                    .flatMap(o -> Arrays.stream(o.split(",")))
                    .filter(o -> !StringUtil.isEmpty(o) && NumberUtil.isNumber(o))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());

            ConvSysOrgEntity tmp = orgs.stream().filter(o -> SDNumberUtil.equals(o.getId(), org.getId())).findFirst().orElse(null);
            if (tmp != null) {
                ids.add(org.getId());// 当前组织
            }
        }
        ids = ids.stream().distinct().collect(Collectors.toList());
        return ids;
    }

    @Override
    public List<ConvSysOrgEntity> getOrgList(Collection<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return super.list(new LambdaQueryWrapper<ConvSysOrgEntity>().in(ConvSysOrgEntity::getId, ids));
    }

    @Override
    public Map<Long, String> getIdNameMap(Collection<Long> ids) {
        List<ConvSysOrgEntity> list = getOrgList(ids);
        if (list == null || list.isEmpty()) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(ConvSysOrgEntity::getId, ConvSysOrgEntity::getName));
    }

    @Override
    public List<ConvSysOrgEntity> getChildList(Long id) {
        return getChildList(id, null);
    }

    @Override
    public List<ConvSysOrgEntity> getChildList(Long id, String name) {
        ConvSysOrgEntity org = getById(id);
        if (org == null) {
            throw new BizRuntimeException("组织不存在");
        }
        return list(new LambdaQueryWrapper<ConvSysOrgEntity>()
                .like(!StringUtil.isEmpty(name), ConvSysOrgEntity::getName, name)
                .likeRight(ConvSysOrgEntity::getIdChain, org.getIdChain()));
    }

}