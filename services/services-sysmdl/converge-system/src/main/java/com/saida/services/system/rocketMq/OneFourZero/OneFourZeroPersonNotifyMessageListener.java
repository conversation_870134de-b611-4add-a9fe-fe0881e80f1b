package com.saida.services.system.rocketMq.OneFourZero;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.common.service.FileService;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.OpsDeviceChannelEntity;
import com.saida.services.entities.pojo.FileModel;
import com.saida.services.system.ops.service.OpsDeviceAlarmService;
import com.saida.services.system.ops.service.OpsDeviceChannelService;
import com.saida.services.system.rocketMq.dto.OneFourZeroPersonNotifyDto;
import com.saida.services.system.rocketMq.dto.SubImageInfoObjectDto;
import com.saida.services.system.sys.dto.AlarmNotifyDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Component

public class OneFourZeroPersonNotifyMessageListener implements VLinkerMqMessageListener {


    @Resource
    private FileService fileService;

    @Resource
    private OpsDeviceAlarmService opsDeviceAlarmService;

    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;


    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("1400_send_msg")
                .tag("person")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String msg = new String(vlinkerMqMessage.getData());
        log.info("1400_person收到消息：{}", msg);
        OneFourZeroPersonNotifyDto oneFourZeroPersonNotifyDto = JSONObject.parseObject(msg, OneFourZeroPersonNotifyDto.class);
        log.info("1400_person收到消息：{}", oneFourZeroPersonNotifyDto);
        AtomicReference<DateTime> time = new AtomicReference<>(new DateTime());
        String dateFormat = DateUtil.format(new Date(), "yyyyMMdd");
        if (oneFourZeroPersonNotifyDto.getSubImageList() != null && oneFourZeroPersonNotifyDto.getSubImageList().getSubImageInfoObject() != null) {
            Map<String, List<SubImageInfoObjectDto>> collect = oneFourZeroPersonNotifyDto.getSubImageList().getSubImageInfoObject()
                    .stream()
                    .filter(item -> StringUtil.isNotEmpty(item.getDeviceID()))
                    .collect(Collectors.groupingBy(SubImageInfoObjectDto::getDeviceID));
            collect.forEach((k, v) -> {
                OpsDeviceChannelEntity channelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                        .eq(OpsDeviceChannelEntity::getDeleteFlag, 1)
                        .eq(OpsDeviceChannelEntity::getOneFourCode, k), false);
                if (channelEntity == null) {
                    log.info("1400_person收到消息：但是没有设备 过滤 1400编号:{}", k);
                    return;
                }
                List<String> imgUrl = new ArrayList<>();
                oneFourZeroPersonNotifyDto.getSubImageList().getSubImageInfoObject().forEach(item -> {
                    String storagePath = item.getStoragePath();
                    if (StringUtil.isNotEmpty(item.getShotTime())) {
                        time.set(DateUtil.parse(item.getShotTime(), "yyyyMMddHHmmss"));
                    }
                    String ossObjKey = "alarm/1400/" + k + "/" + dateFormat + "/" + IdWorker.getId() + ".jpg";
                    DtoResult<FileModel> fileModelDtoResult = fileService.uploadToS3(storagePath, ossObjKey);
                    if (fileModelDtoResult.success()) {
                        log.info("1400_person收到消息：图片地址：{} 上传后:{}", storagePath, fileModelDtoResult.getData().getUrl());
                        imgUrl.add(fileModelDtoResult.getData().getUrl());
                        item.setStoragePath(fileModelDtoResult.getData().getUrl());
                    }
                });
                AlarmNotifyDto dto = new AlarmNotifyDto();
                dto.setId(UUID.randomUUID().toString());
                dto.setSn(channelEntity.getDeviceSn());
                dto.setMsg_id(dto.getId());
                dto.setPriority(1);
                dto.setMethod(5);
                dto.setTimestamp(time.get().getTime());
                dto.setType(1401);
                dto.setSnap_paths(imgUrl);
                JSONObject ext = JSONObject.parseObject(JSONObject.toJSONString(oneFourZeroPersonNotifyDto));
                dto.setExt(ext);
                dto.setOriginalAlarmStr("gb-1400:1401");
                dto.setChannel_id(channelEntity.getChannelId());
                log.info("1400_person告警发送：dto:{}", JSON.toJSONString(dto));
                try {
                    opsDeviceAlarmService.handleMessage(dto);
                } catch (Exception e) {
                    log.error("1400_person告警发送失败：dto:{}", JSON.toJSONString(dto), e);
                }
            });

        }


    }
}

