package com.saida.services.system.rocketMq.message;

import com.saida.services.common.mq.vlinker.BaseMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskForImgReqMessage extends BaseMessage implements Serializable{
    private static final long serialVersionUID = 1L;

    /**
     * 所选算法
     */
    private String algorithmName;

    /**
     * 这次任务的唯一标识
     */
    private Long uuid;

    /**
     * 图片地址
     */
    private String url;

}
