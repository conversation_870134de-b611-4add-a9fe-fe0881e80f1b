package com.saida.services.system.peopleDeployControl.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.saida.services.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 告警-人员布控图片比对记录(AlarmPeopleControlCompareRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-06-19 20:01:55
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("alarm_people_control_compare_record")
public class AlarmPeopleControlCompareRecord extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -67131478748989762L;
    /**
     * 告警id
     */
   private Long alarmId;
    /**
     * 订阅信息的account
     */
    private String appId;
    /**
     * 人员id
     */
    private Long peopleId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 性别，1-男，2-女
     */
    private Integer sex;
    /**
     * 人员照片id
     */
    private Long peoplePhotoId;
    /**
     * 人员照片url
     */
   private String captureImage;
    /**
     * 匹配的人员照片url
     */
   private String matchPeopleImage;
    /**
     * 相似度
     */
   private String similarity;

}

