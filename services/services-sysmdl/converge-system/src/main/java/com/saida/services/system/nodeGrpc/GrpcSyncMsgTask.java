package com.saida.services.system.nodeGrpc;


import brave.Span;
import brave.Tracer;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.protobuf.ByteString;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.mq.vlinker.VLinkerMqTemplate;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.common.tools.VlinkerThrowableUtil;
import com.saida.services.converge.entity.SignalNodeEntity;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.system.ops.service.SignalNodeService;
import com.saida.services.system.pb.*;
import com.saida.services.system.rocketMq.nodev2.DeviceAlarmMessageListener;
import com.saida.services.system.rocketMq.nodev2.DeviceStatusMessageListener;
import io.grpc.ManagedChannel;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.*;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class GrpcSyncMsgTask {

    @Resource
    private SignalNodeService signalNodeService;
    @Resource
    private GrpcConfig grpcConfig;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Autowired
    private RedissonClient redissonClient;

    private final static String grpcRedisPrefix = "CONV:GRPC:";

    @EventListener(ApplicationReadyEvent.class)
    public void clientAppAuthInit() {
        threadPoolConfig.taskRunner(() -> {
            List<SignalNodeEntity> list = signalNodeService.list(new LambdaQueryWrapper<SignalNodeEntity>()
                    .eq(SignalNodeEntity::getVersion, 2)
                    .isNotNull(SignalNodeEntity::getGrpcUrl)
                    .isNotNull(SignalNodeEntity::getGrpcPort));
            list.forEach(signalNodeEntity -> {
                initGrpcClient(signalNodeEntity.getId());
            });
        });
    }

    public void initGrpcClient(String nodeId) {
        GrpcStatusDto grpcStatusDto = new GrpcStatusDto();
        grpcStatusDto.setNodeId(nodeId);
        grpcStatusDto.setSubscribeType(OpenCommonEnum.SubscribeType.SUBSCRIBE_TYPE_DEVICE_STATUS);
        subscribeGrpc(grpcStatusDto);
        GrpcStatusDto grpcStatusDto2 = new GrpcStatusDto();
        grpcStatusDto2.setNodeId(nodeId);
        grpcStatusDto2.setSubscribeType(OpenCommonEnum.SubscribeType.SUBSCRIBE_TYPE_DEVICE_ALARM);
        subscribeGrpc(grpcStatusDto2);
    }


    public void subscribeGrpc(GrpcStatusDto grpcStatusDto) {
        subscribeDeviceStatus(grpcStatusDto);
    }


    @Value("${sysconfig.uploadPath:#{null}}")
    private String uploadPath;

    public void syncDeviceStatus(String nodeId) {
        if (StringUtil.isEmpty(uploadPath)) {
            log.error("未配置上传路径");
            return;
        }
        ManagedChannel managedChannel = grpcConfig.getGrpcChannel(nodeId);
        if (managedChannel == null) {
            return;
        }
        SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = SunOpenGrpc.newBlockingStub(managedChannel);
        OpenCommonMessage.RequestAuthInfo authUser = grpcConfig.getAuthUser();
        OpenSunSubcribe.GetCatalogSQLiteFileRequest dbInfoRequest = OpenSunSubcribe.GetCatalogSQLiteFileRequest
                .newBuilder()
                .setSubscribeType(OpenCommonEnum.SubscribeType.SUBSCRIBE_TYPE_DEVICE_STATUS)
                .setAuthInfo(authUser)
                .build();
        OpenSunSubcribe.GetCatalogSQLiteFileReply dbInfo = sunOpenBlockingStub.getCatalogSQLiteFile(dbInfoRequest);
        if (dbInfo.getStatus() != OpenCommonEnum.ReplyStatus.REPLY_STATUS_SUC) {
            log.error("获取数据库信息失败,status:{}", dbInfo.getStatus());
            return;
        }
        ByteString sqliteDb = dbInfo.getSqliteDb();
        if (!FileUtil.exist(uploadPath + "/sqliteDb/")) {
            File mkdir = FileUtil.mkdir(uploadPath + "/sqliteDb/");
            log.info("sqliteDb文件夹已创建: {}", mkdir.getPath());
        }
        String dbFilePath = uploadPath + "/sqliteDb/" + nodeId;
        if (!FileUtil.exist(dbFilePath)) {
            File file = FileUtil.file(dbFilePath);
            try {
                boolean newFile = file.createNewFile();
                log.info("db文件已创建: {} newFile:{}", dbFilePath, newFile);
            } catch (IOException e) {
                log.error("db文件创建失败: dbFilePath:{} ", dbFilePath, e);
                return;
            }
        }
        String dbFilePathAll = FileUtil.file(dbFilePath).getPath();
        try (OutputStream outputStream = Files.newOutputStream(Paths.get(dbFilePathAll))) {
            sqliteDb.writeTo(outputStream); // 将 ByteString 内容写入文件
            log.info("db文件已保存到: {}", dbFilePathAll);
        } catch (IOException e) {
            log.error("db文件保存失败: ", e);
        }
        ByteString sqliteShm = dbInfo.getSqliteShm();
        String shmFilePath = uploadPath + "/sqliteDb/" + nodeId + "-shm";
        if (!FileUtil.exist(shmFilePath)) {
            File file = FileUtil.file(shmFilePath);
            try {
                boolean newFile = file.createNewFile();
                log.info("shm文件已创建: {} newFile:{}", shmFilePath, newFile);
            } catch (IOException e) {
                log.error("shm文件创建失败: shmFilePath:{}", shmFilePath, e);
                return;
            }
        }
        String shmFilePathAll = FileUtil.file(shmFilePath).getPath();
        try (OutputStream outputStream = Files.newOutputStream(Paths.get(shmFilePathAll))) {
            sqliteShm.writeTo(outputStream); // 将 ByteString 内容写入文件
            log.info("shm文件已保存到: {}", shmFilePathAll);
        } catch (IOException e) {
            log.error("shm文件保存失败: shmFilePathAll:{}", shmFilePathAll, e);
        }
        ByteString sqliteWal = dbInfo.getSqliteWal();
        String walFilePath = uploadPath + "/sqliteDb/" + nodeId + "-wal";
        if (!FileUtil.exist(walFilePath)) {
            File file = FileUtil.file(walFilePath);
            try {
                boolean newFile = file.createNewFile();
                log.info("wal文件已创建: {} newFile:{}", walFilePath, newFile);
            } catch (IOException e) {
                log.error("wal文件创建失败: ", e);
                return;
            }
        }
        String walFilePathAll = FileUtil.file(walFilePath).getPath();
        try (OutputStream outputStream = Files.newOutputStream(Paths.get(walFilePathAll))) {
            sqliteWal.writeTo(outputStream); // 将 ByteString 内容写入文件
            log.info("wal文件已保存到: {}", walFilePathAll);
        } catch (IOException e) {
            log.error("wal文件保存失败: ", e);
        }
        createDataSource(dbFilePathAll, nodeId);
        FileUtil.del(dbFilePathAll);
        FileUtil.del(shmFilePathAll);
        FileUtil.del(walFilePathAll);
    }


    @Autowired(required = false)
    private DeviceAlarmMessageListener deviceAlarmMessageListener;
    @Autowired(required = false)
    private DeviceStatusMessageListener deviceStatusMessageListener;

    @Autowired(required = false)
    private VLinkerMqTemplate vLinkerMqTemplate;


    @Resource
    private Tracer tracer;

    /**
     * 订阅设备状态
     */
    public void subscribeDeviceStatus(GrpcStatusDto grpcStatusDto) {
        String rediskey = grpcRedisPrefix + grpcStatusDto.getNodeId() + ":" + grpcStatusDto.getSubscribeType();
        RLock lock = redissonClient.getLock(rediskey);
        try {
            boolean isLocked = lock.tryLock(40, -1, TimeUnit.SECONDS);
            log.info("subscribeDeviceStatus 获取锁状态:{}", isLocked);
            if (!isLocked) {
                return;
            }
        } catch (InterruptedException e) {
            log.error("subscribeDeviceStatus 获取锁失败", e);
            return;
        }
        try {
            int pow = (int) Math.pow(2, grpcStatusDto.getCount());
            log.info("subscribeDeviceStatus 休眠: {}s", pow);
            Thread.sleep(1000L * pow);
        } catch (InterruptedException ignored) {
        }
        ManagedChannel managedChannel = grpcConfig.getGrpcChannel(grpcStatusDto.getNodeId());
        if (managedChannel == null) {
            log.error("subscribeDeviceStatus 没有找到对应的 gRPC 通道");
            return;
        }
        log.info("subscribeDeviceStatus 获取到 gRPC 通道: nodeId:{} isShutdown:{} ,isTerminated:{}", grpcStatusDto.getNodeId(), managedChannel.isShutdown(), managedChannel.isTerminated());
        String topic = "";
        if (OpenCommonEnum.SubscribeType.SUBSCRIBE_TYPE_DEVICE_STATUS.equals(grpcStatusDto.getSubscribeType())) {
            if (deviceStatusMessageListener == null) {
                throw new BizRuntimeException("无法订阅 SUBSCRIBE_TYPE_DEVICE_ALARM");
            }
            topic = deviceStatusMessageListener.vLinkerTopicConfig().getTopic();
        } else if (OpenCommonEnum.SubscribeType.SUBSCRIBE_TYPE_DEVICE_ALARM.equals(grpcStatusDto.getSubscribeType())) {
            if (deviceAlarmMessageListener == null) {
                throw new BizRuntimeException("无法订阅 SUBSCRIBE_TYPE_DEVICE_STATUS");
            }
            topic = deviceAlarmMessageListener.vLinkerTopicConfig().getTopic();
        } else {
            throw new BizRuntimeException("订阅类型错误");
        }
        OpenCommonMessage.RequestAuthInfo authUser = grpcConfig.getAuthUser();
        OpenSunSubcribe.RocketMQRequest request = OpenSunSubcribe.RocketMQRequest
                .newBuilder()
                .setAuthInfo(authUser)
                .setSubscribeType(grpcStatusDto.getSubscribeType())
                .setDeviceStatusMqConfig(OpenCommonMessage.RocketMQProducerConfig
                        .newBuilder()
                        .setLocalQueueSize(1024)
                        .setMessageReportingPeriodInSeconds(5)
                        .setMessageMaximumStacking(100)
                        .addNameServers(vLinkerMqTemplate.getNameServer())
                        .setOptionalConfig(OpenCommonMessage.RocketMQProducerConfig.OptionalConfig
                                .newBuilder()
                                .setGroupName(topic + "_send_group")
                                .build())
                        .setOptionalMessageConfig(OpenCommonMessage.RocketMQProducerConfig.OptionalMessageConfig.newBuilder()
                                .setTag(grpcStatusDto.getNodeId())
                                .build())
                        .setTopic(topic)
                        .build()).build();
        long threadId = Thread.currentThread().getId();
        StreamObserver<OpenCommonMessage.BaseReply> responseObserver = new StreamObserver<>() {
            @Override
            public void onNext(OpenCommonMessage.BaseReply addRocketMQReplay) {
                log.info("subscribeDeviceStatus nodeId:{} 收到消息：SubscribeType:{} ,desc:{}", grpcStatusDto.getNodeId(), grpcStatusDto.getSubscribeType(), addRocketMQReplay.getDesc());
                signalNodeService.update(new LambdaUpdateWrapper<SignalNodeEntity>()
                        .eq(SignalNodeEntity::getId, grpcStatusDto.getNodeId())
                        .set(SignalNodeEntity::getStatus, 1));
                grpcStatusDto.setCount(1);
                if (grpcStatusDto.getSubscribeType() == OpenCommonEnum.SubscribeType.SUBSCRIBE_TYPE_DEVICE_STATUS) {
                    Span newSpan = tracer.nextSpan().name("mq-consumer-span").start();
                    try (Tracer.SpanInScope ws = tracer.withSpanInScope(newSpan)) {
                        syncDeviceStatus(grpcStatusDto.getNodeId());
                    } catch (Exception e) {
                        log.error("subscribeDeviceStatus nodeId:{} syncDeviceStatus 异常", grpcStatusDto.getNodeId(), e);
                    } finally {
                        newSpan.finish();  // 结束 span
                    }
                }
            }

            @Override
            public void onError(Throwable throwable) {
                log.error("subscribeDeviceStatus nodeId:{} onError: {}", grpcStatusDto.getNodeId(), VlinkerThrowableUtil.getMsg(throwable));
                lock.unlockAsync(threadId);
                if (grpcStatusDto.getCount() < 5) {
                    grpcStatusDto.setCount(grpcStatusDto.getCount() + 1);
                }
                signalNodeService.update(new LambdaUpdateWrapper<SignalNodeEntity>()
                        .eq(SignalNodeEntity::getId, grpcStatusDto.getNodeId())
                        .set(SignalNodeEntity::getStatus, 2));
                subscribeGrpc(grpcStatusDto);
            }

            @Override
            public void onCompleted() {
                log.info("subscribeDeviceStatus nodeId:{} onCompleted", grpcStatusDto.getNodeId());
                lock.unlockAsync(threadId);
                if (grpcStatusDto.getCount() < 5) {
                    grpcStatusDto.setCount(grpcStatusDto.getCount() + 1);
                }
                signalNodeService.update(new LambdaUpdateWrapper<SignalNodeEntity>()
                        .eq(SignalNodeEntity::getId, grpcStatusDto.getNodeId())
                        .set(SignalNodeEntity::getStatus, 2));
                subscribeGrpc(grpcStatusDto);
            }
        };
        try {
            SunOpenGrpc.SunOpenStub sunOpenStub = SunOpenGrpc.newStub(managedChannel);
            log.info("sunOpenStub.addRocketMQ========> grpcStatusDto:{}", grpcStatusDto);
            sunOpenStub.addRocketMQ(request, responseObserver);
        } catch (Exception e) {
            log.error("subscribeDeviceStatus addRocketMQRequestStreamObserver is null", e);
        }
    }

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSSSSSXXX");

    /**
     * create table catalogs
     * (
     *     x_id              integer
     *         primary key autoincrement,
     *     parent_x_id       integer,
     *     entry_type        integer,
     *     device_addr       varchar(21),
     *     name              varchar(255),
     *     status_code       integer,
     *     status            text,
     *     planet_server_tag varchar(32),
     *     platform          integer,
     *     update_time       datetime
     * );
     */
    public void createDataSource(String dbFile, String nodeId) {
        log.info("createDataSource: nodeId:{}, dbFile: {}", nodeId, dbFile);
        String url = "jdbc:sqlite:" + dbFile; // SQLite 数据库文件

        // 1. 连接数据库
        try (Connection conn = DriverManager.getConnection(url)) {
            if (conn != null) {
                // 4. 查询数据
                String querySQL = "SELECT * FROM catalogs";
                try (Statement stmt = conn.createStatement();
                     ResultSet rs = stmt.executeQuery(querySQL)) {
                    int a = 0;
                    while (rs.next()) {
                        a++;

                        String updateTime = rs.getString("update_time");
                        // 定义时间格式化模式
                        // 将字符串转换为 OffsetDateTime 对象
                        OffsetDateTime dateTime = null;
                        try {
                            dateTime = OffsetDateTime.parse(updateTime, formatter);
                        } catch (Exception ignored) {
                            continue;
                        }
                        // 获取秒和纳秒
                        long seconds = dateTime.toEpochSecond();
                        int nanos = dateTime.getNano();
                        OpenCommonEnum.CatalogStatus status = OpenCommonEnum.CatalogStatus.forNumber(rs.getInt("status_code"));
                        OpenSunMessage.DeviceStatusSubscribeBody deviceStatusSubscribeBody = OpenSunMessage.DeviceStatusSubscribeBody.newBuilder()
                                .setXId(rs.getInt("x_id"))
                                .setParentId(rs.getInt("parent_x_id"))
                                .setEntryType(rs.getInt("entry_type"))
                                .setDeviceAddr(rs.getString("device_addr"))
                                .setName(rs.getString("name"))
                                .setPlanetTag(rs.getString("planet_server_tag"))
                                .setStatus(status)
                                .setGenerationTime(com.google.protobuf.Timestamp
                                        .newBuilder()
                                        .setSeconds(seconds)
                                        .setNanos(nanos)
                                        .build())
                                .build();
                        deviceStatusMessageListener.handleDeviceStatusPb("dbFile", nodeId, deviceStatusSubscribeBody);
                    }
                    log.info("createDataSource nodeId:{} 一共有：{}条", nodeId, a);
                } catch (Exception e) {
                    log.error("createDataSource nodeId:{} 查询失败：", nodeId, e);
                }
            }
        } catch (SQLException e) {
            log.error("数据库操作失败：", e);
        }
    }
}
