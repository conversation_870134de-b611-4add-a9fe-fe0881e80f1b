package com.saida.services.system.analyse.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.mq.vlinker.DelayLevelEnum;
import com.saida.services.common.service.BaseServiceImpl;
import com.saida.services.common.vo.BaseEnumVo;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.enums.AlarmIntervalEnum;
import com.saida.services.enums.AnalyseSensitivityEnum;
import com.saida.services.enums.AnalyzeFpsEnum;
import com.saida.services.system.algorithm.dto.AddTaskByBatchDto;
import com.saida.services.system.analyse.mapper.AnalyseTaskBatchMapper;
import com.saida.services.system.analyse.mapper.AnalyseTaskMapper;
import com.saida.services.system.analyse.pojo.dto.AnalyseTaskListPageDto;
import com.saida.services.system.analyse.pojo.entity.AnalyseTaskBatchEntity;
import com.saida.services.system.analyse.pojo.entity.AnalyseTaskBatchStatusEntity;
import com.saida.services.system.analyse.pojo.entity.AnalyseTaskBatchTaskEntity;
import com.saida.services.system.analyse.pojo.vo.AnalyseTaskListPageVo;
import com.saida.services.system.analyse.service.AnalyseTaskBatchService;
import com.saida.services.system.analyse.service.AnalyseTaskBatchStatusService;
import com.saida.services.system.analyse.service.AnalyseTaskBatchTaskService;
import com.saida.services.system.sys.service.AttributeDetailService;
import com.saida.services.tools.attr.AttrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class AnalyseTaskBatchServiceImpl extends BaseServiceImpl<AnalyseTaskBatchMapper, AnalyseTaskBatchEntity> implements AnalyseTaskBatchService {

    @Resource
    private AnalyseTaskBatchTaskService analyseTaskBatchTaskService;
    @Resource
    private AnalyseTaskMapper analyseTaskMapper;
    @Resource
    private AttributeDetailService attributeDetailService;
    @Resource
    private DataSourceTransactionManager dataSourceTransactionManager;
    @Resource
    private TransactionDefinition transactionDefinition;
    @Resource
    private AnalyseTaskBatchStatusService analyseTaskBatchStatusService;

    @Override
    public DtoResult<List<AnalyseTaskBatchEntity>> list(AnalyseTaskBatchEntity entity) {
        return DtoResult.ok(this.list());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DtoResult<Void> add(AnalyseTaskBatchEntity entity) {
        long count = this.count(new LambdaQueryWrapper<AnalyseTaskBatchEntity>()
                .eq(AnalyseTaskBatchEntity::getName, entity.getName()));
        if (count > 0) {
            return DtoResult.error("批次名称不能重复");
        }
        return this.save(entity) ? DtoResult.ok() : DtoResult.error();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DtoResult<Void> edit(AnalyseTaskBatchEntity entity) {
        AnalyseTaskBatchEntity updateAnalyseTaskBatchEntity = new AnalyseTaskBatchEntity();
        updateAnalyseTaskBatchEntity.setId(entity.getId());
        updateAnalyseTaskBatchEntity.setAnalyzeTime(entity.getAnalyzeTime());
        return this.updateById(updateAnalyseTaskBatchEntity) ? DtoResult.ok() : DtoResult.error();
    }

    @Override
    public DtoResult<Integer> getStatus() {
        AnalyseTaskBatchStatusEntity analyseTaskBatchStatusEntity = analyseTaskBatchStatusService.getAny(new LambdaQueryWrapper<>());
        if (null == analyseTaskBatchStatusEntity) {
            AnalyseTaskBatchStatusEntity saveAnalyseTaskBatchStatusEntity = new AnalyseTaskBatchStatusEntity();
            saveAnalyseTaskBatchStatusEntity.setStatus(1);
            saveAnalyseTaskBatchStatusEntity.setRemark("内置数据");
            analyseTaskBatchStatusService.save(saveAnalyseTaskBatchStatusEntity);
            return DtoResult.ok(0);
        }
        return DtoResult.ok(analyseTaskBatchStatusEntity.getStatus());
    }

    @Override
    public DtoResult<Void> editStatus(AnalyseTaskBatchStatusEntity entity) {
        return analyseTaskBatchStatusService.update(new LambdaUpdateWrapper<AnalyseTaskBatchStatusEntity>()
                .set(AnalyseTaskBatchStatusEntity::getStatus, entity.getStatus())) ? DtoResult.ok() : DtoResult.error();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DtoResult<Void> delete(AnalyseTaskBatchEntity entity) {
        // 1.删除 analyse_task_batch_task
        analyseTaskBatchTaskService.remove(new LambdaQueryWrapper<AnalyseTaskBatchTaskEntity>()
                .eq(AnalyseTaskBatchTaskEntity::getBatchId, entity.getId()));
        // 2.删除 analyse_task_batch
        return this.removeById(entity.getId()) ? DtoResult.ok() : DtoResult.error();
    }

    @Override
    public DtoResult<Integer> getNextSerialNumber(AnalyseTaskBatchEntity entity) {
        if (null == entity.getGroupId()) {
            return DtoResult.error("分组ID不能为空！");
        }
        LambdaQueryWrapper<AnalyseTaskBatchEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(AnalyseTaskBatchEntity::getGroupId, entity.getGroupId())
                .orderByDesc(AnalyseTaskBatchEntity::getSerialNumber)
                .last("limit 1");
        AnalyseTaskBatchEntity analyseTaskBatchEntity = this.getOne(lambdaQueryWrapper, false);
        if (analyseTaskBatchEntity == null) {
            return DtoResult.ok(1);
        }
        return DtoResult.ok(analyseTaskBatchEntity.getSerialNumber() + 1);
    }

    @Override
    public DtoResult<List<BaseEnumVo<Integer>>> getAnalyzeTimeList() {
        List<BaseEnumVo<Integer>> list = new ArrayList<>();
        DelayLevelEnum[] values = DelayLevelEnum.values();
        for (DelayLevelEnum e : values) {
            BaseEnumVo<Integer> vo = new BaseEnumVo<>();
            vo.setValue(e.getCode());
            vo.setLabel(e.getMsg());
            list.add(vo);
        }
        return DtoResult.ok(list);
    }

    @Override
    public DtoResult<BasePageInfoEntity<AnalyseTaskListPageVo>> getTaskListPage(AnalyseTaskBatchEntity entity, BaseRequest baseRequest) {
        if (null == entity.getId()) {
            return DtoResult.error("批次ID不能为空！");
        }
        List<AnalyseTaskBatchTaskEntity> analyseTaskBatchTaskEntityList = analyseTaskBatchTaskService.list(new LambdaQueryWrapper<AnalyseTaskBatchTaskEntity>()
                .eq(AnalyseTaskBatchTaskEntity::getBatchId, entity.getId())
        );
        if (CollectionUtil.isEmpty(analyseTaskBatchTaskEntityList)) {
            return DtoResult.ok();
        }
        Set<Long> taskIdSet = analyseTaskBatchTaskEntityList.stream().map(AnalyseTaskBatchTaskEntity::getTaskId).collect(Collectors.toSet());
        AnalyseTaskListPageDto analyseTaskListPageDto = new AnalyseTaskListPageDto();
        analyseTaskListPageDto.setTaskIdSet(taskIdSet);
        analyseTaskListPageDto.setDeviceId(entity.getDeviceId());
        analyseTaskListPageDto.setDeviceName(entity.getDeviceName());
        analyseTaskListPageDto.setChannelName(entity.getChannelName());
        try (Page<AnalyseTaskListPageVo> page = PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize())) {
            List<AnalyseTaskListPageVo> analyseTaskListPageVoList = analyseTaskMapper.listPage(analyseTaskListPageDto);
            if (CollectionUtil.isNotEmpty(analyseTaskListPageVoList)) {
                Map<Object, Object> dicMap = new HashMap<>();
                Map<Long, String> attrMap = attributeDetailService.getAllIdNameMap();
                if (CollectionUtil.isNotEmpty(attrMap)) {
                    dicMap.putAll(attrMap);
                }
                analyseTaskListPageVoList.replaceAll(o -> AttrUtil.putAttr(o, dicMap));
                for (AnalyseTaskListPageVo analyseTaskListPageVo : analyseTaskListPageVoList) {
                    AnalyseSensitivityEnum analyseSensitivityEnum = AnalyseSensitivityEnum.match(analyseTaskListPageVo.getAnalyseSensitivity());
                    if (null != analyseSensitivityEnum) {
                        analyseTaskListPageVo.setAnalyseSensitivityName(analyseSensitivityEnum.getDes());
                    }

                    AnalyzeFpsEnum analyzeFpsEnum = AnalyzeFpsEnum.match(analyseTaskListPageVo.getAnalyzeFps());
                    if (null != analyzeFpsEnum) {
                        analyseTaskListPageVo.setAnalyzeFpsName(analyzeFpsEnum.getDes());
                    }

                    AlarmIntervalEnum alarmIntervalEnum = AlarmIntervalEnum.match(analyseTaskListPageVo.getAlarmInterval());
                    if (null != alarmIntervalEnum) {
                        analyseTaskListPageVo.setAlarmIntervalName(alarmIntervalEnum.getDes());
                    }
                }
            }
            return DtoResult.ok(new BasePageInfoEntity<>(page));
        }
    }

    @Override
    public DtoResult<List<AnalyseTaskListPageVo>> getTaskList(AnalyseTaskBatchEntity entity) {
        if (null == entity.getId()) {
            return DtoResult.error("批次ID不能为空！");
        }
        List<AnalyseTaskBatchTaskEntity> analyseTaskBatchTaskEntityList = analyseTaskBatchTaskService.list(new LambdaQueryWrapper<>());
        if (CollectionUtil.isEmpty(analyseTaskBatchTaskEntityList)) {
            return DtoResult.ok();
        }
        Set<Long> taskIdSet = analyseTaskBatchTaskEntityList.stream().map(AnalyseTaskBatchTaskEntity::getTaskId).collect(Collectors.toSet());
        AnalyseTaskListPageDto analyseTaskListPageDto = new AnalyseTaskListPageDto();
        analyseTaskListPageDto.setTaskIdSet(taskIdSet);
        List<AnalyseTaskListPageVo> analyseTaskListPageVoList = analyseTaskMapper.listPage(analyseTaskListPageDto);
        if (CollectionUtil.isNotEmpty(analyseTaskListPageVoList)) {
            Map<Object, Object> dicMap = new HashMap<>();
            Map<Long, String> attrMap = attributeDetailService.getAllIdNameMap();
            if (CollectionUtil.isNotEmpty(attrMap)) {
                dicMap.putAll(attrMap);
            }
            analyseTaskListPageVoList.replaceAll(o -> AttrUtil.putAttr(o, dicMap));
            for (AnalyseTaskListPageVo analyseTaskListPageVo : analyseTaskListPageVoList) {
                AnalyseSensitivityEnum analyseSensitivityEnum = AnalyseSensitivityEnum.match(analyseTaskListPageVo.getAnalyseSensitivity());
                if (null != analyseSensitivityEnum) {
                    analyseTaskListPageVo.setAnalyseSensitivityName(analyseSensitivityEnum.getDes());
                }

                AnalyzeFpsEnum analyzeFpsEnum = AnalyzeFpsEnum.match(analyseTaskListPageVo.getAnalyzeFps());
                if (null != analyzeFpsEnum) {
                    analyseTaskListPageVo.setAnalyzeFpsName(analyzeFpsEnum.getDes());
                }

                AlarmIntervalEnum alarmIntervalEnum = AlarmIntervalEnum.match(analyseTaskListPageVo.getAlarmInterval());
                if (null != alarmIntervalEnum) {
                    analyseTaskListPageVo.setAlarmIntervalName(alarmIntervalEnum.getDes());
                }
            }
        }
        return DtoResult.ok(analyseTaskListPageVoList);
    }

    @Override
    public DtoResult<Void> addTask(AddTaskByBatchDto dto) {
        // 手动开启事务
        TransactionStatus transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
        try {
            AnalyseTaskBatchEntity analyseTaskBatchEntity = this.getById(dto.getBatchId());
            if (null == analyseTaskBatchEntity) {
                return DtoResult.error("分析任务批次不存在！");
            }
            if (null != analyseTaskBatchEntity.getInProgress() && analyseTaskBatchEntity.getInProgress() == 1) {
                return DtoResult.error("该批次任务正在执行，不允许操作！");
            }
            // 查询该批次下的所有任务
            List<AnalyseTaskBatchTaskEntity> analyseTaskBatchTaskEntityList = analyseTaskBatchTaskService.list(new LambdaUpdateWrapper<AnalyseTaskBatchTaskEntity>()
                    .eq(AnalyseTaskBatchTaskEntity::getBatchId, dto.getBatchId()));
            Set<Long> taskIdSet = analyseTaskBatchTaskEntityList.stream().map(AnalyseTaskBatchTaskEntity::getTaskId).collect(Collectors.toSet());
            List<AnalyseTaskBatchTaskEntity> batchSaveAnalyseTaskBatchTaskEntityList = new ArrayList<>();
            for (Long taskId : dto.getTaskIdSet()) {
                if (taskIdSet.contains(taskId)) {
                    continue;
                }
                AnalyseTaskBatchTaskEntity analyseTaskBatchTaskEntity = new AnalyseTaskBatchTaskEntity();
                analyseTaskBatchTaskEntity.setBatchId(dto.getBatchId());
                analyseTaskBatchTaskEntity.setTaskId(taskId);
                batchSaveAnalyseTaskBatchTaskEntityList.add(analyseTaskBatchTaskEntity);
            }
            if (CollectionUtil.isNotEmpty(batchSaveAnalyseTaskBatchTaskEntityList)) {
                analyseTaskBatchTaskService.saveBatch(batchSaveAnalyseTaskBatchTaskEntityList);
            }
            // 手动提交事务
            dataSourceTransactionManager.commit(transactionStatus);

            // 1.查询当前是否正在执行的任务
            // long inProgressCount = this.count(new LambdaQueryWrapper<AnalyseTaskBatchEntity>()
            //         .eq(AnalyseTaskBatchEntity::getInProgress, 1));
            // if (inProgressCount == 0) {
            // 如果当前没有正在执行的任务，则把批次任务放进MQ
            // sendToMqThread.sendAnalyseTaskBatchMq(analyseTaskBatchEntity);
            // }
            return DtoResult.ok();
        } catch (Exception e) {
            log.info("V-LINKER算法中台.调度中心，任务轮询，批次添加任务出错...msg={}", e.getMessage(), e);
            // 手动回滚事务
            dataSourceTransactionManager.rollback(transactionStatus);
            return DtoResult.error("批次添加任务出错");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DtoResult<Void> deleteTask(AnalyseTaskBatchTaskEntity analyseTaskBatchTaskEntity) {
        AnalyseTaskBatchEntity analyseTaskBatchEntity = this.getById(analyseTaskBatchTaskEntity.getBatchId());
        if (null == analyseTaskBatchEntity) {
            return DtoResult.error("分析任务批次不存在！");
        }
        if (null != analyseTaskBatchEntity.getInProgress() && analyseTaskBatchEntity.getInProgress() == 1) {
            return DtoResult.error("该批次任务正在执行，不允许操作！");
        }
        return analyseTaskBatchTaskService
                .remove(new LambdaQueryWrapper<AnalyseTaskBatchTaskEntity>()
                        .eq(AnalyseTaskBatchTaskEntity::getBatchId, analyseTaskBatchTaskEntity.getBatchId())
                        .eq(AnalyseTaskBatchTaskEntity::getTaskId, analyseTaskBatchTaskEntity.getTaskId())
                ) ? DtoResult.ok() : DtoResult.error();
    }

    @Override
    public DtoResult<Void> sendMq() {
        return DtoResult.ok();
        // try {
        //     DtoResult<List<AnalyseTaskGroupListDto>> dtoResult = analyseTaskGroupService.getList();
        //     List<AnalyseTaskGroupListDto> analyseTaskGroupListDtoList = dtoResult.getData();
        //     if (CollectionUtil.isEmpty(analyseTaskGroupListDtoList)) {
        //         log.info("V-LINKER算法中台.调度中心，任务轮询..手动触发，分析任务分组为空，程序结束...");
        //     }
        //
        //     List<AnalyseTaskBatchTaskEntity> analyseTaskBatchTaskEntityList = analyseTaskBatchTaskService.list();
        //     List<AnalyseTaskGroupListDto.AnalyseTaskBatchInfo> analyseTaskBatchInfoList = new ArrayList<>();
        //
        //     for (AnalyseTaskGroupListDto analyseTaskGroupListDto : analyseTaskGroupListDtoList) {
        //         List<AnalyseTaskGroupListDto.AnalyseTaskBatchInfo> analyseTaskBatchInfoListTemp = analyseTaskGroupListDto.getAnalyseTaskBatchList();
        //         if (CollectionUtil.isNotEmpty(analyseTaskBatchInfoListTemp)) {
        //             for (AnalyseTaskGroupListDto.AnalyseTaskBatchInfo analyseTaskBatchInfo : analyseTaskBatchInfoListTemp) {
        //                 if (analyseTaskBatchTaskEntityList.stream().anyMatch(t1 -> Objects.equals(t1.getBatchId(), analyseTaskBatchInfo.getId()))) {
        //                     analyseTaskBatchInfoList.add(analyseTaskBatchInfo);
        //                 }
        //             }
        //         }
        //     }
        //
        //     if (CollectionUtil.isEmpty(analyseTaskBatchInfoList)) {
        //         log.info("V-LINKER算法中台.调度中心，任务轮询..手动触发，分析任务批次为空，程序结束...");
        //         return DtoResult.ok();
        //     }
        //     AnalyseTaskGroupListDto.AnalyseTaskBatchInfo nextAnalyseTaskBatch = analyseTaskBatchInfoList.get(0);
        //     int size = analyseTaskBatchInfoList.size();
        //     for (int i = 0; i < size; i++) {
        //         AnalyseTaskGroupListDto.AnalyseTaskBatchInfo analyseTaskBatchInfo = analyseTaskBatchInfoList.get(i);
        //         if (Objects.equals(analyseTaskBatchInfo.getId(), currentAnalyseTaskBatchId)) {
        //             if (i + 1 < size) {
        //                 nextAnalyseTaskBatch = analyseTaskBatchInfoList.get(i + 1);
        //             }
        //         }
        //     }
        //
        //     AnalyseTaskBatchEntity nextAnalyseTaskBatchEntity = analyseTaskBatchService.getById(nextAnalyseTaskBatch.getId());
        //     if (null == nextAnalyseTaskBatchEntity) {
        //         log.info("V-LINKER算法中台.调度中心，任务轮询..消费者监听消息，下一个任务批次为空，程序结束");
        //         return;
        //     }
        //     // 查询该批次下的任务
        //     List<AnalyseTaskByBatchIdVo> analyseTaskEntityList = analyseTaskService.getAnalyseTaskByBatchId(currentAnalyseTaskBatchId, null);
        //     if (CollectionUtil.isEmpty(analyseTaskEntityList)) {
        //         log.info("V-LINKER算法中台.调度中心，任务轮询..消费者监听消息，任务列表为空，程序结束");
        //         return;
        //     }
        //
        //     Map<Long, List<AnalyseTaskByBatchIdVo>> map = analyseTaskEntityList.stream().collect(Collectors.groupingBy(AnalyseTaskEntity::getDeviceId));
        //     // 查询总任务
        //     List<AnalyseTaskByBatchIdVo> allAnalyseTaskEntityList = analyseTaskService.getAnalyseTaskByBatchId(null, null);
        //     Set<Long> keySet = map.keySet();
        //
        //     Map<Long, List<AnalyseTaskByBatchIdVo>> updateChannelMap = new HashMap<>();
        //
        //     Map<Long, List<AnalyseTaskByBatchIdVo>> disableAnalyseTaskMap = new HashMap<>();
        //     for (Long deviceId : keySet) {
        //         List<AnalyseTaskByBatchIdVo> analyseTaskEntityListTemp = map.get(deviceId);
        //         // 查询该设备下所有的分析任务
        //         List<AnalyseTaskByBatchIdVo> allAnalyseTaskEntityListTemp = allAnalyseTaskEntityList.stream().filter(t1 -> Objects.equals(t1.getDeviceId(), deviceId)).collect(Collectors.toList());
        //         // 如果分析任务数量不相等
        //         if (analyseTaskEntityListTemp.size() == allAnalyseTaskEntityListTemp.size()) {
        //             // 需要禁用通道的设备
        //             updateChannelMap.put(deviceId, analyseTaskEntityListTemp);
        //             continue;
        //         }
        //         List<AnalyseTaskByBatchIdVo> analyseTaskByBatchIdVoList = new ArrayList<>();
        //         for (AnalyseTaskByBatchIdVo analyseTaskByBatchIdVo : allAnalyseTaskEntityListTemp) {
        //             if (0 == analyseTaskByBatchIdVo.getEnable()) {
        //                 analyseTaskByBatchIdVoList.add(analyseTaskByBatchIdVo);
        //             }
        //         }
        //         // 需要设置分析算法的设备
        //         disableAnalyseTaskMap.put(deviceId, analyseTaskByBatchIdVoList);
        //     }
        //     // 禁用通道
        //     if (CollectionUtil.isNotEmpty(updateChannelMap)) {
        //         for (Long deviceId : keySet) {
        //             sendToCloudServerThread.disableChannel(updateChannelMap.get(deviceId).get(0));
        //         }
        //     }
        //     // 设置分析参数
        //     if (CollectionUtil.isNotEmpty(disableAnalyseTaskMap)) {
        //         Set<Long> setAiParamsMapKeySet = disableAnalyseTaskMap.keySet();
        //         for (Long deviceId : setAiParamsMapKeySet) {
        //             AnalyseTaskByBatchIdVo analyseTaskByBatchIdVo = new AnalyseTaskByBatchIdVo();
        //             analyseTaskByBatchIdVo.setDeviceId(deviceId);
        //             analyseTaskByBatchIdVo.setCloudServerId(disableAnalyseTaskMap.get(deviceId).get(0).getCloudServerId());
        //
        //             Set<Long> disableTaskIdSet = disableAnalyseTaskMap.get(deviceId).stream().map(AnalyseTaskByBatchIdVo::getId).collect(Collectors.toSet());
        //             sendToCloudServerThread.setAiParamsList(analyseTaskByBatchIdVo, null, disableTaskIdSet);
        //         }
        //     }
        //     analyseTaskBatchService.update(new LambdaUpdateWrapper<AnalyseTaskBatchEntity>()
        //             .eq(AnalyseTaskBatchEntity::getId, currentAnalyseTaskBatchId)
        //             .set(AnalyseTaskBatchEntity::getInProgress, 0)
        //             .set(AnalyseTaskBatchEntity::getLastExecuteEndTime, LocalDateTime.now())
        //     );
        //     sendToMqThread.sendAnalyseTaskBatchMq(nextAnalyseTaskBatchEntity);
        // } catch (Exception e) {
        //     if (null != currentAnalyseTaskBatchId) {
        //         analyseTaskBatchService.update(new LambdaUpdateWrapper<AnalyseTaskBatchEntity>()
        //                 .eq(AnalyseTaskBatchEntity::getId, currentAnalyseTaskBatchId)
        //                 .set(AnalyseTaskBatchEntity::getInProgress, 0)
        //                 .set(AnalyseTaskBatchEntity::getLastExecuteEndTime, LocalDateTime.now())
        //         );
        //     }
        //     log.info("V-LINKER算法中台.调度中心，任务轮询..消费者监听消息错误，程序结束");
        // }
    }
}