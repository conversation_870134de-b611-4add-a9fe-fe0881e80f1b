package com.saida.services.system.ops.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.saida.services.common.base.Result;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.mq.message.VirtualOrganTreeMessage;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.DeviceEntity;
import com.saida.services.converge.entity.OpsDeviceChannelEntity;
import com.saida.services.converge.entity.VirtualOrganDeviceRelativeEntity;
import com.saida.services.converge.entity.VirtualOrganTreeEntity;
import com.saida.services.converge.entity.excel.ShareDeviceExportTemplate;
import com.saida.services.converge.entity.params.VirtualOrganDeviceRelativeParams;
import com.saida.services.converge.entity.params.VirtualOrganTreeParam;
import com.saida.services.converge.entity.system.ConvSysOrgEntity;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.ops.dto.DeviceShareDto;
import com.saida.services.system.ops.dto.ListPageByVirtualDto;
import com.saida.services.system.ops.dto.ListPageByVirtualParam;
import com.saida.services.system.ops.dto.VirtualDeviceExport;
import com.saida.services.system.ops.mapper.VirtualOrganDeviceRelativeMapper;
import com.saida.services.system.ops.service.DeviceService;
import com.saida.services.system.ops.service.OpsDeviceChannelService;
import com.saida.services.system.ops.service.VirtualOrganDeviceRelativeService;
import com.saida.services.system.ops.service.VirtualOrganTreeService;
import com.saida.services.system.rocketMq.nodev1.VirtualOrganTreeProduce;
import com.saida.services.system.sys.service.ConvSysOrgService;
import com.saida.services.tools.excel.EasyExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 虚拟组织-设备关系
 */
@Slf4j
@Service("virtualOrganDeviceRelativeService")
@Transactional(rollbackFor = Exception.class)
public class VirtualOrganDeviceRelativeServiceImpl extends ServiceImpl<VirtualOrganDeviceRelativeMapper, VirtualOrganDeviceRelativeEntity> implements VirtualOrganDeviceRelativeService {

    @Autowired
    private OpsDeviceChannelService opsDeviceChannelService;

    @Autowired
    private VirtualOrganTreeService virtualOrganTreeService;

    @Autowired
    private VirtualOrganTreeProduce virtualOrganTreeProduce;
    @Resource
    private ConvSysOrgService convSysOrgService;

    @Override
    public Result listPageByVirtual(ListPageByVirtualParam param, BaseRequest baseRequest) {
        if (param.getOrgId() != null) {
            ConvSysOrgEntity byId = convSysOrgService.getById(param.getOrgId());
            if (byId != null) {
                param.setOrgIdChain(byId.getIdChain());
            }
        }
        if (param.getVirtualOrganId() != null) {
            VirtualOrganTreeEntity organTreeEntity = virtualOrganTreeService.getById(param.getVirtualOrganId());
            if (Objects.nonNull(param.getSubLevel()) && param.getSubLevel() == 1 && organTreeEntity != null) {
                param.setVirtualOrganIdChain(organTreeEntity.getIdChain());
            }
        }
        PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize());
        List<ListPageByVirtualDto> list = baseMapper.listPageByVirtual(param);
        return Result.ok(new BasePageInfoEntity<>(new PageInfo<>(list)));
    }

    @Override
    public List<ListPageByVirtualDto> listPageByVirtual(ListPageByVirtualParam param) {
        return baseMapper.listPageByVirtual(param);
    }

    @Override
    public void excelExport(HttpServletResponse response, ListPageByVirtualParam param) throws Exception {
        if (param.getOrgId() != null) {
            ConvSysOrgEntity byId = convSysOrgService.getById(param.getOrgId());
            if (byId != null) {
                param.setOrgIdChain(byId.getIdChain());
            }
        }
        if (param.getVirtualOrganId() != null) {
            VirtualOrganTreeEntity organTreeEntity = virtualOrganTreeService.getById(param.getVirtualOrganId());
            if (organTreeEntity != null) {
                param.setVirtualOrganIdChain(organTreeEntity.getIdChain());
            }
        }
        List<ListPageByVirtualDto> list = baseMapper.listPageByVirtual(param);
        List<ShareDeviceExportTemplate> objects = list.stream().map(it -> {
            ShareDeviceExportTemplate template = new ShareDeviceExportTemplate();
            template.setTreeName(it.getTreeName());
            template.setDeviceName(it.getDeviceName());
            template.setDeviceCode(it.getDeviceCode());
            template.setChannelCode(it.getChannelCode());
            template.setChannelName(it.getChannelName());
            template.setConcatenatedName(it.getConcatenatedName());
            template.setConcatenatedCode(it.getConcatenatedCode());
            template.setDeviceStatus(it.getDeviceStatus() == 1 ? "在线" : "离线");
            template.setShareStatus((it.getVcrShare() == 1 && it.getPtzShare() == 1 && it.getVideoShare() == 1) ? "已共享" : "未共享");
            template.setVcrShare(it.getVcrShare() == 1 ? "是" : "否");
            template.setPtzShare(it.getPtzShare() == 1 ? "是" : "否");
            template.setVideoShare(it.getVideoShare() == 1 ? "是" : "否");
            return template;
        }).collect(Collectors.toList());

        EasyExcelUtils.webWriteExcel(response, objects,
                ShareDeviceExportTemplate.class, "设备导出清单", "设备清单");
    }

    @Override
    public List<VirtualOrganDeviceRelativeEntity> findByIds(List<Long> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return getBaseMapper().selectBatchIds(idList);
    }

    @Override
    public void updateShareInfo(DeviceShareDto dto) {
        List<VirtualOrganDeviceRelativeEntity> list = dto.getIdList().stream().map(it -> {
            VirtualOrganDeviceRelativeEntity entity = new VirtualOrganDeviceRelativeEntity();
            entity.setId(it);
            entity.setShareCodeType(dto.getShareCodeType());
            entity.setVideoShare(dto.getVideoShare());
            entity.setPtzShare(dto.getPtzShare());
            entity.setVcrShare(dto.getVcrShare());
            return entity;
        }).collect(Collectors.toList());
        updateBatchById(list);
    }

    @Override
    public List<ListPageByVirtualDto> findByRelativeIds(List<Long> idList) {
        return baseMapper.findByRelativeIds(idList);
    }

    @Override
    public List<VirtualOrganDeviceRelativeEntity> getChildShareListByOrgId(Long topOrgId) {
        LambdaQueryWrapper<VirtualOrganDeviceRelativeEntity> query = Wrappers.lambdaQuery();
        query.eq(VirtualOrganDeviceRelativeEntity::getTopOrganId, topOrgId);
        return getBaseMapper().selectList(query);
    }

    @Override
    public void updateShareStatusByTopOrgId(Long orgId) {
        LambdaUpdateWrapper<VirtualOrganDeviceRelativeEntity> update = Wrappers.lambdaUpdate();
        update.eq(VirtualOrganDeviceRelativeEntity::getTopOrganId, orgId)
                .set(VirtualOrganDeviceRelativeEntity::getVcrShare, 0)
                .set(VirtualOrganDeviceRelativeEntity::getVideoShare, 0)
                .set(VirtualOrganDeviceRelativeEntity::getPtzShare, 0)
                .set(VirtualOrganDeviceRelativeEntity::getShareCodeType, null);
        getBaseMapper().update(new VirtualOrganDeviceRelativeEntity(), update);
    }

    @Override
    public void deviceExport(ListPageByVirtualParam param, HttpServletResponse response) throws Exception {
        if (param.getOrgId() != null) {
            ConvSysOrgEntity byId = convSysOrgService.getById(param.getOrgId());
            if (byId != null) {
                param.setOrgIdChain(byId.getIdChain());
            }
        }
        if (param.getVirtualOrganId() != null) {
            VirtualOrganTreeEntity organTreeEntity = virtualOrganTreeService.getById(param.getVirtualOrganId());
            if (Objects.nonNull(param.getSubLevel()) && param.getSubLevel() == 1 && organTreeEntity != null) {
                param.setVirtualOrganIdChain(organTreeEntity.getIdChain());
            }
        }
        List<ListPageByVirtualDto> list = baseMapper.listPageByVirtual(param);
        List<VirtualDeviceExport> exportList = list.stream().map(it -> {
            return VirtualDeviceExport.builder()
                    .status(it.getDeviceStatus() == 1 ? "在线" : "离线")
                    .deviceName(it.getDeviceCode() + "(" + it.getChannelName() + ")")
                    .deviceCode(it.getDeviceCode())
                    .channelId(it.getChannelCode())
                    .concatenatedCode(it.getConcatenatedCode())
                    .orgName(it.getTreeName())
                    .bindStatus(it.getStatusName())
                    .build();
        }).collect(Collectors.toList());
        EasyExcelUtils.webWriteExcel(response, exportList,
                VirtualDeviceExport.class, "设备导出清单", "设备清单");
    }

    @Override
    public List<VirtualOrganDeviceRelativeEntity> getByConcatenatedCodeList(List<String> concatenatedCodeList) {
        if (CollectionUtil.isEmpty(concatenatedCodeList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<VirtualOrganDeviceRelativeEntity> query = Wrappers.lambdaQuery();
        query.in(VirtualOrganDeviceRelativeEntity::getConcatenatedCode, concatenatedCodeList);
        return getBaseMapper().selectList(query);
    }

    @Resource
    private DeviceService deviceService;

    @Override
    @Transactional
    public Result bindDevice(VirtualOrganTreeParam virtualOrganTree) {
        List<VirtualOrganDeviceRelativeEntity> list = new ArrayList<>();
        List<String> concatenatedCodeList = getConcatenatedCode(virtualOrganTree, virtualOrganTree.getChannelIds().size());
        List<String> channelIds = virtualOrganTree.getChannelIds();
        VirtualOrganTreeEntity organTreeEntity = virtualOrganTreeService.getById(virtualOrganTree.getId());
        String idChain = organTreeEntity.getIdChain();
        //查询该节点下已绑定通道
        List<VirtualOrganDeviceRelativeEntity> virtualOrganDeviceRelativeEntityList = baseMapper.getListVirtualOrganId(virtualOrganTree.getId());
        //查询组织下不在该节点的通道绑定关系
        List<Long> ids = baseMapper.getListByChannelIdsAndNotVirtualOrganIdLikeIdChain(
                virtualOrganTree.getId(),
                idChain.substring(0, idChain.indexOf(",") > 0 ? idChain.indexOf(",") : idChain.length()), channelIds);
        for (int i = 0; i < channelIds.size(); i++) {
            String channelId = channelIds.get(i);
            String s = concatenatedCodeList.get(0).substring(0, 15);
            boolean flag = false;
            //判断该通道是否已绑定该节点
            for (VirtualOrganDeviceRelativeEntity virtualOrganDeviceRelative : virtualOrganDeviceRelativeEntityList) {
                if (virtualOrganDeviceRelative.getChannelId().equals(channelId)) {
                    //级联码是否一致
                    if (!s.equals(virtualOrganDeviceRelative.getConcatenatedCode().substring(0, 15))) {
                        ids.add(virtualOrganDeviceRelative.getId());
                    } else {
                        flag = true;
                        break;
                    }
                }
            }
            //已绑定该节点的通道
            if (flag) {
                continue;
            }
            VirtualOrganDeviceRelativeEntity virtualOrganDeviceRelativeEntity = new VirtualOrganDeviceRelativeEntity();
            virtualOrganDeviceRelativeEntity.setChannelId(channelId.trim());
            virtualOrganDeviceRelativeEntity.setConcatenatedCode(concatenatedCodeList.get(i));
            virtualOrganDeviceRelativeEntity.setVirtualOrganId(virtualOrganTree.getId());
            virtualOrganDeviceRelativeEntity.setTopOrganId(virtualOrganTree.getTopOrganId());
            Long id = IdWorker.getId();
            virtualOrganDeviceRelativeEntity.setId(id);
            virtualOrganDeviceRelativeEntity.setIdChain(idChain);
            virtualOrganDeviceRelativeEntity.setCreateTime(LocalDateTime.now());
            virtualOrganDeviceRelativeEntity.setCreateBy(JwtUtil.getUserId());
            virtualOrganDeviceRelativeEntity.setUpdateTime(LocalDateTime.now());
            virtualOrganDeviceRelativeEntity.setUpdateBy(JwtUtil.getUserId());
            list.add(virtualOrganDeviceRelativeEntity);
        }
        this.removeByIds(ids);
        this.saveBatch(list);
        //同步绑定信息
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("id", virtualOrganTree.getId().toString());
        List<OpsDeviceChannelEntity> deviceEntities = opsDeviceChannelService.list(
                new LambdaQueryWrapper<OpsDeviceChannelEntity>().in(OpsDeviceChannelEntity::getChannelId, channelIds)
        );
        Map<String, String> deviceCodeMap = deviceEntities.stream()
                .collect(Collectors.toMap(OpsDeviceChannelEntity::getChannelId, e -> {
                    if (StringUtil.isNotEmpty(e.getBid())) {
                        return e.getBid();
                    } else {
                        return e.getChannelId();
                    }
                }));
        Map<String, String> deviceSNMap = deviceEntities.stream()
                .collect(Collectors.toMap(OpsDeviceChannelEntity::getChannelId, OpsDeviceChannelEntity::getDeviceSn));
        List<Map<String, Object>> deviceList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            Map<Long, VirtualOrganTreeEntity> virtualOrganTreeMap = virtualOrganTreeService.listByIds(list.stream()
                            .map(VirtualOrganDeviceRelativeEntity::getVirtualOrganId).collect(Collectors.toSet()))
                    .stream().collect(Collectors.toMap(VirtualOrganTreeEntity::getId, e -> e));
            for (VirtualOrganDeviceRelativeEntity virtualOrganDeviceRelative : list) {
                Map<String, Object> deviceMap = new HashMap<>();
                deviceMap.put("id", String.valueOf(virtualOrganDeviceRelative.getId()));
                deviceMap.put("treeId", virtualOrganTreeMap
                        .getOrDefault(virtualOrganDeviceRelative.getVirtualOrganId()
                                , new VirtualOrganTreeEntity()).getOrganCode());
                deviceMap.put("sn", deviceCodeMap.get(virtualOrganDeviceRelative.getChannelId()));
                deviceMap.put("deviceId", deviceSNMap.get(virtualOrganDeviceRelative.getChannelId()));
                deviceMap.put("gbConcatenatedCode", virtualOrganDeviceRelative.getConcatenatedCode());
                deviceMap.put("1400ConcatenatedCode", virtualOrganDeviceRelative.getConcatenatedCode());
                Long deviceId = virtualOrganDeviceRelative.getDeviceId();
                DeviceEntity deviceEntity = deviceService.getById(deviceId);
                if (deviceEntity != null) {
                    deviceMap.put("longitude", deviceEntity.getLongitude() == null ? "0.0" : deviceEntity.getLongitude());
                    deviceMap.put("latitude", deviceEntity.getLatitude() == null ? "0.0" : deviceEntity.getLatitude());
                }
                deviceList.add(deviceMap);
            }
        }
        dataMap.put("deviceInfos", deviceList);
        VirtualOrganTreeMessage virtualOrganTreeMessage = new VirtualOrganTreeMessage();
        virtualOrganTreeMessage.setMessageType(3);
        virtualOrganTreeMessage.setData(dataMap);
        virtualOrganTreeProduce.sync(virtualOrganTreeMessage);
        return Result.ok("绑定成功");
    }


    @Override
    public Result delDevice(VirtualOrganDeviceRelativeParams dto) {
        List<VirtualOrganDeviceRelativeEntity> deviceRelativeEntityList = super.listByIds(dto.getIds());
        // 已经共享的设备
        Set<VirtualOrganDeviceRelativeEntity> collect1 = deviceRelativeEntityList.stream()
                .filter(e -> 1 == e.getVideoShare()
                        || 1 == e.getVcrShare() || 1 == e.getPtzShare()
                        || 1 == e.getFaceShare() || 1 == e.getBodyShare()
                        || 1 == e.getCarShare() || 1 == e.getNoCarShare()).collect(Collectors.toSet());
        if (!collect1.isEmpty()) {
            StringBuilder a = new StringBuilder();
            collect1.forEach(e -> {
                a.append(e.getConcatenatedCode()).append(",");
            });
            return Result.error("请先将[" + a + "]解除共享!");
        }
        super.removeByIds(dto.getIds());
        Map<Long, List<VirtualOrganDeviceRelativeEntity>> collect = deviceRelativeEntityList.stream()
                .collect(Collectors.groupingBy(VirtualOrganDeviceRelativeEntity::getVirtualOrganId));
        collect.forEach((k, v) -> {
            List<VirtualOrganDeviceRelativeEntity> list = super.list(new LambdaQueryWrapper<VirtualOrganDeviceRelativeEntity>()
                    .eq(VirtualOrganDeviceRelativeEntity::getVirtualOrganId, k));
            //同步绑定信息
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("id", k.toString());
            List<Map<String, Object>> deviceList = new ArrayList<>();
            if (!list.isEmpty()) {
                List<OpsDeviceChannelEntity> deviceEntities = opsDeviceChannelService.list(
                        new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                                .in(OpsDeviceChannelEntity::getChannelId, list.stream()
                                        .map(VirtualOrganDeviceRelativeEntity::getChannelId)
                                        .collect(Collectors.toSet()))
                );
                Map<String, String> deviceCodeMap = deviceEntities.stream()
                        .collect(Collectors.toMap(OpsDeviceChannelEntity::getChannelId, e -> {
                            if (StringUtil.isNotEmpty(e.getBid())) {
                                return e.getBid();
                            } else {
                                return e.getChannelId();
                            }
                        }));
                Map<String, String> deviceSNMap = deviceEntities.stream()
                        .collect(Collectors.toMap(OpsDeviceChannelEntity::getChannelId, OpsDeviceChannelEntity::getDeviceSn));
                if (CollectionUtil.isNotEmpty(list)) {
                    Map<Long, VirtualOrganTreeEntity> virtualOrganTreeMap = virtualOrganTreeService.listByIds(list.stream()
                                    .map(VirtualOrganDeviceRelativeEntity::getVirtualOrganId).collect(Collectors.toSet()))
                            .stream().collect(Collectors.toMap(VirtualOrganTreeEntity::getId, e -> e));
                    for (VirtualOrganDeviceRelativeEntity virtualOrganDeviceRelative : list) {
                        Map<String, Object> deviceMap = new HashMap<>();
                        deviceMap.put("id", String.valueOf(virtualOrganDeviceRelative.getId()));
                        deviceMap.put("treeId", virtualOrganTreeMap
                                .getOrDefault(virtualOrganDeviceRelative.getVirtualOrganId()
                                        , new VirtualOrganTreeEntity()).getOrganCode());
                        deviceMap.put("sn", deviceCodeMap.get(virtualOrganDeviceRelative.getChannelId()));
                        deviceMap.put("deviceId", deviceSNMap.get(virtualOrganDeviceRelative.getChannelId()));
                        deviceMap.put("gbConcatenatedCode", virtualOrganDeviceRelative.getConcatenatedCode());
                        deviceMap.put("1400ConcatenatedCode", virtualOrganDeviceRelative.getConcatenatedCode());

                        Long deviceId = virtualOrganDeviceRelative.getDeviceId();
                        DeviceEntity deviceEntity = deviceService.getById(deviceId);
                        if (deviceEntity != null) {
                            deviceMap.put("longitude", deviceEntity.getLongitude() == null ? "0.0" : deviceEntity.getLongitude());
                            deviceMap.put("latitude", deviceEntity.getLatitude() == null ? "0.0" : deviceEntity.getLatitude());
                        }
                        deviceList.add(deviceMap);
                    }
                }
            }
            dataMap.put("deviceInfos", deviceList);
            VirtualOrganTreeMessage virtualOrganTreeMessage = new VirtualOrganTreeMessage();
            virtualOrganTreeMessage.setMessageType(3);
            virtualOrganTreeMessage.setData(dataMap);
            virtualOrganTreeProduce.sync(virtualOrganTreeMessage);
        });
        return Result.ok();


    }

    @Override
    public Result updateConcatenatedCode(VirtualOrganDeviceRelativeParams entity) {
        if (entity.getId() == null) {
            return Result.error("id不可为空");
        }
        VirtualOrganDeviceRelativeEntity virtualOrganDeviceRelativeEntity = baseMapper.selectOne(
                new LambdaQueryWrapper<VirtualOrganDeviceRelativeEntity>()
                        .eq(VirtualOrganDeviceRelativeEntity::getConcatenatedCode,
                                entity.getConcatenatedCode()).last("limit 1"));
        if (virtualOrganDeviceRelativeEntity != null && !entity.getId().equals(virtualOrganDeviceRelativeEntity.getId())) {
            return Result.error("级联码已存在，请重新输入");
        }
        VirtualOrganDeviceRelativeEntity organDeviceRelativeEntity = this.getById(entity.getId());
        VirtualOrganTreeEntity treeEntity = virtualOrganTreeService.getById(organDeviceRelativeEntity.getVirtualOrganId());
        if (treeEntity == null) {
            return Result.error("虚拟组织不存在");
        }
        organDeviceRelativeEntity.setConcatenatedCode(entity.getConcatenatedCode());
        this.updateById(organDeviceRelativeEntity);
        //同步绑定信息
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("id", organDeviceRelativeEntity.getId().toString());
        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getById(organDeviceRelativeEntity.getChannelId());
        if (opsDeviceChannelEntity != null && opsDeviceChannelEntity.getId() != null) {
            List<Map<String, Object>> deviceList = new ArrayList<>();
            Map<String, Object> deviceMap = new HashMap<>();
            deviceMap.put("id", String.valueOf(opsDeviceChannelEntity.getId()));
            deviceMap.put("treeId", treeEntity.getOrganCode());
            deviceMap.put("sn", opsDeviceChannelEntity.getBid());
            deviceMap.put("gbConcatenatedCode", organDeviceRelativeEntity.getConcatenatedCode());
            deviceMap.put("gbConcatenatedName", organDeviceRelativeEntity.getConcatenatedGbName());
            deviceMap.put("1400ConcatenatedCode", organDeviceRelativeEntity.getConcatenatedCode());

            Long deviceId = organDeviceRelativeEntity.getDeviceId();
            DeviceEntity deviceEntity = deviceService.getById(deviceId);
            if (deviceEntity != null) {
                deviceMap.put("longitude", deviceEntity.getLongitude() == null ? "0.0" : deviceEntity.getLongitude());
                deviceMap.put("latitude", deviceEntity.getLatitude() == null ? "0.0" : deviceEntity.getLatitude());
            }
            deviceList.add(deviceMap);
            dataMap.put("deviceInfos", deviceList);
            VirtualOrganTreeMessage virtualOrganTreeMessage = new VirtualOrganTreeMessage();
            virtualOrganTreeMessage.setMessageType(3);
            virtualOrganTreeMessage.setData(dataMap);
            virtualOrganTreeProduce.sync(virtualOrganTreeMessage);
        }
        return Result.ok("修改成功");
    }

    @Override
    public Result updateConcatenatedName(VirtualOrganDeviceRelativeParams entity) {
        if (entity.getId() == null) {
            return Result.error("id不可为空");
        }
        if (StringUtil.isEmpty(entity.getConcatenatedGbName())) {
            return Result.error("级联名称不可为空");
        }
        VirtualOrganDeviceRelativeEntity organDeviceRelativeEntity = this.getById(entity.getId());
        VirtualOrganTreeEntity treeEntity = virtualOrganTreeService.getById(organDeviceRelativeEntity.getVirtualOrganId());
        if (treeEntity == null) {
            return Result.error("虚拟组织不存在");
        }
        organDeviceRelativeEntity.setConcatenatedGbName(entity.getConcatenatedGbName());
        this.updateById(organDeviceRelativeEntity);
        //同步绑定信息
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("id", organDeviceRelativeEntity.getId().toString());
        OpsDeviceChannelEntity opsDeviceChannelEntity = opsDeviceChannelService.getById(organDeviceRelativeEntity.getChannelId());
        if (opsDeviceChannelEntity != null && opsDeviceChannelEntity.getId() != null) {
            List<Map<String, Object>> deviceList = new ArrayList<>();
            Map<String, Object> deviceMap = new HashMap<>();
            deviceMap.put("id", String.valueOf(opsDeviceChannelEntity.getId()));
            deviceMap.put("treeId", treeEntity.getOrganCode());
            deviceMap.put("sn", opsDeviceChannelEntity.getBid());
            deviceMap.put("gbConcatenatedCode", organDeviceRelativeEntity.getConcatenatedCode());
            deviceMap.put("gbConcatenatedName", organDeviceRelativeEntity.getConcatenatedGbName());
            deviceMap.put("1400ConcatenatedCode", organDeviceRelativeEntity.getConcatenatedCode());

            Long deviceId = organDeviceRelativeEntity.getDeviceId();
            DeviceEntity deviceEntity = deviceService.getById(deviceId);
            if (deviceEntity != null) {
                deviceMap.put("longitude", deviceEntity.getLongitude() == null ? "0.0" : deviceEntity.getLongitude());
                deviceMap.put("latitude", deviceEntity.getLatitude() == null ? "0.0" : deviceEntity.getLatitude());
            }
            deviceList.add(deviceMap);
            dataMap.put("deviceInfos", deviceList);
            VirtualOrganTreeMessage virtualOrganTreeMessage = new VirtualOrganTreeMessage();
            virtualOrganTreeMessage.setMessageType(3);
            virtualOrganTreeMessage.setData(dataMap);
            virtualOrganTreeProduce.sync(virtualOrganTreeMessage);
        }
        return Result.ok("修改成功");
    }

    /**
     * 生成级联码
     */
    private List<String> getConcatenatedCode(VirtualOrganTreeParam virtualOrganTree, Integer num) {
        List<String> list = new ArrayList<>();
        String concatenatedCodePrefix = virtualOrganTree.getConcatenatedCodePrefix();
        if (StringUtil.isEmpty(concatenatedCodePrefix)) {
            String organCode = virtualOrganTree.getOrganCode();
            int length = organCode.length();
            if (length > 8) {
                organCode = organCode.substring(0, 8);
            } else {
                organCode = StringUtils.rightPad(organCode.substring(0, length), 8, '0');
            }
            concatenatedCodePrefix = organCode + virtualOrganTree.getIndustryType() + virtualOrganTree.getCodeType() + virtualOrganTree.getNetworkIdentification();
        }
//        else {
//            concatenatedCodePrefix = concatenatedCodePrefix + virtualOrganTree.getIndustryType() + virtualOrganTree.getCodeType() + virtualOrganTree.getNetworkIdentification();
//        }
        List<String> codeList = baseMapper.getConcatenatedCode(concatenatedCodePrefix);
        Integer max = 0;
        if (CollectionUtil.isNotEmpty(codeList)) {
            for (String code : codeList) {
                int i = Integer.parseInt(code.substring(14));
                if (max < i) {
                    max = i;
                }
            }
        }
        for (int i = 1; i <= num; i++) {
            list.add(concatenatedCodePrefix + StringUtils.leftPad(String.valueOf(max + i), 6, '0'));
        }
        return list;
    }

}