package com.saida.services.system.rocketMq.nodev1;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.common.mq.message.HostResourceUsageMessage;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.OpsHostDataEntity;
import com.saida.services.converge.entity.OpsResourceUsageEntity;
import com.saida.services.converge.entity.OpsResourceUsageRecordEntity;
import com.saida.services.system.ops.service.OpsHostDataService;
import com.saida.services.system.ops.service.OpsResourceUsageRecordService;
import com.saida.services.system.ops.service.OpsResourceUsageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Date;

@Slf4j
@Component

public class HostResourceUsageMessageListener implements VLinkerMqMessageListener {

    @Autowired
    private OpsHostDataService opsHostDataService;
    @Autowired
    private OpsResourceUsageService opsResourceUsageService;
    @Autowired
    private OpsResourceUsageRecordService opsResourceUsageRecordService;





    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("host_resource_usage")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        HostResourceUsageMessage message = JSON.parseObject(messageBody, HostResourceUsageMessage.class);
        handleMessage(message);
    }


    protected void handleMessage(HostResourceUsageMessage message) {
        // 此时这里才是最终的业务处理，代码只需要处理资源类关闭异常，其他的可以交给父类重试
        if (StringUtil.isEmpty(message.getMac())) {
            return;
        }
        log.info("[mq:host_resource_usage] 收到消息message:{}", message);
        OpsHostDataEntity one = opsHostDataService.getOne(new LambdaQueryWrapper<OpsHostDataEntity>()
                .eq(OpsHostDataEntity::getMac, message.getMac()));
        if (one == null) {
            one = new OpsHostDataEntity();
            BeanUtils.copyProperties(message, one);
            one.setCreateTime(new Date());
            one.setUpdateTime(new Date());
            one.setIntranetIpAddress(message.getIntranetIPAddress());
            one.setPublicIpAddress(message.getPublicIPAddress());
            opsHostDataService.save(one);
        } else {
            BeanUtils.copyProperties(message, one);
            one.setUpdateTime(new Date());
            one.setIntranetIpAddress(message.getIntranetIPAddress());
            one.setPublicIpAddress(message.getPublicIPAddress());
            opsHostDataService.updateById(one);
        }
        OpsResourceUsageEntity usageEntity = opsResourceUsageService.getOne(new LambdaQueryWrapper<OpsResourceUsageEntity>()
                .eq(OpsResourceUsageEntity::getHostId, one.getId()));
        if (usageEntity == null) {
            usageEntity = new OpsResourceUsageEntity();
            usageEntity.setCreateTime(new Date());
            usageEntity.setUpdateTime(new Date());
        }
        BeanUtils.copyProperties(message, usageEntity);
        usageEntity.setCreateTime(new Date());
        usageEntity.setUpdateTime(new Date());
        usageEntity.setHostId(one.getId());
        opsResourceUsageService.saveOrUpdate(usageEntity);

        OpsResourceUsageRecordEntity recordEntity = new OpsResourceUsageRecordEntity();
        BeanUtils.copyProperties(usageEntity, recordEntity);
        recordEntity.setId(null);
        opsResourceUsageRecordService.save(recordEntity);
    }
}

