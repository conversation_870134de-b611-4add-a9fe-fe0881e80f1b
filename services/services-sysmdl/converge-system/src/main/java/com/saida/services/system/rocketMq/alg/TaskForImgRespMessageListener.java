package com.saida.services.system.rocketMq.alg;

import com.alibaba.fastjson.JSON;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.system.rocketMq.message.TaskForImgRespMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * 视频流地址监听更新
 */
@Slf4j
@Component

public class TaskForImgRespMessageListener implements VLinkerMqMessageListener {


    @Autowired
    private RedisUtil redisUtil;


    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("task_for_img_resp")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        onMessage(messageBody);
    }

    public void onMessage(String message) {
        log.info("[mq:task_for_img_resp]接收到消息：{}", message);
        TaskForImgRespMessage forImgRespMessage = JSON.parseObject(message, TaskForImgRespMessage.class);
        if (forImgRespMessage.getUuid() == null) {
            log.error("[mq:task_for_img_resp] 没有任务id");
            return;
        }

        //5分钟
        redisUtil.set("algorithm:task_for_img_resp:" + forImgRespMessage.getUuid(), message, 60 * 5, TimeUnit.SECONDS);

    }
}

