package com.saida.services.system.utils;

/**
 * description your class purpose
 *
 * <AUTHOR>
 * @version RedisCacheUtil v1.0.0
 * @since 2025/6/26 16:08
 */

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Redis 缓存工具类（支持泛型、支持 LocalDateTime）
 */
@Component
@Slf4j
public class RedisCacheUtil {

    private final RedisTemplate<String, String> redisTemplate;
    private final ObjectMapper objectMapper;

    public RedisCacheUtil(RedisTemplate<String, String> redisTemplate, ObjectMapper objectMapper) {
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
    }

    /**
     * 设置缓存（单个对象）
     *
     * @param key      Redis Key
     * @param hashKey  Hash Key
     * @param value    要缓存的对象
     * @param timeout  过期时间（单位：秒）
     */
    public <T> void setHashValue(String key, String hashKey, T value, long timeout) {
        try {
            String json = objectMapper.writeValueAsString(value);
            redisTemplate.opsForHash().put(key, hashKey, json);
            redisTemplate.expire(key, timeout, TimeUnit.SECONDS);
        } catch (JsonProcessingException e) {
            log.error("Redis 缓存对象失败，key: {}, hashKey: {}", key, hashKey, e);
        }
    }

    /**
     * 获取缓存（单个对象）
     *
     * @param key      Redis Key
     * @param hashKey  Hash Key
     * @param clazz    目标类类型
     * @return 解析后的对象
     */
    public <T> T getHashValue(String key, String hashKey, Class<T> clazz) {
        Object json = redisTemplate.opsForHash().get(key, hashKey);
        if (json == null) {
            return null;
        }
        try {
            return objectMapper.readValue(json.toString(), clazz);
        } catch (JsonProcessingException e) {
            log.error("Redis 反序列化对象失败，key: {}, hashKey: {}", key, hashKey, e);
            return null;
        }
    }

    /**
     * 设置缓存（List 类型）
     *
     * @param key      Redis Key
     * @param hashKey  Hash Key
     * @param list     要缓存的 List 对象
     * @param timeout  过期时间（单位：秒）
     */
    public <T> void setHashListValue(String key, String hashKey, List<T> list, long timeout) {
        try {
            String json = objectMapper.writeValueAsString(list);
            redisTemplate.opsForHash().put(key, hashKey, json);
            redisTemplate.expire(key, timeout, TimeUnit.SECONDS);
        } catch (JsonProcessingException e) {
            log.error("Redis 缓存 List 对象失败，key: {}, hashKey: {}", key, hashKey, e);
        }
    }


    /**
     * 获取缓存（List 类型）
     *
     * @param key      Redis Key
     * @param hashKey  Hash Key
     * @param clazz    泛型类类型
     * @return 解析后的 List 对象
     */
    public <T> List<T> getHashListValue(String key, String hashKey, Class<T> clazz) {
        Object json = redisTemplate.opsForHash().get(key, hashKey);
        if (json == null) {
            return null;
        }
        try {
            return objectMapper.readValue(
                    json.toString(),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, clazz)
            );
        } catch (JsonProcessingException e) {
            log.error("Redis 反序列化 List 对象失败，key: {}, hashKey: {}", key, hashKey, e);
            return null;
        }
    }

    /**
     * 删除缓存
     */
    public void delete(String key) {
        redisTemplate.delete(key);
    }

    /**
     * 判断某个 hashKey 是否存在
     */
    public boolean hasKey(String key, String hashKey) {
        return Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(key, hashKey));
    }
}