package com.saida.services.system.rocketMq.message;

import com.saida.services.common.mq.vlinker.BaseMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AlgRoomPushDataMessage extends BaseMessage {
    private Long deviceId;

    private String streamToken;

    private String streamReplyForPlayerStr;
}
