package com.saida.services.system.algorithm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.saida.services.algorithm.dto.AlgorithmParamField;
import com.saida.services.algorithm.entity.AlgorithmManageEntity;
import com.saida.services.algorithm.entity.AlgorithmMappingEntity;
import com.saida.services.algorithm.entity.ThirdPartyDeviceEntity;
import com.saida.services.algorithm.enums.AlgorithmMappingSourceEnum;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.common.vo.BaseEnumVo;
import com.saida.services.enums.AlgAlgorithmSourceEnum;
import com.saida.services.enums.TerminalBoxTypeEnum;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.system.algorithm.dto.AlgorithmParam;
import com.saida.services.system.algorithm.dto.MappingAlgorithmsDto;
import com.saida.services.system.algorithm.dto.SaveConfigParamListDto;
import com.saida.services.system.algorithm.mapper.AlgorithmManageMapper;
import com.saida.services.system.algorithm.service.AlgorithmManageService;
import com.saida.services.system.algorithm.service.AlgorithmMappingService;
import com.saida.services.system.analyse.pojo.entity.AnalyseTaskEntity;
import com.saida.services.system.analyse.service.AnalyseTaskService;
import com.saida.services.system.device.entity.CloudServerEntity;
import com.saida.services.system.device.service.CloudServerService;
import com.saida.services.system.rocketMq.config.RocketMqTopic;
import com.saida.services.system.rocketMq.config.TaskMessageProducer;
import com.saida.services.system.rocketMq.message.TaskForImgReqMessage;
import com.saida.services.system.rocketMq.message.TaskForImgRespMessage;
import com.saida.services.system.sys.entity.AttributeDetailEntity;
import com.saida.services.system.sys.mapper.AttributeDetailMapper;
import com.saida.services.system.sys.service.AttributeDetailService;
import com.saida.services.system.sys.service.ThirdPartyDeviceService;
import com.saida.services.tools.attr.AttrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.async.DeferredResult;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service("algorithmManageService")
public class AlgorithmManageServiceImpl extends ServiceImpl<AlgorithmManageMapper, AlgorithmManageEntity> implements AlgorithmManageService {

    @Resource
    private AttributeDetailService attributeDetailService;
    @Resource
    private AnalyseTaskService analyseTaskService;
    @Resource
    private ThirdPartyDeviceService thirdPartyDeviceService;
    @Resource
    private AlgorithmMappingService algorithmMappingService;
    @Resource
    private CloudServerService cloudServerService;
    @Resource
    private DataSourceTransactionManager dataSourceTransactionManager;
    @Resource
    private TransactionDefinition transactionDefinition;
    @Resource
    private AttributeDetailMapper attributeDetailMapper;


    @Override
    public Result addOrUpdate(AlgorithmManageEntity entity) {
        if (entity.getId() == null) {
            String name = entity.getName();
            if (StringUtil.isEmpty(name)) {
                return Result.error("算法名称必填");
            }
//            Pattern compile = Pattern.compile(".*[[ _`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“'。，、？]|\\n|\\r|\\t].*");
//            Matcher matcher = compile.matcher(name);
//            if (matcher.matches()) {
//                return Result.error("算法名称只能包含字母、数字和中文！");
//            }
            AlgorithmManageEntity alg = getOne(new LambdaQueryWrapper<AlgorithmManageEntity>().eq(AlgorithmManageEntity::getName, entity.getName()), false);
            if (alg != null) {
                throw new BizRuntimeException(String.format("算法 %s 已存在", entity.getName()));
            }
            long id = IdUtil.getSnowflakeNextId();
            entity.setId(id);
            entity.setCode(String.valueOf(id));
            entity.setCreateUser(JwtUtil.getUserId());
            entity.setCreateTime(DateTime.now());
            entity.setParam(null);
            this.setAlgorithmConfigParam(entity);

            save(entity);
        } else {
            // 算法名称不可编辑
            entity.setName(null);
            entity.setUpdateUser(JwtUtil.getUserId());
            entity.setUpdateTime(DateTime.now());
            entity.setParam(null);
            updateById(entity);
        }
        return Result.ok();
    }

    @Override
    public IPage<AlgorithmManageEntity> listPage(AlgorithmManageEntity entity) {
        IPage<AlgorithmManageEntity> page = baseMapper.selectPage(new Page<>(entity.getPageNum(), entity.getPageSize()),
                new LambdaQueryWrapper<AlgorithmManageEntity>()
                        .like(!StringUtil.isEmpty(entity.getName()), AlgorithmManageEntity::getName, entity.getName())
                        .eq(entity.getCategory() != null, AlgorithmManageEntity::getCategory, entity.getCategory())
                        .eq(entity.getAlgorithmCategory() != null, AlgorithmManageEntity::getAlgorithmCategory, entity.getAlgorithmCategory())
                        .eq(entity.getStatus() != null, AlgorithmManageEntity::getStatus, entity.getStatus())
                        .orderByDesc(AlgorithmManageEntity::getId)
        );
        if (page == null || page.getRecords() == null || page.getRecords().isEmpty()) {
            return page;
        }
        fillAttr(page.getRecords());
        return page;
    }

    @Override
    public DtoResult<List<AlgorithmManageEntity>> list(AlgorithmManageEntity entity) {
        List<AlgorithmManageEntity> allList = this.list();
        List<AlgorithmManageEntity> updateAlgorithmManageEntityList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(allList)) {
            for (AlgorithmManageEntity algorithmManageEntity : allList) {
                if (CollectionUtil.isEmpty(algorithmManageEntity.getConfigParam())) {
                    this.setAlgorithmConfigParam(algorithmManageEntity);

                    AlgorithmManageEntity updateAlgorithmManageEntity = new AlgorithmManageEntity();
                    updateAlgorithmManageEntity.setId(algorithmManageEntity.getId());
                    updateAlgorithmManageEntity.setConfigParam(algorithmManageEntity.getConfigParam());
                    updateAlgorithmManageEntityList.add(updateAlgorithmManageEntity);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(updateAlgorithmManageEntityList)) {
            // 手动开启事务
            TransactionStatus transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
            // 更新算法配置参数
            this.updateBatchById(updateAlgorithmManageEntityList);
            // 手动提交事务
            dataSourceTransactionManager.commit(transactionStatus);
        }

        List<AlgorithmManageEntity> list = this.list(new LambdaQueryWrapper<AlgorithmManageEntity>()
                .like(StringUtil.isNotEmpty(entity.getName()), AlgorithmManageEntity::getName, entity.getName())
                .eq(entity.getCategory() != null, AlgorithmManageEntity::getCategory, entity.getCategory())
                .eq(entity.getAlgorithmCategory() != null, AlgorithmManageEntity::getAlgorithmCategory, entity.getAlgorithmCategory())
                .eq(entity.getStatus() != null, AlgorithmManageEntity::getStatus, entity.getStatus()));
        return DtoResult.ok(list);
    }

    @Override
    public DtoResult<List<AlgorithmManageEntity>> getAlgorithmManageListByCloudServerId(Long cloudServerId, Integer status) {
        List<AlgorithmMappingEntity> algorithmMappingEntityList = algorithmMappingService.list(new LambdaQueryWrapper<AlgorithmMappingEntity>()
                .eq(cloudServerId != null, AlgorithmMappingEntity::getSourceId, cloudServerId));
        if (CollectionUtil.isEmpty(algorithmMappingEntityList)) {
            return DtoResult.ok(new ArrayList<>());
        }
        return DtoResult.ok(this.list(new LambdaQueryWrapper<AlgorithmManageEntity>()
                .in(AlgorithmManageEntity::getId, algorithmMappingEntityList.stream().map(AlgorithmMappingEntity::getAlgorithmId).collect(Collectors.toSet()))
                .eq(status != null, AlgorithmManageEntity::getStatus, status)
        ));
    }

    @Override
    public AlgorithmManageEntity getInfo(Long id) {
        AlgorithmManageEntity info = baseMapper.selectById(id);
        if (info == null) {
            throw new BizRuntimeException("算法不存在");
        }
        return fillAttr(new ArrayList<AlgorithmManageEntity>() {{
            add(info);
        }}).get(0);
    }

    /**
     * 删除算法信息。
     *
     * @param id 算法ID。
     * @throws BizRuntimeException 如果算法不存在或该算法已创建任务，则抛出业务运行时异常。
     */
    @Override
    public void delete(Long id) {
        // 根据ID查询算法信息
        AlgorithmManageEntity info = baseMapper.selectById(id);
        // 如果算法信息不存在，则抛出异常
        if (info == null) {
            throw new BizRuntimeException("算法不存在");
        }
        // 查询是否有任务使用了该算法，如果有，则抛出异常
        long count = analyseTaskService.count(new LambdaQueryWrapper<AnalyseTaskEntity>().eq(AnalyseTaskEntity::getAlgorithmId, id));
        if (count > 0) {
            throw new BizRuntimeException("该算法已创建任务，无法删除");
        }
        // 查询第三方设备中订阅了该算法的设备
        List<ThirdPartyDeviceEntity> thirdDevices = thirdPartyDeviceService.list(new LambdaQueryWrapper<ThirdPartyDeviceEntity>().like(ThirdPartyDeviceEntity::getSubscribe, String.valueOf(id)));
        // 如果有订阅了该算法的第三方设备，则更新设备的订阅列表
        if (thirdDevices != null && !thirdDevices.isEmpty()) {
            for (ThirdPartyDeviceEntity td : thirdDevices) {
                // 如果设备没有订阅该算法或订阅列表中不包含该算法ID，则跳过
                if (StringUtil.isEmpty(td.getSubscribe()) || !td.getSubscribe().contains(String.valueOf(id))) {
                    continue;
                }
                // 将设备订阅列表中的该算法ID移除
                List<String> subs = Lists.newArrayList(td.getSubscribe().split(","));
                subs.remove(String.valueOf(id));
                td.setSubscribe(String.join(",", subs));
            }
            // 批量更新第三方设备的订阅列表
            thirdPartyDeviceService.updateBatchById(thirdDevices);
        }
        // 删除算法信息
        removeById(id);
    }


    /**
     * 保存算法参数。
     * 该方法用于更新已存在的算法参数。如果算法不存在，则抛出异常；如果参数字段为空，则初始化为空列表。
     * 使用JSON序列化将参数字段列表转换为字符串，然后更新算法参数的数据库记录。
     *
     * @param param 算法参数对象，包含待更新的参数信息。
     * @throws BizRuntimeException 如果算法不存在，则抛出此异常。
     */
    @Override
    public void saveParam(AlgorithmParam param) {
        // 根据参数ID查询算法实体，检查算法是否存在
        AlgorithmManageEntity algorithm = getById(param.getId());
        if (algorithm == null) {
            throw new BizRuntimeException("算法不存在");
        }
        // 使用Lambda表达式构建更新wrapper，设置算法参数为序列化后的字段列表，然后根据ID进行更新
        update(new LambdaUpdateWrapper<AlgorithmManageEntity>()
                .set(AlgorithmManageEntity::getParam, CollectionUtil.isEmpty(param.getFields()) ? null : JSON.toJSONString(param.getFields()))
                .eq(AlgorithmManageEntity::getId, param.getId()));
    }

    @Override
    public DtoResult<Void> saveConfigParam(SaveConfigParamListDto param) {
        List<SaveConfigParamListDto.ConfigParamInfo> dataList = param.getDataList();
        if (CollectionUtil.isEmpty(dataList)) {
            return DtoResult.error("参数不能为空");
        }
        List<AlgorithmManageEntity> algorithmManageEntityList = this.list();

        List<AlgorithmManageEntity> updateSaveAlgorithmManageEntityList = new ArrayList<>();
        for (SaveConfigParamListDto.ConfigParamInfo configParamInfo : dataList) {
            // 根据参数ID查询算法实体，检查算法是否存在
            algorithmManageEntityList.stream().filter(t1 -> Objects.equals(t1.getId(), configParamInfo.getId())).findFirst().ifPresent((algorithmManageEntity) -> {
                AlgorithmManageEntity updateAlgorithmManageEntity = new AlgorithmManageEntity();
                updateAlgorithmManageEntity.setId(configParamInfo.getId());
                updateAlgorithmManageEntity.setParam(configParamInfo.getParamList());
                updateAlgorithmManageEntity.setConfigParam(configParamInfo.getConfigParamList());
                updateSaveAlgorithmManageEntityList.add(updateAlgorithmManageEntity);
            });
        }
        if (CollectionUtil.isNotEmpty(updateSaveAlgorithmManageEntityList)) {
            this.updateBatchById(updateSaveAlgorithmManageEntityList);
        }
        return DtoResult.ok();
    }

    /**
     * 根据算法ID获取算法参数字段列表。
     * 此方法首先尝试根据提供的ID从数据库中获取算法实体。如果算法实体不存在，则抛出一个业务运行时异常。
     * 如果算法实体存在，但其参数字段为空，方法将默认初始化一个空的算法参数列表，并以JSON格式存储。
     * 最后，方法将解析存储的JSON字符串，将其转换为算法参数字段的列表，并返回该列表。
     *
     * @param id 算法的唯一标识符。
     * @return 算法参数字段的列表。
     * @throws BizRuntimeException 如果算法实体不存在，则抛出此异常。
     */
    @Override
    public List<AlgorithmParamField> getParam(Long id) {
        // 根据ID获取算法实体
        AlgorithmManageEntity algorithm = getById(id);
        // 检查算法实体是否存在，如果不存在则抛出异常
        if (algorithm == null) {
            throw new BizRuntimeException("算法不存在");
        }
        return algorithm.getParam();
    }

    @Override
    public List<AlgorithmParamField> getConfigParamList(Long id) {
        AlgorithmManageEntity algorithmManageEntity = Optional.ofNullable(this.getById(id)).orElseThrow(() -> new BizRuntimeException("该算法不存在！"));
        return algorithmManageEntity.getConfigParam();
    }

    /**
     * 管理算法仓库，根据条件查询算法管理实体列表。
     *
     * @param entity 查询条件实体，包含算法的状态、名称、类别、来源和应用场景等信息。
     * @return 返回符合条件的算法管理实体列表，列表中的实体可能已经被填充了额外的属性。
     */
    @Override
    public List<AlgorithmManageEntity> algWarehouse(AlgorithmManageEntity entity) {
        // 根据条件查询算法管理实体列表
        List<AlgorithmManageEntity> list = list(
                new LambdaQueryWrapper<AlgorithmManageEntity>()
                        .eq(AlgorithmManageEntity::getStatus, 1) // 查询状态为1的算法
                        .like(!StringUtil.isEmpty(entity.getName()), AlgorithmManageEntity::getName, entity.getName()) // 模糊查询算法名称
                        .eq(entity.getCategory() != null, AlgorithmManageEntity::getCategory, entity.getCategory()) // 精确查询算法类别
                        .eq(entity.getAlgorithmCategory() != null, AlgorithmManageEntity::getAlgorithmCategory, entity.getAlgorithmCategory()) // 精确查询算法类别
                        .eq(entity.getIsShow() != null, AlgorithmManageEntity::getIsShow, entity.getIsShow()) // 精确查询算法类别
                        .like(!StringUtil.isEmpty(entity.getSource()), AlgorithmManageEntity::getSource, entity.getSource()) // 模糊查询算法来源
                        .like(!StringUtil.isEmpty(entity.getScene()), AlgorithmManageEntity::getScene, entity.getScene()) // 模糊查询算法应用场景
        );

        // 对查询结果进行额外属性填充后返回
        return fillAttr(list);
    }


    /**
     * 获取算法源列表。
     * 该方法用于聚合不同来源的算法源选项，包括云服务、边缘终端和端侧摄像机。
     *
     * @return Result 对象，包含算法源的列表。
     */
    @Override
    public Result getAlgorithmSource() {
        // 初始化一个属性详情实体对象，用于后续设置属性类型和获取属性列表
        AttributeDetailEntity attributeDetailEntity = new AttributeDetailEntity();

        // 1. 获取云服务列表
        List<CloudServerEntity> cloudServerEntityList = cloudServerService.list();
        // 将云服务实体列表转换为BaseEnumVo列表，方便统一处理
        List<BaseEnumVo<String>> list = cloudServerEntityList.stream().map(t1 -> {
            BaseEnumVo<String> baseEnumVo = new BaseEnumVo<>();
            baseEnumVo.setLabel("[云]"+t1.getName());
            baseEnumVo.setValue(String.valueOf(t1.getId()));
            return baseEnumVo;
        }).collect(Collectors.toList());

        // 2. 设置属性类型为"terminal_box_model"，获取边缘终端-英特灵达的属性列表
        attributeDetailEntity.setAttrType("terminal_box_model");
        // 获取属性列表并转换为BaseEnumVo列表
        List<AttributeDetailEntity> attributeDetailEntityList = attributeDetailService.getList(attributeDetailEntity);
        list.addAll(attributeDetailEntityList.stream().map(t1 -> {
            BaseEnumVo<String> baseEnumVo = new BaseEnumVo<>();
            baseEnumVo.setLabel("[边]"+t1.getName());
            baseEnumVo.setValue(String.valueOf(t1.getId()));
            return baseEnumVo;
        }).collect(Collectors.toList()));

        TerminalBoxTypeEnum[] values = TerminalBoxTypeEnum.values();
        for (TerminalBoxTypeEnum value : values) {
            BaseEnumVo<String> baseEnumVo = new BaseEnumVo<>();
            baseEnumVo.setLabel("[边]"+value.getName());
            baseEnumVo.setValue(value.getLabel());
            list.add(baseEnumVo);
        }


        // 4. 添加端侧摄像机的选项，作为固定的算法源选项
        BaseEnumVo<String> baseEnumVo = new BaseEnumVo<>();
        baseEnumVo.setLabel("[端]"+AlgorithmMappingSourceEnum.CLOUD_BASED_CAMERA.getName());
        baseEnumVo.setValue(AlgorithmMappingSourceEnum.CLOUD_BASED_CAMERA.getCode());
        list.add(baseEnumVo);


        // 返回处理后的算法源列表
        return Result.ok(list);
    }

    @Override
    public Result info(String algorithmId) {
        List<AlgorithmMappingEntity> list = algorithmMappingService.list(new LambdaQueryWrapper<AlgorithmMappingEntity>()
                .eq(AlgorithmMappingEntity::getAlgorithmId, algorithmId)
                .orderByDesc(AlgorithmMappingEntity::getId)
        );
        return Result.ok(list);
    }

    /**
     * 保存或更新算法映射关系。
     *
     * @param dto 算法映射关系的输入DTO，包含需要映射的算法ID和映射实体列表。
     * @return 返回操作结果，成功返回ok，失败返回错误信息。
     * <p>
     * 此方法首先检查输入的映射实体列表中是否存在相同的源ID但名称或代码不同的情况，
     * 如果存在，则抛出异常，因为同源的算法名称和代码必须唯一。
     * 接着，它删除已存在的算法映射关系，并根据新的映射实体列表创建并保存新的映射关系。
     * 使用事务确保整个操作的原子性。
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result mapping(MappingAlgorithmsDto dto) {
        // 检查输入的算法映射实体列表是否为空
        List<AlgorithmMappingEntity> algorithmMappingEntityList = dto.getDataList();
        if (CollectionUtil.isNotEmpty(algorithmMappingEntityList)) {
            // 根据源ID分组，检查是否存在相同源ID但名称或代码不同的情况
            Map<String, List<AlgorithmMappingEntity>> map = algorithmMappingEntityList.stream()
                    .collect(Collectors.groupingBy(AlgorithmMappingEntity::getSourceId));
            map.forEach((k, v) -> {
                if (v.size() > 1) {
                    Map<String, List<AlgorithmMappingEntity>> theMap1 = v.stream()
                            .collect(Collectors.groupingBy(AlgorithmMappingEntity::getName));
                    Map<String, List<AlgorithmMappingEntity>> theMap2 = v.stream()
                            .collect(Collectors.groupingBy(AlgorithmMappingEntity::getCode));
                    if (theMap1.size() != v.size() || theMap2.size() != v.size()) {
                        throw new BizRuntimeException("同一种算法来源，算法名称和算法编码不能重复！");
                    }
                }
            });
        }

        // 检查算法ID是否为空
        Long algorithmId = dto.getAlgorithmId();
        if (null == algorithmId) {
            return Result.error("算法ID不能为空！");
        }
        // 删除已有的算法映射关系
        // 1.删除原来的映射关系 algorithms_mapping
        algorithmMappingService.remove(new LambdaUpdateWrapper<AlgorithmMappingEntity>().eq(AlgorithmMappingEntity::getAlgorithmId, algorithmId));

        // 创建并保存新的算法映射关系
        // 2.添加新的映射关系 algorithms_mapping
        List<AlgorithmMappingEntity> insertAlgorithmMappingEntityList = new ArrayList<>();
        for (AlgorithmMappingEntity algorithmMappingEntity : algorithmMappingEntityList) {
            AlgorithmMappingEntity insertAlgorithmMappingEntity = new AlgorithmMappingEntity();
            // 设置新的映射关系的属性
            insertAlgorithmMappingEntity.setAlgorithmId(algorithmId);
            insertAlgorithmMappingEntity.setSourceId(algorithmMappingEntity.getSourceId());
            insertAlgorithmMappingEntity.setName(algorithmMappingEntity.getName());
            insertAlgorithmMappingEntity.setCode(algorithmMappingEntity.getCode());
            insertAlgorithmMappingEntity.setMinCode(algorithmMappingEntity.getMinCode());
            insertAlgorithmMappingEntityList.add(insertAlgorithmMappingEntity);
        }
        // 如果有新的映射关系需要保存，则批量保存
        if (CollectionUtil.isNotEmpty(insertAlgorithmMappingEntityList)) {
            algorithmMappingService.saveBatch(insertAlgorithmMappingEntityList);
        }
        // 返回操作成功的结果
        return Result.ok();
    }

    @Resource
    private TaskMessageProducer taskMessageProducer;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public DeferredResult<Result> getShowResult(String algoName, String imgUrl) throws IOException {
//        File file = null;
//
//        // 如果 imgUrl 不为空，下载图片并保存为 File
//        if (imgUrl != null && !imgUrl.isEmpty()) {
//            file = downloadImageFromUrl(imgUrl);  // 下载图片并返回文件
//            if (file.length() > 10 * 1024 * 1024) {  // 判断文件大小是否超过 10MB
//                return Result.error("图片大小超过10MB，请上传更小的图片");  // 返回错误信息
//            }
//        } else {
//            // 如果 imgUrl 为空，使用上传的 multipartFile
//            file = convertMultipartFileToFile(multipartFile);  // 转换 MultipartFile 为 File
//            if (file.length() > 10 * 1024 * 1024) {  // 判断文件大小是否超过 10MB
//                return Result.error("图片大小超过10MB，请上传更小的图片");  // 返回错误信息
//            }
//        }
//
//        HashMap<String, Object> paramMap = new HashMap<>();
//        paramMap.put("algo_name", algoName);
//        paramMap.put("file", file);
//
//        Response resp = null;
//        try {
//            resp = OkHttpUtil.doPostFormFile(algorithmShow, algoName, file);  // 发起 HTTP 请求
//        } catch (IOException e) {
//            throw new RuntimeException(e);  // 捕获 IO 异常
//        }
//
//        String responseBody = resp.body().string();  // 获取响应体内容
//        ObjectMapper objectMapper = new ObjectMapper();
//        JsonNode rootNode = objectMapper.readTree(responseBody);  // 解析 JSON
//        // 处理返回的 "detail" 字段，如果是错误信息
//        if (rootNode.has("detail")) {
//            String detail = rootNode.path("detail").asText();
//            return Result.error("错误信息: " + detail);  // 如果存在 "detail" 字段，说明返回了错误信息
//        }
//
//        // 处理返回的成功数据（包含 resultText, resultImage 等字段）
//        if (rootNode.has("result_text") && rootNode.has("result_image")) {
//            String resultText = rootNode.path("result_text").asText();
//            resultText = resultText.replace("\\n", "\n");  // 去除转义符号
//
//            String resultImage = rootNode.path("result_image").asText();  // 获取 resultImage 字段
//
//            HashMap<String, String> resultMap = new HashMap<>();
//            resultMap.put("resultText", resultText);
//            resultMap.put("resultImage", resultImage);
//            resultMap.put("algoName", algoName);
//
//            return Result.ok(resultMap);  // 返回成功的结果
//        }
        //最多等待15s
        DeferredResult<Result> result = new DeferredResult<>(15 * 1000L);
        result.onTimeout(() -> {
            result.setResult(Result.error("执行超时"));
        });
        result.onError((e) -> {
            log.error("执行异常, e:", e);
            result.setResult(Result.error("执行异常"));
        });
        // 异步执行任务
        CompletableFuture.runAsync(() -> {
            try {
                Long uuid = IdWorker.getId();
                TaskForImgReqMessage taskForImgReqMessage = new TaskForImgReqMessage();
                taskForImgReqMessage.setUuid(uuid);
                taskForImgReqMessage.setUrl(imgUrl);
                taskForImgReqMessage.setAlgorithmName(algoName);
                taskMessageProducer.sendSync(RocketMqTopic.TASK_FOR_IMG, taskForImgReqMessage);
                String redisKey = "algorithm:task_for_img_resp:" + uuid;
                //堵塞查询这个redis是否有值 如果有说明任务执行完成
                while (true) {
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                    Object o = redisUtil.get(redisKey);
                    if (o != null) {
                        //删掉这个key
                        redisUtil.del(redisKey);
                        //任务结束了
                        TaskForImgRespMessage taskForImgRespMessage = JSON.parseObject(String.valueOf(o), TaskForImgRespMessage.class);
                        if (taskForImgRespMessage.getType() == 1) {
                            String analysisImgBase64 = taskForImgRespMessage.getBase64();

                            HashMap<String, String> resultMap = new HashMap<>();
                            resultMap.put("resultImage", analysisImgBase64);
                            resultMap.put("algoName", algoName);
                            result.setResult(Result.ok(resultMap));
                            return;
                        } else {
                            result.setResult(Result.error("任务识别失败"));
                            return;
                        }

                    }
                }

            } catch (Exception e) {
                log.info("识别异常, e:", e);
                result.setResult(Result.error("任务识别失败"));
            }
        });
        return result;
    }

    @Override
    public List<AlgorithmManageEntity> getListBySourceId(String sourceId) {
        return baseMapper.getListBySourceId(sourceId);
    }

    /**
     * 填充算法管理实体的属性信息。
     * 通过查询属性详情服务获取ID和名称的映射关系，进而为算法管理实体的场景和来源字段补充名称信息。
     * 对于场景和来源字段包含多个ID的情况，以逗号分隔的形式存储对应的名称。
     *
     * @param records 算法管理实体的列表。
     * @return 填充了属性信息的算法管理实体列表。
     */
    private List<AlgorithmManageEntity> fillAttr(List<AlgorithmManageEntity> records) {
        // 如果记录为空或为空列表，直接返回。
        if (records == null || records.isEmpty()) {
            return records;
        }
        // 获取所有属性ID和名称的映射关系
        Map<Long, String> idNameMap = attributeDetailService.getAllIdNameMap();

        // 初始化不同算法源的ID，用于后续根据ID获取对应的简称
        Long cloudSourceId = AlgAlgorithmSourceEnum.CLOUD_SERVICE.getDicId();
        Long terminalBoxId = AlgAlgorithmSourceEnum.TERMINAL_BOX.getDicId();
        Long cloudBasedCameraId = AlgAlgorithmSourceEnum.CLOUD_BASED_CAMERA.getDicId();

        // 创建一个字典映射，用于存储所有ID和名称的映射关系，包括后续处理中可能新增的映射
        Map<Object, Object> dicMap = new HashMap<>(idNameMap);
        for (AlgorithmManageEntity a : records) {
            // 处理场景ID，补充场景名称
            if (!StringUtil.isEmpty(a.getScene())) {
                for (String id : a.getScene().split(",")) {
                    // 如果场景名称为空，则直接设置；否则追加到已有的名称后面
                    if (StringUtil.isEmpty(a.getSceneNames())) {
                        a.setSceneNames(idNameMap.getOrDefault(Long.valueOf(id), null));
                    } else {
                        a.setSceneNames(String.format("%s,%s", a.getSceneNames(), idNameMap.getOrDefault(Long.valueOf(id), null)));
                    }
                }
            }
            // 处理来源ID，补充来源名称
            if (!StringUtil.isEmpty(a.getSource())) {
                List<String> shortSourceNameList = new ArrayList<>();
                for (String id : a.getSource().split(",")) {
                    // 根据ID对应的算法源，添加对应的简称到列表
                    // 云服务
                    if (Objects.equals(id, String.valueOf(cloudSourceId))) {
                        shortSourceNameList.add("云");
                    }
                    // 边缘盒子
                    if (Objects.equals(id, String.valueOf(terminalBoxId))) {
                        shortSourceNameList.add("边");
                    }
                    // 云化摄像机
                    if (Objects.equals(id, String.valueOf(cloudBasedCameraId))) {
                        shortSourceNameList.add("端");
                    }
                    // 如果来源名称为空，则直接设置；否则追加到已有的名称后面
                    if (StringUtil.isEmpty(a.getSourceNames())) {
                        a.setSourceNames(idNameMap.getOrDefault(Long.valueOf(id), null));
                    } else {
                        a.setSourceNames(String.format("%s,%s", a.getSourceNames(), idNameMap.getOrDefault(Long.valueOf(id), null)));
                    }
                }
                // 如果简称列表不为空，将简称合并为字符串，用逗号分隔
                if (CollectionUtil.isNotEmpty(shortSourceNameList)) {
                    a.setShortSourceNames(StringUtil.join(",", shortSourceNameList));
                }
            }
            //填充在线演示  选择模型字典
            //  a.setLabelName(attributeMapper.selectById(a.getAlgorithmCategory()).getName());

            //转换算法类别
            a.setAlgorithmCategoryName(idNameMap.get(a.getAlgorithmCategory()));
            a.setOnlineModelTypeName(idNameMap.get(a.getOnlineModelType()));
        }
        // 使用字典映射更新所有记录的属性信息
        records.replaceAll(o -> AttrUtil.putAttr(o, dicMap));
        return records;
    }

    private void setAlgorithmConfigParam(AlgorithmManageEntity algorithmManageEntity) {
        List<AlgorithmParamField> algorithmParamFieldList = new ArrayList<>();
        AlgorithmParamField algorithmParamField = new AlgorithmParamField();
        algorithmParamField.setName("置信度");
        algorithmParamField.setField("sensitivity");
        algorithmParamField.setFieldValue("50");
        algorithmParamField.setType("String");
        algorithmParamField.setUnit("%");
        algorithmParamFieldList.add(algorithmParamField);

        //之前的灵敏度是当置信度开发的 所以修改之前字段，同时添加灵敏度字段
        algorithmParamField = new AlgorithmParamField();
        algorithmParamField.setName("灵敏度");
        algorithmParamField.setField("confidence");
        algorithmParamField.setFieldValue("50");
        algorithmParamField.setType("String");
        algorithmParamField.setUnit("%");
        algorithmParamFieldList.add(algorithmParamField);

        algorithmParamField = new AlgorithmParamField();
        algorithmParamField.setName("容错帧率");
        algorithmParamField.setField("faultTolerantFrameRate");
        algorithmParamField.setFieldValue("50");
        algorithmParamField.setType("String");
        algorithmParamField.setUnit("%");
        algorithmParamFieldList.add(algorithmParamField);

        algorithmParamField = new AlgorithmParamField();
        algorithmParamField.setName("告警时长");
        algorithmParamField.setField("alarmDuration");
        algorithmParamField.setFieldValue("300");
        algorithmParamField.setType("String");
        algorithmParamField.setUnit("秒");
        algorithmParamFieldList.add(algorithmParamField);
        algorithmManageEntity.setConfigParam(algorithmParamFieldList);
    }


    // 将 MultipartFile 转换为 File 的方法
    private File convertMultipartFileToFile(MultipartFile multipartFile) throws IOException {
        // 创建临时文件
        File file = File.createTempFile("temp", multipartFile.getOriginalFilename());

        // 将 MultipartFile 的内容写入文件
        multipartFile.transferTo(file);

        // 设置程序退出时删除临时文件
        file.deleteOnExit();

        return file;
    }
}