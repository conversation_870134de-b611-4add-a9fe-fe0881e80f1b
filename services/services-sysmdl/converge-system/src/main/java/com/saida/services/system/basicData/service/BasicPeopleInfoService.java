package com.saida.services.system.basicData.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.basicData.dto.*;
import com.saida.services.system.basicData.entity.BasicPeopleInfoEntity;
import com.saida.services.system.basicData.vo.BasicPeoplePageQueryVo;
import com.saida.services.system.basicData.vo.PeopleFaceCompareVo;
import com.saida.services.system.basicData.vo.PeopleRelateVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 基础信息-人员信息表(BasicPeopleInfoEntity)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-05 15:20:01
 */
public interface BasicPeopleInfoService extends IService<BasicPeopleInfoEntity> {

    /**
     * 分页查询
     *
     * @param dto         筛选条件
     * @param pageRequest 分页对象
     * @return 查询结果
     */
    BasePageInfoEntity<BasicPeoplePageQueryVo> queryByPage(BasicPeoplePageQueryDto dto, BaseRequest pageRequest);

    /**
     * 新增数据
     *
     * @param dto 实例对象
     * @return 实例对象
     */
    DtoResult<Integer> insert(BasicPeopleAddDto dto);

    /**
     * 修改数据
     *
     * @param dto 实例对象
     * @return 实例对象
     */
    DtoResult<Integer> update(BasicPeopleEditDto dto);

    /**
     * 通过主键删除数据
     *
     * @param ids 主键
     * @return 是否成功
     */
    DtoResult<Integer> deleteById(List<String> ids);

    DtoResult faceCompare(MultipartFile imageLeft, MultipartFile imageRight);

    DtoResult<BasePageInfoEntity<PeopleFaceCompareVo>> faceSearch(MultipartFile image, List<Long> groupIds, double similarity, int pageSize, int pageNum);

    DtoResult<Boolean> updateGroups(BasicBatchUpdateGroupDto dto);

    DtoResult<Integer> addStrangerPhoto(BasicDefaultPeopleAddDto dto);

    PeopleRelateVo selectPeopleRelateInfo(Long peopleId, Long faceId);
}
