package com.saida.services.system.ops.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.common.tools.IdGenerateUtil;
import com.saida.services.common.tools.StringRedisUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.InPlatformEntity;
import com.saida.services.converge.entity.SignalNodeEntity;
import com.saida.services.converge.entity.dto.GbReSyncDto;
import com.saida.services.converge.entity.dto.GbSubscribesDto;
import com.saida.services.converge.entity.dto.InPlatformDto;
import com.saida.services.converge.entity.dto.InPlatformSyncDto;
import com.saida.services.converge.qxNode.resp.GbSyncResp;
import com.saida.services.common.mq.message.InOutPlatformMessage;
import com.saida.services.system.client.nodev1.QxNodeReqService;
import com.saida.services.system.ops.mapper.InPlatformMapper;
import com.saida.services.system.ops.service.InPlatformService;
import com.saida.services.system.ops.service.SignalNodeService;
import com.saida.services.system.rocketMq.nodev1.InOutPlatformProduce;
import com.saida.services.converge.entity.RegionEntity;
import com.saida.services.system.sys.service.RegionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Service("inPlatformService")
public class InPlatformServiceImpl extends ServiceImpl<InPlatformMapper, InPlatformEntity> implements InPlatformService {

    @Autowired
    private SignalNodeService signalNodeService;

    @Autowired
    private InOutPlatformProduce inOutPlatformProduce;

    @Autowired
    private RegionService regionService;

    @Autowired
    private StringRedisUtil redisUtil;

    @Autowired
    private QxNodeReqService qxNodeReqService;

    private void vAddOrEdit(InPlatformEntity inPlatformEntity){
        Long voId = inPlatformEntity.getVoId();
        if (null == voId) {
            throw new BizRuntimeException("虚拟组织ID不能为空");
        }
        String nodeId = inPlatformEntity.getNodeId();
        if (StringUtil.isEmpty(nodeId)) {
            throw new BizRuntimeException("边缘节点ID不能为空");
        }

        SignalNodeEntity node = signalNodeService.getById(nodeId);
        if (ObjectUtil.isEmpty(node)) {
            throw new BizRuntimeException("查无此边缘节点");
        }

        InPlatformEntity one = this.getOne(new LambdaQueryWrapper<InPlatformEntity>().eq(InPlatformEntity::getName, inPlatformEntity.getName()), false);
        //更新
        if (null != inPlatformEntity.getId() && inPlatformEntity.getId() > 0) {
            InPlatformEntity platform = this.getById(inPlatformEntity.getId());
            if (ObjectUtil.isEmpty(platform)) {
                throw new BizRuntimeException("查无此平台");
            }
            if (ObjectUtil.isNotEmpty(one) && !one.getName().equals(platform.getName())) {
                throw new BizRuntimeException("该平台名称已存在");
            }
        } else {
            //新增
            Long regionId = inPlatformEntity.getRegionId();
            if (regionId < 1) {
                throw new BizRuntimeException("区域ID不能为空");
            }
            if (ObjectUtil.isNotEmpty(one)) {
                throw new BizRuntimeException("该平台名称已存在");
            }
        }
    }

    @Override
    public Boolean add(InPlatformEntity inPlatformEntity) {
        vAddOrEdit(inPlatformEntity);

        Long regionId = inPlatformEntity.getRegionId();
        RegionEntity region = regionService.getById(regionId);
        if (null == region || StringUtil.isEmpty(region.getCode())) {
            throw new BizRuntimeException("查无此区域或区域编码为空");
        }

        String platformCode = generateInPlatformCode(region.getCode());
        if (StringUtil.isEmpty(platformCode)) {
            throw new BizRuntimeException("平台编码生成失败");
        }

        InPlatformEntity one = this.getOne(new LambdaQueryWrapper<InPlatformEntity>().eq(InPlatformEntity::getCode, platformCode));
        if (null != one) {
            throw new BizRuntimeException("平台编码重复");
        }
        inPlatformEntity.setCode(platformCode);

        String pwd = IdGenerateUtil.generateInviteCode62();
        inPlatformEntity.setPwd(pwd);
        boolean save = this.save(inPlatformEntity);
        if (save) {
            createOrUpdateSync(inPlatformEntity);
        }
        return save;
    }

    /**
     * 中心编码(8位)、行业编码(2位)、类型编码(3位)和序号(7位)四个码段共20位十进制数字字符构成
     * 即系统编码 =中心编码 + 行业编码 + 类型编码 + 序号。
     */
    private String generateInPlatformCode(String regionCode) {
        int regionCodeTargetLength = 8;
        int regionCodePaddingLength = regionCodeTargetLength - regionCode.length();
        // 生成一个由0组成的字符串
        String regionCodePadding = String.format("%0" + regionCodePaddingLength + "d", 0);
        regionCode = regionCode + regionCodePadding;

        //行业编码
        String industryCode = "00";

        //类型编码 200
        String typeCode = "200";

        //网络标识 0
        String networkCode = "0";

        //序号
        String key = "in:platform:code";
        String incr = String.valueOf(redisUtil.incr(key, 1l));
        int numberCodeTargetLength = 6;
        int numberCodePaddingLength = numberCodeTargetLength - incr.length();
        String numberCodePadding = String.format("%0" + numberCodePaddingLength + "d", 0);
        String numberCode = numberCodePadding + incr;

        return regionCode + industryCode + typeCode + networkCode + numberCode;
    }

    private void createOrUpdateSync(InPlatformEntity inPlatformEntity) {
        InPlatformSyncDto inPlatformSyncDto = new InPlatformSyncDto();
        inPlatformSyncDto.setId(String.valueOf(inPlatformEntity.getId()));
        inPlatformSyncDto.setSn(inPlatformEntity.getCode());
        inPlatformSyncDto.setName(inPlatformEntity.getName());
        inPlatformSyncDto.setPwd(inPlatformEntity.getPwd());
        inOutPlatformProduce.send(InOutPlatformProduce.GB_DOWN_TOPIC, InOutPlatformMessage.builder().nodeId(inPlatformEntity.getNodeId()).messageType(InOutPlatformProduce.MESSAGE_TYPE_1).data(inPlatformSyncDto).build());
    }


    @Override
    public Boolean edit(InPlatformEntity inPlatformEntity) {
        if (null == inPlatformEntity.getId() || inPlatformEntity.getId() <= 0) {
            throw new BizRuntimeException("平台id有误");
        }
        vAddOrEdit(inPlatformEntity);
        boolean update = this.updateById(inPlatformEntity);
        if (update) {
            InPlatformEntity entity = this.getById(inPlatformEntity.getId());
            createOrUpdateSync(entity);
        }
        return update;
    }

    @Override
    public InPlatformDto getInfo(Long platformId) {
        if (null == platformId) {
            throw new BizRuntimeException("平台id不能为空");
        }
        return baseMapper.getInfo(platformId);
    }

    @Override
    public IPage<InPlatformDto> listPage(InPlatformEntity entity) {
        IPage<InPlatformDto> page = baseMapper.getListPage(new Page<InPlatformEntity>(entity.getPageNum(), entity.getPageSize()), entity);
        if (page == null || page.getRecords() == null || page.getRecords().isEmpty()) {
            return page;
        }
        return page;
    }

    @Override
    public Boolean del(Long platformId) {
        if (null == platformId) {
            throw new BizRuntimeException("平台id不能为空");
        }
        InPlatformDto platform = getInfo(platformId);
        if (ObjectUtil.isEmpty(platform)) {
            throw new BizRuntimeException("查无此平台");
        }
        int del = baseMapper.deleteById(platformId);
        if (del > 0) {
            InPlatformSyncDto inPlatformSyncDto = new InPlatformSyncDto();
            inPlatformSyncDto.setId(String.valueOf(platformId));
            inPlatformSyncDto.setSn(platform.getCode());
            inOutPlatformProduce.send(InOutPlatformProduce.GB_DOWN_TOPIC, InOutPlatformMessage.builder().nodeId(platform.getNodeId()).messageType(InOutPlatformProduce.MESSAGE_TYPE_2).data(inPlatformSyncDto).build());
        }
        return del > 0;
    }

    @Override
    public String subscribes(GbSubscribesDto dto) {
        if (StringUtil.isEmpty(dto.getCode())) {
            throw new BizRuntimeException("平台编码不能为空");
        }
        InPlatformEntity one = this.getOne(new LambdaQueryWrapper<InPlatformEntity>().eq(InPlatformEntity::getCode, dto.getCode()), false);
        if (ObjectUtil.isEmpty(one)) {
            throw new BizRuntimeException("查无此平台");
        }
        if (StringUtil.isEmpty(one.getNodeId())) {
            throw new BizRuntimeException("该平台节点ID缺失");
        }
        SignalNodeEntity nodeInfo = signalNodeService.getInfo(one.getNodeId());
        if (ObjectUtil.isEmpty(nodeInfo)) {
            throw new BizRuntimeException("该平台节点信息缺失");
        }

        JSONObject jsonObject = new JSONObject();
        List<String> ids = new ArrayList<>();
        ids.add(dto.getCode());
        jsonObject.put("device_ids", ids);
        jsonObject.put("catelog", true);
        jsonObject.put("alarm", true);
        jsonObject.put("position", true);

        GbSyncResp gbSyncResp = qxNodeReqService.gbSubscribes(nodeInfo, jsonObject);
        if (gbSyncResp.getFailure() > 0) {
            throw new BizRuntimeException(!CollectionUtils.isEmpty(gbSyncResp.getResult()) ? "国标节点："+gbSyncResp.getResult().get(0).getError() : "同步失败");
        }
        return "成功";
    }

    @Override
    public String reSync(GbReSyncDto dto) {
        if (StringUtil.isEmpty(dto.getCode())) {
            throw new BizRuntimeException("平台编码不能为空");
        }
        InPlatformEntity one = this.getOne(new LambdaQueryWrapper<InPlatformEntity>().eq(InPlatformEntity::getCode, dto.getCode()), false);
        if (ObjectUtil.isEmpty(one)) {
            throw new BizRuntimeException("查无此平台");
        }
        if (StringUtil.isEmpty(one.getNodeId())) {
            throw new BizRuntimeException("该平台节点ID缺失");
        }
        SignalNodeEntity nodeInfo = signalNodeService.getInfo(one.getNodeId());
        if (ObjectUtil.isEmpty(nodeInfo)) {
            throw new BizRuntimeException("该平台节点信息缺失");
        }


        Map<String, Object> map = new HashMap<>();
        List<String> ids = new ArrayList<>();
        ids.add(dto.getCode());
        map.put("ids", ids);
        GbSyncResp gbSyncResp = qxNodeReqService.gbReSync(nodeInfo, map);
        if (gbSyncResp.getFailure() > 0) {
            throw new BizRuntimeException(!CollectionUtils.isEmpty(gbSyncResp.getResult()) ? "国标节点："+gbSyncResp.getResult().get(0).getError() : "同步失败");
        }
        return "成功";
    }
}