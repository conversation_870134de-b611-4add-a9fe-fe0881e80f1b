package com.saida.services.system.basicData.controller;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.service.FileService;
import com.saida.services.common.tools.ImageCropperUtil;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.system.basicData.dto.*;
import com.saida.services.system.basicData.service.BasicPeopleInfoService;
import com.saida.services.system.basicData.vo.BasicPeoplePageQueryVo;
import com.saida.services.system.basicData.vo.PeopleFaceCompareVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Base64;
import java.util.List;

/**
 * 基础信息-人员信息表(BasicPeopleInfoEntity)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-05 15:20:01
 */
@RestController
@RequestMapping("basicPeopleInfo")
public class BasicPeopleInfoController {
    /**
     * 服务对象
     */
    @Resource
    private BasicPeopleInfoService basicPeopleInfoService;
    @Autowired
    private FileService fileService;

    /**
     * 分页查询
     *
     * @param dto         筛选条件
     * @param pageRequest 分页对象
     * @return 查询结果
     */
    @GetMapping("findPage")
    public DtoResult<BasePageInfoEntity<BasicPeoplePageQueryVo>> queryByPage(BasicPeoplePageQueryDto dto, BaseRequest pageRequest) {
        return DtoResult.ok(this.basicPeopleInfoService.queryByPage(dto, pageRequest));
    }

    /**
     * 新增数据
     *
     * @param dto 实体
     * @return 新增结果
     */
    @PostMapping("insert")
    public DtoResult<Integer> add(@RequestBody @Validated BasicPeopleAddDto dto) {
        return this.basicPeopleInfoService.insert(dto);
    }

    /**
     * 编辑数据
     *
     * @param dto 实体
     * @return 编辑结果
     */
    @PostMapping("update")
    public DtoResult<Integer> edit(@RequestBody @Validated BasicPeopleEditDto dto) {
        return this.basicPeopleInfoService.update(dto);
    }

    /**
     * 批量删除
     */
    @PostMapping("delete")
    public DtoResult<Integer> deleteById(@RequestBody @Validated BasicBatchDeleteDto dto) {
        return this.basicPeopleInfoService.deleteById(dto.getIds());
    }

    /**
     * 修改分组
     */
    @PostMapping("updateGroups")
    public DtoResult<Boolean> updateGroups(@RequestBody @Validated BasicBatchUpdateGroupDto dto) {
        return this.basicPeopleInfoService.updateGroups(dto);
    }

    /**
     * 人脸比对
     */
    @PostMapping("faceCompare")
    public DtoResult faceCompare(@RequestParam("image1") MultipartFile imageLeft,
                                                      @RequestParam("image2") MultipartFile imageRight) {
        return this.basicPeopleInfoService.faceCompare(imageLeft, imageRight);
    }

    /**
     * 以图搜图
     */
    @PostMapping("faceSearch")
    public DtoResult<BasePageInfoEntity<PeopleFaceCompareVo>> faceSearch(@RequestParam("image") MultipartFile image,
                                                           @RequestParam("groupId") List<Long> groupIds,
                                                           @RequestParam("similarity") double similarity,
                                                                         @RequestParam("pageSize") int pageSize,
                                                                          @RequestParam("pageNum") int pageNum) {
        return this.basicPeopleInfoService.faceSearch(image, groupIds, similarity,pageSize, pageNum);
    }



    /**
     * 陌生人分组人脸新增
     */
    @PostMapping("addStrangerPhoto")
    public DtoResult<Integer> addStrangerPhoto(@RequestBody BasicDefaultPeopleAddDto dto) {
        return this.basicPeopleInfoService.addStrangerPhoto(dto);
    }

    /**
     * 测试裁剪图片
     *
     */
    @GetMapping("cropImage")
    public DtoResult<String> cropImage() {
        try {
            byte[] bytes = fileService.downloadFileAsByteArrayByCount("https://test-vlinker-minio-api.sdvideo.cn:48801/v-linker/common/2025/06/23/9f8a6989-43f0-451b-b443-569b4ebe0c29.png", 5);
            String base64AlarmPic = Base64.getEncoder().encodeToString(bytes);
            return DtoResult.ok(ImageCropperUtil.cropImage(base64AlarmPic, 367, 67, 110, 146));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

