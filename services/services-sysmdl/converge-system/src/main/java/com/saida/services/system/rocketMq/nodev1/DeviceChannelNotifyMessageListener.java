package com.saida.services.system.rocketMq.nodev1;

import com.alibaba.fastjson.JSON;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.system.job.UpdateDeviceChannelJob;
import com.saida.services.system.job.UpdatePlatformChannelJob;
import com.saida.services.system.sys.dto.DeviceChannelNotifyDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

@Slf4j
@Component

public class DeviceChannelNotifyMessageListener implements VLinkerMqMessageListener {

    @Lazy
    @Resource
    private UpdateDeviceChannelJob updateDeviceChannelJob;
    @Lazy
    @Resource
    private UpdatePlatformChannelJob updatePlatformChannelJob;

    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("notify_sync_device_channel")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        log.info("[mq:{}]消费消息：{}", vLinkerTopicConfig().getTopic(), messageBody);
        DeviceChannelNotifyDto message = JSON.parseObject(messageBody, DeviceChannelNotifyDto.class);
        handleMessage(message);
    }


    /**
     * 处理告警通知消息。
     * 当收到告警通知消息时，根据消息内容查询设备信息，并根据设备信息和告警通知内容创建或更新告警实体，
     * 对于特定类型的告警，还会通知算法平台。
     *
     * @param message 告警通知消息，包含告警的详细信息。
     */
    protected void handleMessage(DeviceChannelNotifyDto message) {
        // 如果消息为空，则直接返回，不进行处理
        if (message == null) {
            return;
        }
        // 如果消息中的SN（设备序列号）为空，则直接返回，不进行处理
        if (StringUtil.isEmpty(message.getDevice_id())) {
            log.info("[mq:notify_sync_device_channel]设备序列号为空，忽略消息：{}", JSON.toJSONString(message));
            return;
        }
        if ("1".equals(message.getIs_platform())) {
            updatePlatformChannelJob.updateDeviceChannelJob(message.getDevice_id(), false);
        } else {
            updateDeviceChannelJob.updateDeviceChannel(UUID.randomUUID().toString(), message.getDevice_id());
        }

    }


}