package com.saida.services.system.thread;

import cn.hutool.core.date.DateUtil;
import com.saida.services.algorithm.dto.AlgorithmJumpPrePointDto;
import com.saida.services.algorithm.entity.CameraCruiseRecordEntity;
import com.saida.services.common.mq.rocketMq.RocketMQEnhanceTemplate;
import com.saida.services.common.mq.message.DevicePreSetJumpMessage;
import com.saida.services.system.algVideo.service.AlgVideoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
public class CameraCruiseThread extends Thread {

    private final Long id;
    private final List<CameraCruiseRecordEntity> list;

    private boolean isRun = true;

    private final AlgVideoService algVideoService;

    private final RedisTemplate<String, Object> redisTemplate;

    private final RocketMQEnhanceTemplate rocketMQEnhanceTemplate;

    public CameraCruiseThread(Long id, List<CameraCruiseRecordEntity> list, AlgVideoService algVideoService
            , RedisTemplate<String, Object> redisTemplate, RocketMQEnhanceTemplate rocketMQEnhanceTemplate) {
        this.id = id;
        this.list = list;
        this.algVideoService = algVideoService;
        this.redisTemplate = redisTemplate;
        this.rocketMQEnhanceTemplate = rocketMQEnhanceTemplate;
    }

    public void stopRun() {
        isRun = false;
    }

    /**
     * 运行方法，用于设备巡航线程的执行。
     * 通过Redis的键值对来标记任务是否正在执行，避免重复执行相同任务。
     * 巡航过程中，根据配置的设备和位置信息，逐个跳转到指定的预设位，并在每个预设位停留指定的时间。
     */
    @Override
    public void run() {
        this.setName("alg-巡航-" + id);
        log.info("巡航线程启动... id:{}", this.id);
        // 构建任务的Redis键，用于标记任务状态和避免重复执行
        String key = "ALG:TASK:CameraCruiseThread:" + id;
        // 从Redis中获取当前任务的状态
        Object redisTask = redisTemplate.opsForValue().get(key);
        // 如果任务已经在执行，则直接返回，避免重复执行
        if (redisTask != null) {
            log.info("任务已经在别的服务执行，跳过执行");
            return;
        }
        // 初始化索引，用于遍历设备巡航记录列表
        int i = 0;
        // 当任务应该继续运行时，循环执行巡航过程
        while (isRun) {
            // 更新Redis中的任务状态，表示任务正在执行
            redisTemplate.opsForValue().set(key, DateUtil.now(), 60, TimeUnit.SECONDS);
            // 获取当前要处理的设备巡航记录
            CameraCruiseRecordEntity cameraCruiseRecordEntity = list.get(i);
            // 构建预设位跳转请求
            AlgorithmJumpPrePointDto req = new AlgorithmJumpPrePointDto();
            req.setCameraId(cameraCruiseRecordEntity.getCameraId());
            req.setIndex(cameraCruiseRecordEntity.getPrePoint());
            // 日志记录当前的巡航请求
            log.info("巡航->req: devId:{} index:{}  cruiseTime:{} rotationTime:{}", cameraCruiseRecordEntity.getCruiseId(),
                    cameraCruiseRecordEntity.getPrePoint(), cameraCruiseRecordEntity.getCruiseTime(), cameraCruiseRecordEntity.getRotationTime());
            // 发送预设位跳转请求
            algVideoService.jumpPrePoint(req);
            //发送预置点位跳转mq
            if (rocketMQEnhanceTemplate != null) {
                DevicePreSetJumpMessage devicePreSetJumpMessage = new DevicePreSetJumpMessage() {{
                    setCameraId(cameraCruiseRecordEntity.getCameraId());
                    setIndex(req.getIndex());
                    setRotationTime(cameraCruiseRecordEntity.getRotationTime());
                    setCruiseTime(cameraCruiseRecordEntity.getCruiseTime());
                }};
                rocketMQEnhanceTemplate.asyncSend("device_pre_set_jump", devicePreSetJumpMessage, new SendCallback() {
                    @Override
                    public void onSuccess(SendResult sendResult) {
                        log.info("device_pre_set_jump 发送参数:{} 发送结果：{}", devicePreSetJumpMessage, sendResult);
                    }

                    @Override
                    public void onException(Throwable e) {
                        log.error("device_pre_set_jump 发送参数:{} 发送失败：{}", devicePreSetJumpMessage, e.getMessage(), e);
                    }
                });
            }
            // 判断是否是最后一个预设位，如果是，则重新开始，否则继续下一个预设位
            if (i == list.size() - 1) {
                i = 0;
            } else {
                i = i + 1;
            }
            // 在当前预设位停留指定的时间
            try {
                Thread.sleep(cameraCruiseRecordEntity.getCruiseTime() * 1000);
            } catch (InterruptedException e) {
                // 记录线程休眠过程中的异常
                log.error("线程休眠异常", e);
            }
        }
        // 任务结束，删除Redis中的任务标记
        redisTemplate.delete(key);
    }

}
