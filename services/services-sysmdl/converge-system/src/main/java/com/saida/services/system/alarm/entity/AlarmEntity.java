package com.saida.services.system.alarm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.saida.services.entities.base.BaseRequest;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * 告警信息记录表
 */
@Getter
@Setter
@TableName("alarm")
public class AlarmEntity extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，使用雪花算法生成
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 运行算法ID
     */
    private Long algorithmId;

    /**
     * 告警来源：云服务，边缘终端，云化摄像机
     */
    private Long alarmSource;

    /**
     * 云服务ID
     */
    private Long cloudServerId;

    /**
     * 盒子id
     */
    private Long terminalBoxId;

    /**
     * 原始图片地址
     */
    private String originalImageUrl;

    /**
     * 告警图片地址
     */
    private String alarmImageUrl;

    private String alarmImageUrlTmp;

    /**
     * 告警图片不包含置信度
     */
    private String alarmImageNotProbUrl;

    /**
     * 告警视频地址
     */
    private String videoUrl;

    /**
     * 年份
     */
    private String year;

    /**
     * 月份
     */
    private String month;

    /**
     * 日期，格式为yyyy-MM-dd
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String day;

    /**
     * 时间
     */
    private String time;

    /**
     * 告警时间
     */
    private String alarmTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段
     */
    private String ext;

    /**
     * 是否在管理端显示
     */
    private Integer isManageShow;

    /**
     * 原始告警标识 规则 设备类型(算法)_告警类型
     */
    private String originalAlarmStr;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 告警时间戳
     */
    private Long alarmTimeLong;

    /**
     * 告警消息唯一id
     */
    private String msgId;

    /**
     * 1 已绘图 2未绘图
     */
    private Integer drawAlarm;

    /**
     * 1 已绘图 2未绘图
     */
    private Integer drawNotProbAlarm;

    /**
     * @see com.saida.services.common.dto.AlarmNormalizationExt
     */
    private String normalizationExt; // 归一化的扩展信息

    private String callbackMessage;

    private String traceId;

    private String spanId;

    private Integer push;

    /**
     * 人员布控id
     */
    private Long peopleDeployId;
}