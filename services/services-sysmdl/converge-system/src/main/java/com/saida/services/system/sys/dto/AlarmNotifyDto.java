package com.saida.services.system.sys.dto;

import com.saida.services.common.dto.AlarmNormalizationExt;
import com.saida.services.common.mq.vlinker.BaseMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class AlarmNotifyDto extends BaseMessage {

    private String id; // 此设备的唯一报警ID
    private String sn; // 设备唯一ID
    private String msg_id; // 告警标识 为了拼接多个告警
    private String channel_id; // 通道ID
    private Integer priority; // 优先级别
    private Integer method; // 报警方式
    private Long timestamp; // 开始报警时间(时间戳毫秒)
    private Integer type; // 报警类型
    private List<String> snap_paths; // 图片链接地址列表
    private String video_path; // 视频链接地址
    private String log_path; // 日志地址
    private Map<String, Object> ext; // 扩展信息
    private String node_id; // 服务节点ID

    //原始告警标识 规则 设备类型(算法)_告警类型
    private String originalAlarmStr;

    /**
     * 归一化的ext
     */
    private AlarmNormalizationExt normalizationExt;

    /**
     * 是否是sdk的告警
     * 因为就算sdk接入的 也会触发原本视频协议的告警信息
     */
    private Boolean sdkAccess = false;
}