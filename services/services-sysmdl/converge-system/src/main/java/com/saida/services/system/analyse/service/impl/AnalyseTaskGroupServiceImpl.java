package com.saida.services.system.analyse.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.mq.vlinker.DelayLevelEnum;
import com.saida.services.system.analyse.mapper.AnalyseTaskGroupMapper;
import com.saida.services.system.analyse.pojo.dto.AnalyseTaskGroupListDto;
import com.saida.services.system.analyse.pojo.entity.AnalyseTaskBatchEntity;
import com.saida.services.system.analyse.pojo.entity.AnalyseTaskBatchTaskEntity;
import com.saida.services.system.analyse.pojo.entity.AnalyseTaskGroupEntity;
import com.saida.services.system.analyse.service.AnalyseTaskBatchService;
import com.saida.services.system.analyse.service.AnalyseTaskBatchTaskService;
import com.saida.services.system.analyse.service.AnalyseTaskGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class AnalyseTaskGroupServiceImpl extends ServiceImpl<AnalyseTaskGroupMapper, AnalyseTaskGroupEntity> implements AnalyseTaskGroupService {

    @Resource
    private AnalyseTaskBatchService analyseTaskBatchService;
    @Resource
    private AnalyseTaskBatchTaskService analyseTaskBatchTaskService;

    @Override
    public DtoResult<List<AnalyseTaskGroupListDto>> getList() {
        List<AnalyseTaskGroupEntity> analyseTaskGroupEntityList = this.list();
        if (CollectionUtil.isEmpty(analyseTaskGroupEntityList)) {
            return DtoResult.ok();
        }
        List<AnalyseTaskBatchEntity> analyseTaskBatchEntityList = analyseTaskBatchService.list(new LambdaQueryWrapper<AnalyseTaskBatchEntity>()
                .orderByAsc(AnalyseTaskBatchEntity::getSerialNumber)
        );
        return DtoResult.ok(analyseTaskGroupEntityList.stream().map(t1 -> {
            AnalyseTaskGroupListDto dto = new AnalyseTaskGroupListDto();
            dto.setId(t1.getId());
            dto.setName(t1.getName());

            List<AnalyseTaskGroupListDto.AnalyseTaskBatchInfo> analyseTaskBatchInfoList;
            analyseTaskBatchInfoList = analyseTaskBatchEntityList.stream().filter(t2 -> Objects.equals(t1.getId(), t2.getGroupId())).map(t2 -> {
                AnalyseTaskGroupListDto.AnalyseTaskBatchInfo analyseTaskBatchInfo = new AnalyseTaskGroupListDto.AnalyseTaskBatchInfo();
                BeanUtils.copyProperties(t2, analyseTaskBatchInfo);
                DelayLevelEnum delayLevelEnum = DelayLevelEnum.match(analyseTaskBatchInfo.getAnalyzeTime());
                if (null != delayLevelEnum) {
                    analyseTaskBatchInfo.setAnalyzeTimeName(delayLevelEnum.getMsg());
                }
                return analyseTaskBatchInfo;
            }).collect(Collectors.toList());
            dto.setAnalyseTaskBatchList(analyseTaskBatchInfoList);
            return dto;
        }).collect(Collectors.toList()));
    }

    @Override
    public DtoResult<Void> add(AnalyseTaskGroupEntity entity) {
        long count = this.count(new LambdaQueryWrapper<AnalyseTaskGroupEntity>()
                .eq(AnalyseTaskGroupEntity::getName, entity.getName()));
        if (count > 0) {
            return DtoResult.error("分组名称不能重复");
        }
        return this.save(entity) ? DtoResult.ok() : DtoResult.error();
    }

    @Override
    public DtoResult<Void> edit(AnalyseTaskGroupEntity entity) {
        return this.updateById(entity) ? DtoResult.ok() : DtoResult.error();
    }

    @Override
    public DtoResult<Void> delete(AnalyseTaskGroupEntity entity) {
        List<AnalyseTaskBatchEntity> analyseTaskBatchEntityList = analyseTaskBatchService.list(new LambdaQueryWrapper<AnalyseTaskBatchEntity>()
                .eq(AnalyseTaskBatchEntity::getGroupId, entity.getId()));
        if (CollectionUtil.isNotEmpty(analyseTaskBatchEntityList)) {
            Set<Long> taskBatchIdSet = analyseTaskBatchEntityList.stream().map(AnalyseTaskBatchEntity::getId).collect(Collectors.toSet());
            // 1.删除 analyse_task_batch_task
            analyseTaskBatchTaskService.remove(new LambdaUpdateWrapper<AnalyseTaskBatchTaskEntity>()
                    .in(AnalyseTaskBatchTaskEntity::getBatchId, taskBatchIdSet));
            // 2.删除 analyse_task_batch
            analyseTaskBatchService.remove(new LambdaUpdateWrapper<AnalyseTaskBatchEntity>()
                    .in(AnalyseTaskBatchEntity::getId, taskBatchIdSet));
        }
        // 3.删除 analyse_task_group
        return this.removeById(entity.getId()) ? DtoResult.ok() : DtoResult.error();
    }
}