package com.saida.services.system.ops.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.*;
import com.saida.services.converge.entity.dto.*;
import com.saida.services.common.mq.message.InOutPlatformMessage;
import com.saida.services.system.ops.mapper.OutPlatformMapper;
import com.saida.services.system.ops.service.*;
import com.saida.services.system.rocketMq.nodev1.InOutPlatformProduce;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service("outPlatformService")
public class OutPlatformServiceImpl extends ServiceImpl<OutPlatformMapper, OutPlatformEntity> implements OutPlatformService {

    @Autowired
    private VirtualOrganTreeService virtualOrganTreeService;

    @Autowired
    private VirtualOrganDeviceRelativeService virtualOrganDeviceRelativeService;

    @Autowired
    private InOutPlatformProduce inOutPlatformProduce;

    @Autowired
    private SignalNodeService signalNodeService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private OpsDeviceChannelService channelService;

    private void vAddOrEdit(OutPlatformEntity outPlatformEntity) {
        Long voId = outPlatformEntity.getVoId();
        if (null == voId) {
            throw new BizRuntimeException("虚拟组织ID不能为空");
        }
        VirtualOrganTreeEntity virtualOrgan = virtualOrganTreeService.getById(voId);
        if (ObjectUtil.isEmpty(virtualOrgan)) {
            throw new BizRuntimeException("该虚拟组织不存在");
        }

        OutPlatformEntity one = this.getOne(new LambdaQueryWrapper<OutPlatformEntity>().eq(OutPlatformEntity::getName, outPlatformEntity.getName()), false);
        OutPlatformEntity oneByCode = this.getOne(new LambdaQueryWrapper<OutPlatformEntity>().eq(OutPlatformEntity::getCode, outPlatformEntity.getCode()), false);
        //更新
        if (null != outPlatformEntity.getId() && outPlatformEntity.getId() > 0) {
            OutPlatformEntity platform = this.getById(outPlatformEntity.getId());
            if (ObjectUtil.isEmpty(platform)) {
                throw new BizRuntimeException("查无此平台");
            }
            if (ObjectUtil.isNotEmpty(one) && !one.getName().equals(platform.getName())) {
                throw new BizRuntimeException("该平台名称已存在");
            }
            if (ObjectUtil.isNotEmpty(oneByCode) && !oneByCode.getCode().equals(platform.getCode())) {
                throw new BizRuntimeException("该接入编码已存在");
            }
        } else {//新增
            if (ObjectUtil.isNotEmpty(one)) {
                throw new BizRuntimeException("该平台名称已存在");
            }
            if (ObjectUtil.isNotEmpty(oneByCode)) {
                throw new BizRuntimeException("该接入编码已存在");
            }
            if (ObjectUtil.isEmpty(outPlatformEntity.getNodeId())) {
                throw new BizRuntimeException("节点id不能为空");
            }
        }
    }

    @Override
    public Boolean add(OutPlatformEntity outPlatformEntity) {
        vAddOrEdit(outPlatformEntity);
//        String pwd = IdGenerateUtil.generateInviteCode62();
//        outPlatformEntity.setPwd(pwd);

        boolean save = this.save(outPlatformEntity);
        if (save) {
            createOrUpdateSync(outPlatformEntity, "add");
        }
        return save;
    }

    private void createOrUpdateSync(OutPlatformEntity outPlatformEntity, String type) {
        OutPlatformSyncDto dto = new OutPlatformSyncDto();

        SignalNodeEntity nodeEntity = signalNodeService.getInfo(outPlatformEntity.getNodeId());
        OutPlatformSyncDto.MainPlatform mainPlatform = new OutPlatformSyncDto.MainPlatform();
        BeanUtils.copyProperties(nodeEntity, mainPlatform);
        mainPlatform.setSipPort(String.valueOf(nodeEntity.getSipPort()));
        dto.setMainPlatform(mainPlatform);

        OutPlatformSyncDto.ThirdPlatform thirdPlatform = new OutPlatformSyncDto.ThirdPlatform();
        BeanUtils.copyProperties(outPlatformEntity, thirdPlatform);
        thirdPlatform.setVirtualOrganId(String.valueOf(outPlatformEntity.getVoId()));
        thirdPlatform.setSipPort(String.valueOf(outPlatformEntity.getSipPort()));
        if (outPlatformEntity.getStatus() == null) {
            thirdPlatform.setEnable(true);
        } else {
            thirdPlatform.setEnable(outPlatformEntity.getStatus() == 1);
        }
        dto.setThirdPlatform(thirdPlatform);
        dto.setUsername(outPlatformEntity.getCode());

        inOutPlatformProduce.send(InOutPlatformProduce.GB_UP_TOPIC, InOutPlatformMessage.builder().nodeId(outPlatformEntity.getNodeId()).messageType(InOutPlatformProduce.MESSAGE_TYPE_1).data(dto).build());

//        String str = "{\"thirdPlatform\":{\"enable\":true,\"name\":\"测试平台1\",\"sipId\":\"34010100001120000001\",\"sipDomain\":\"3401010000\",\"sipIp\":\"**************\",\"sipPort\":\"25060\",\"registerCycle\":\"3600\",\"heartbeatCycle\":\"60\",\"protocol\":\"tcp\",\"pwd\":\"123456\",\"virtualOrganId\":\"1790917960549593090\"},\"mainPlatform\":{\"name\":\"主节点\",\"sipId\":\"34020000002000000001\",\"sipDomain\":\"3402000000\",\"sipIp\":\"**************\",\"sipPort\":\"5060\",\"registerCycle\":\"3600\",\"heartbeatCycle\":\"60\",\"protocol\":\"TCP\",\"pwd\":\"123456\"}}";
//        Object parse = JSON.parse(str);
//
//        inOutPlatformProduce.send(InOutPlatformProduce.GB_UP_TOPIC, InOutPlatformMessage.builder().nodeId("zx30191").messageType(InOutPlatformProduce.MESSAGE_TYPE_1).data(parse).build());
    }


    @Override
    public Boolean edit(OutPlatformEntity outPlatformEntity) {
        if (null == outPlatformEntity.getId() || outPlatformEntity.getId() <= 0) {
            throw new BizRuntimeException("平台id有误");
        }
        vAddOrEdit(outPlatformEntity);
        boolean update = this.updateById(outPlatformEntity);
        if (update) {
            OutPlatformEntity entity = this.getById(outPlatformEntity.getId());
            createOrUpdateSync(entity, "edit");
        }
        return update;
    }

    @Override
    public OutPlatformDto getInfo(Long platformId) {
        if (null == platformId) {
            throw new BizRuntimeException("平台id不能为空");
        }
        return baseMapper.getInfo(platformId);
    }

    @Override
    public IPage<OutPlatformDto> listPage(OutPlatformEntity entity) {
        IPage<OutPlatformDto> page = baseMapper.getListPage(new Page<OutPlatformEntity>(entity.getPageNum(), entity.getPageSize()), entity);
        if (page == null || page.getRecords() == null || page.getRecords().isEmpty()) {
            return page;
        }
        return page;
    }

    @Override
    public Boolean del(Long platformId) {
        if (null == platformId) {
            throw new BizRuntimeException("平台id不能为空");
        }
        OutPlatformDto platform = getInfo(platformId);
        if (ObjectUtil.isEmpty(platform)) {
            throw new BizRuntimeException("查无此平台");
        }
        boolean del = baseMapper.deleteById(platformId) > 0;
        if (del) {
            OutPlatformDelSyncDto dto = new OutPlatformDelSyncDto();
            dto.setId(String.valueOf(platformId));
            dto.setSn(platform.getSipId());
            inOutPlatformProduce.send(InOutPlatformProduce.GB_UP_TOPIC, InOutPlatformMessage.builder().nodeId(platform.getNodeId()).messageType(InOutPlatformProduce.MESSAGE_TYPE_2).data(dto).build());
        }
        return del;
    }

    @Override
    public Boolean updateStatus(Long platformId, Integer status) {
        if (null == platformId || platformId <= 0L || null == status || !new ArrayList<>(Arrays.asList(1, 2)).contains(status)) {
            throw new BizRuntimeException("参数有误");
        }
        OutPlatformEntity entity = this.getById(platformId);
        if (ObjectUtil.isEmpty(entity)) {
            throw new BizRuntimeException("查无此平台");
        }
        entity.setStatus(status);
        boolean b = this.updateById(entity);

        createOrUpdateSync(entity, "edit");
        return b;
    }

    @Override
    public Boolean batchShare(GbBatchShareDto dto) {
        if (dto.getConcatenatedCodes().isEmpty()) {
            throw new BizRuntimeException("级联码不能为空");
        }
        if (dto.getVideoShare() != 0 && dto.getVideoShare() != 1) {
            throw new BizRuntimeException("视频共享参数有误");
        }
        if (dto.getVcrShare() != 0 && dto.getVcrShare() != 1) {
            throw new BizRuntimeException("录像共享参数有误");
        }
        if (dto.getPtzShare() != 0 && dto.getPtzShare() != 1) {
            throw new BizRuntimeException("ptz享参数有误");
        }
        List<VirtualOrganDeviceRelativeEntity> deviceRelativeList = virtualOrganDeviceRelativeService.list(
                new LambdaQueryWrapper<VirtualOrganDeviceRelativeEntity>()
                        .in(VirtualOrganDeviceRelativeEntity::getConcatenatedCode, dto.getConcatenatedCodes()));
        if (deviceRelativeList.size() != dto.getConcatenatedCodes().size()) {
            throw new BizRuntimeException("设备缺失");
        }
        deviceRelativeList.forEach(deviceRelative -> {
            deviceRelative.setVideoShare(dto.getVideoShare());
            deviceRelative.setVcrShare(dto.getVcrShare());
            deviceRelative.setPtzShare(dto.getPtzShare());
        });

        boolean update = virtualOrganDeviceRelativeService.updateBatchById(deviceRelativeList);
        if (update) {
            // 同步至国标
            List<String> ids = deviceRelativeList.stream().map(VirtualOrganDeviceRelativeEntity::getChannelId).collect(Collectors.toList());
            List<OpsDeviceChannelEntity> channelList = channelService.list(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                    .in(OpsDeviceChannelEntity::getChannelId, ids));
            Map<String, String> snMap = channelList.stream().collect(Collectors.toMap(OpsDeviceChannelEntity::getChannelId, OpsDeviceChannelEntity::getDeviceSn));
            Map<String, String> channelMap = channelList.stream()
                    .collect(Collectors.toMap(OpsDeviceChannelEntity::getChannelId, e -> {
                        if (StringUtil.isNotEmpty(e.getBid())) {
                            return e.getBid();
                        } else {
                            return e.getChannelId();
                        }
                    }));
            OutPlatformShareSyncDto outPlatformShareSyncDto = new OutPlatformShareSyncDto();
            if (dto.getVideoShare() != 1 && dto.getVcrShare() != 1 && dto.getPtzShare() != 1) {
                outPlatformShareSyncDto.setOperationType(2);
            }
            ArrayList<OutPlatformShareSyncDto.DeviceInfo> deviceInfos = new ArrayList<>();

            Map<Long, VirtualOrganTreeEntity> virtualOrganTreeMap = virtualOrganTreeService.listByIds(deviceRelativeList.stream()
                            .map(VirtualOrganDeviceRelativeEntity::getVirtualOrganId).collect(Collectors.toSet()))
                    .stream().collect(Collectors.toMap(VirtualOrganTreeEntity::getId, e -> e));

            for (VirtualOrganDeviceRelativeEntity virtualOrganDeviceRelativeEntity : deviceRelativeList) {
                if (StringUtil.isEmpty(channelMap.get(virtualOrganDeviceRelativeEntity.getChannelId()))) {
                    throw new BizRuntimeException("通道号缺失");
                }
                outPlatformShareSyncDto.setId(String.valueOf(virtualOrganDeviceRelativeEntity.getVirtualOrganId()));
                OutPlatformShareSyncDto.DeviceInfo deviceInfo = new OutPlatformShareSyncDto.DeviceInfo();
                deviceInfo.setId(String.valueOf(virtualOrganDeviceRelativeEntity.getId()));
                deviceInfo.setTreeId(virtualOrganTreeMap
                        .getOrDefault(virtualOrganDeviceRelativeEntity.getVirtualOrganId()
                                , new VirtualOrganTreeEntity()).getOrganCode());
                deviceInfo.setSn(channelMap.get(virtualOrganDeviceRelativeEntity.getChannelId()));
                deviceInfo.setDeviceId(snMap.get(virtualOrganDeviceRelativeEntity.getChannelId()));
                deviceInfo.setGbConcatenatedCode(virtualOrganDeviceRelativeEntity.getConcatenatedCode());
                deviceInfo.setEnableVideo(virtualOrganDeviceRelativeEntity.getVideoShare() == 1);
                deviceInfo.setEnableRecord(virtualOrganDeviceRelativeEntity.getVcrShare() == 1);
                deviceInfo.setEnablePTZ(virtualOrganDeviceRelativeEntity.getPtzShare() == 1);
                deviceInfos.add(deviceInfo);
            }
            outPlatformShareSyncDto.setDeviceInfos(deviceInfos);
            inOutPlatformProduce.send(InOutPlatformProduce.VIRTUAL_ORGAN_TREE,
                    InOutPlatformMessage
                            .builder()
                            .messageType(InOutPlatformProduce.MESSAGE_TYPE_3
                            )
                            .data(outPlatformShareSyncDto).build());
        }
        return update;
    }

}