package com.saida.services.system.ops.service.impl;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.tools.VlinkerThrowableUtil;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.common.mq.rocketMq.RocketMQEnhanceTemplate;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.*;
import com.saida.services.converge.entity.dto.SignalNodeDto;
import com.saida.services.entities.pojo.CountDto;
import com.saida.services.system.nodeGrpc.GrpcConfig;
import com.saida.services.system.nodeGrpc.GrpcSyncMsgTask;
import com.saida.services.system.ops.mapper.DeviceMapper;
import com.saida.services.system.ops.mapper.SignalNodeMapper;
import com.saida.services.system.ops.service.CloudStorageService;
import com.saida.services.system.ops.service.SignalNodeService;
import com.saida.services.system.ops.service.SignalNodeStatusRecordService;
import com.saida.services.system.ops.service.StorageStrategyService;
import com.saida.services.system.pb.OpenCommonMessage;
import com.saida.services.system.pb.OpenSun;
import com.saida.services.system.pb.OpenSunSaida;
import com.saida.services.system.pb.SunOpenGrpc;
import com.saida.services.system.rocketMq.dto.nodev2.GrpcCloseMessage;
import com.saida.services.system.rocketMq.nodev2.GrpcCloseMessageListener;
import groovy.lang.Lazy;
import io.grpc.ManagedChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service("signalNodeService")
public class SignalNodeServiceImpl extends ServiceImpl<SignalNodeMapper, SignalNodeEntity> implements SignalNodeService {

    @Lazy
    @Resource
    private SignalNodeStatusRecordService signalNodeStatusRecordService;
    @Resource
    private DeviceMapper deviceMapper;
    @Lazy
    @Resource
    private CloudStorageService cloudStorageService;
    @Lazy
    @Resource
    private StorageStrategyService storageStrategyService;
    @Resource
    private GrpcSyncMsgTask grpcSyncMsgTask;
    private RocketMQEnhanceTemplate rocketMQEnhanceTemplate;
    private GrpcCloseMessageListener grpcCloseMessageListener;

    @Autowired(required = false)
    public void setRocketMQEnhanceTemplate(RocketMQEnhanceTemplate rocketMQEnhanceTemplate) {
        this.rocketMQEnhanceTemplate = rocketMQEnhanceTemplate;
    }

    @Autowired(required = false)
    public void setGrpcCloseMessageListener(GrpcCloseMessageListener grpcCloseMessageListener) {
        this.grpcCloseMessageListener = grpcCloseMessageListener;
    }

    @Override
    public void addOrUpdate(SignalNodeEntity entity) {
        if (StringUtil.isEmpty(entity.getId())) {
            log.error("参数错误");
            return;
        }
        SignalNodeEntity node = getById(entity.getId());
        entity.setUpdateTime(DateTime.now());
        if (node == null) {//新增
            save(entity);
            saveStatusRecord(entity);
            if (entity.getVersion() == 2) {
                grpcSyncMsgTask.initGrpcClient(entity.getId());
            } else {
                //同步基础信息：存储服务、存储策略
                syncBaseInfo();
            }
        } else {//更新
            updateById(entity);
            saveStatusRecord(entity);
            if (rocketMQEnhanceTemplate != null) {
                if (node.getVersion() == 2) {
                    GrpcCloseMessage grpcCloseMessage = new GrpcCloseMessage();
                    grpcCloseMessage.setNodeId(entity.getId());
                    rocketMQEnhanceTemplate.send(grpcCloseMessageListener.vLinkerTopicConfig().getTopic(), grpcCloseMessage);
                }
            }
        }
    }

    @Resource
    private ThreadPoolConfig threadPoolConfig;

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        log.info("初始化信号节点服务");
        //同步基础信息：存储服务、存储策略
        if (rocketMQEnhanceTemplate != null) {
            threadPoolConfig.taskRunner(this::syncBaseInfo);
        }
        threadPoolConfig.taskRunner(this::initAddAncientGBSignalingService);
    }

    @Resource
    private GrpcConfig grpcConfig;

    //把国标节点新增到新节点上
    public void initAddAncientGBSignalingService() {
        List<SignalNodeEntity> list = super.list();
        List<SignalNodeEntity> gbNodes = list.stream()
                .filter(e -> e.getVersion() != null && e.getType() != null)
                .filter(e -> 1 == e.getVersion())
                .filter(e -> 1 == e.getType() || 2 == e.getType())
                .filter(e -> StringUtil.isNotEmpty(e.getUniverseIp()) && e.getUniversePort() != null)
                .collect(Collectors.toList());
        List<SignalNodeEntity> sunNodes = list.stream()
                .filter(e -> e.getVersion() != null)
                .filter(e -> 2 == e.getVersion())
                .collect(Collectors.toList());
        for (SignalNodeEntity gbNode : gbNodes) {
            for (SignalNodeEntity sunNode : sunNodes) {
                try {
                    // 把国标节点新增到新节点上
                    ManagedChannel managedChannel = grpcConfig.getGrpcChannel(sunNode.getId());
                    if (managedChannel == null) {
                        continue;
                    }
                    SunOpenGrpc.SunOpenBlockingStub sunOpenBlockingStub = SunOpenGrpc.newBlockingStub(managedChannel);
                    OpenCommonMessage.RequestAuthInfo authUser = grpcConfig.getAuthUser();
                    OpenSunSaida.AddAncientGBSignalingServiceRequest addAncientGBSignaling = OpenSunSaida.AddAncientGBSignalingServiceRequest
                            .newBuilder()
                            .setAuthInfo(authUser)
                            .setForceUseWanHost(true)
                            .setCoordinationGrpcPort(gbNode.getUniversePort())
                            .setLanHost(gbNode.getUniverseIp())
                            .setWanHost(gbNode.getUniverseIp())
                            .setDomain(gbNode.getUniverseIp())
                            .setPlanetServerTag(gbNode.getId())
                            .build();
                    OpenSun.StatefulReply statefulReply = sunOpenBlockingStub.addAncientGBSignalingService(addAncientGBSignaling);
                    log.info("新增国标节点到新节点上：{}  {}", statefulReply.getStatus(), statefulReply.getDesc());
                } catch (Exception e) {
                    log.error("新增国标节点到新节点上失败 {}", VlinkerThrowableUtil.getMsg(e));
                }
            }
        }
    }

    public void syncBaseInfo() {
        //同步存储服务
        List<CloudStorageEntity> storageList = cloudStorageService.list();
        if (storageList != null && !storageList.isEmpty()) {
            for (CloudStorageEntity storage : storageList) {
                cloudStorageService.syncMq(storage.getId(), CloudStorageService.ActionEnum.EDIT);
            }
        }

        //同步存储策略
        List<StorageStrategyEntity> strategyList = storageStrategyService.list();
        if (strategyList != null && !strategyList.isEmpty()) {
            for (StorageStrategyEntity strategy : strategyList) {
                storageStrategyService.syncMq(strategy.getId(), StorageStrategyService.ActionEnum.EDIT);
            }
        }
    }

    private void saveStatusRecord(SignalNodeEntity entity) {
        if (entity.getVersion() == 1) {
            signalNodeStatusRecordService.save(new SignalNodeStatusRecordEntity() {{
                setNodeId(entity.getId());
                setBearNum(entity.getBearNum());
                setOnLineNum(entity.getOnLineNum());
                setRecordTime(DateTime.now());
            }});
        }
    }

    @Override
    public IPage<SignalNodeDto> listPage(SignalNodeEntity entity) {
        return baseMapper.listPage(new Page<>(entity.getPageNum(), entity.getPageSize()), entity);
    }

    @Override
    public List<SignalNodeEntity> getList(SignalNodeEntity entity) {
        return baseMapper.getOnLineList(entity);
    }

    @Override
    public SignalNodeEntity getInfo(String id) {
        return getById(id);
    }

    @Override
    public void delete(String id) {
        if (rocketMQEnhanceTemplate != null) {
            GrpcCloseMessage grpcCloseMessage = new GrpcCloseMessage();
            grpcCloseMessage.setNodeId(id);
            rocketMQEnhanceTemplate.send(grpcCloseMessageListener.vLinkerTopicConfig().getTopic(), grpcCloseMessage);
        }
        removeById(id);
    }

    @Override
    public List<CountDto> groupByStatus(SignalNodeEntity entity) {
        List<SignalNodeEntity> list = super.getBaseMapper().getOnLineList(entity);
        long count = list.stream().filter(e -> ((double) e.getOnLineNum() / e.getBearNum()) > 0.8).count();
        List<CountDto> res = new ArrayList<>();
        res.add(CountDto.builder().type1(0).longCount(count).build());
        res.add(CountDto.builder().type1(1).longCount(list.size() - count).build());
        return res;
    }

    @Override
    public SignalNodeEntity getSignalNodeByDeviceId(Long deviceId) {
        DeviceEntity device = deviceMapper.selectById(deviceId);
        if (device == null) {
            throw new BizRuntimeException("设备不存在");
        }
        if (StringUtil.isEmpty(device.getNodeId())) {
            throw new BizRuntimeException("设备未绑定节点");
        }
        SignalNodeEntity node = getById(device.getNodeId());
        if (node == null) {
            throw new BizRuntimeException("设备节点不存在");
        }
        return node;
    }
}