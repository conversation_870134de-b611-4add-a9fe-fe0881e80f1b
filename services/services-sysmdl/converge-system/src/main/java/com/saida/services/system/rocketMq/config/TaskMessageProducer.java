package com.saida.services.system.rocketMq.config;

import com.alibaba.fastjson2.JSON;
import com.saida.services.common.mq.vlinker.BaseMessage;
import com.saida.services.common.mq.vlinker.SendResultWrapper;
import com.saida.services.common.mq.vlinker.VLinkerMqTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TaskMessageProducer {

    @Autowired(required = false)
    private VLinkerMqTemplate vLinkerMqTemplate;

    /**
     * 同步发送消息
     *
     * @param topic   消息主题
     * @param message 消息内容
     */
    public void sendSync(String topic, BaseMessage message) {
        SendResultWrapper sendResult = null;
        try {
            // 如果消息发送不成功，则再次重新发送，如果发送异常则抛出由MQ再次处理(异常时不走延迟消息)
            if (message.getTag() == null) {
                sendResult = vLinkerMqTemplate.send(topic, message);
            } else {
                sendResult = vLinkerMqTemplate.send(topic, message.getTag(), message);
            }
        } catch (Exception ex) {
            // 此处捕获之后，相当于此条消息被消息完成然后重新发送新的消息
            // 由生产者直接发送
            throw new RuntimeException(ex);
        }
        log.info("topic:{} message:{} 发送结果：{}", topic, JSON.toJSONString(message), sendResult.isSuccess());
        // 发送失败的处理就是不进行ACK，由RocketMQ重试
        if (sendResult.isSuccess()) {
            throw new RuntimeException("重试消息发送失败");
        }
    }


}
