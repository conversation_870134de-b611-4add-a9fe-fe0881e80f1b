package com.saida.services.system.rocketMq.alg;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.saida.services.algorithm.dto.AlgorithmReceiveAlarmMqDto;
import com.saida.services.algorithm.entity.AlgorithmManageEntity;
import com.saida.services.algorithm.entity.AlgorithmMappingEntity;
import com.saida.services.common.dto.AlarmNormalizationExt;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.common.tools.DateUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.enums.AlgAlgorithmSourceEnum;
import com.saida.services.system.alarm.entity.AlarmEntity;
import com.saida.services.system.alarm.service.AlarmService;
import com.saida.services.system.algorithm.service.AlgorithmManageService;
import com.saida.services.system.algorithm.service.AlgorithmMappingService;
import com.saida.services.system.analyse.pojo.entity.TaskDispatchEntity;
import com.saida.services.system.analyse.service.TaskDispatchService;
import com.saida.services.system.callback.CallBackInvoke;
import com.saida.services.system.callback.CallBackMessage;
import com.saida.services.system.device.entity.CameraEntity;
import com.saida.services.system.device.entity.CloudServerEntity;
import com.saida.services.system.device.service.CameraService;
import com.saida.services.system.device.service.CloudServerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Optional;

/**
 * 告警监听
 */
@Slf4j
@Component

public class AnalysisAlarmMessageListener implements VLinkerMqMessageListener {


    @Resource
    private CameraService cameraService;
    @Resource
    private TaskDispatchService taskDispatchService;
    @Resource
    private AlarmService alarmService;
    @Resource
    private CloudServerService cloudServerService;
    @Resource
    private CallBackInvoke callBackInvoke;
    @Lazy
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private AlgorithmMappingService algorithmMappingService;
    @Resource
    private AlgorithmManageService algorithmManageService;


    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("analysis_alarm")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        receive(messageBody);
    }

    public void receive(String message) {
        String logId = IdUtil.fastSimpleUUID();
        log.info("V-LINKER算法平台.rocketMq接收告警消息...logId={},data:{}", logId, message);
        try {
            AlgorithmReceiveAlarmMqDto dto = JSON.parseObject(message, AlgorithmReceiveAlarmMqDto.class);
            String algorithmCode = dto.getAlgorithmCode();
            Long deviceId = dto.getDeviceId();
            Long time = dto.getTime();

            if (StringUtil.isEmpty(algorithmCode)) {
                log.info("V-LINKER算法平台.rocketMq接收告警消息..算法名称为空...logId={}", logId);
                return;
            }
            if (null == deviceId) {
                log.info("V-LINKER算法平台.rocketMq接收告警消息..设备ID为空...logId={}", logId);
                return;
            }
            CameraEntity cameraEntity = cameraService.getInfo(deviceId);
            if (null == cameraEntity) {
                log.info("V-LINKER算法平台.rocketMq接收告警消息..对应的设备不存在...logId={}", logId);
                return;
            }

            AlarmEntity alarmEntity = new AlarmEntity();
            alarmEntity.setTaskId(dto.getTaskId());
            alarmEntity.setMsgId(String.valueOf(dto.getMsgId()));
            alarmEntity.setExt(JSON.toJSONString(dto.getAlgorithmsResult()));
            // 处理云服务
            TaskDispatchEntity taskDispatchEntity = taskDispatchService.getById(dto.getTaskId());
            if (null == taskDispatchEntity) {
                log.info("V-LINKER算法平台.rocketMq接收告警消息..对应的任务不存在...logId={} taskId:{}", logId, dto.getTaskId());
                return;
            }
            Long cloudServerId = taskDispatchEntity.getCloudServerId();
            if (null != cloudServerId) {
                alarmEntity.setCloudServerId(cloudServerId);
                CloudServerEntity cloudServerEntity = cloudServerService.getById(cloudServerId);
                if (null != cloudServerEntity) {
                    alarmEntity.setRemark(String.format("%s（%s）", AlgAlgorithmSourceEnum.CLOUD_SERVICE.getName(), cloudServerEntity.getName()));
                }
            }
            if (null == cloudServerId) {
                log.info("V-LINKER算法平台.rocketMq接收告警消息..对应的云服务不存在...logId={}", logId);
                return;
            }

            List<AlgorithmMappingEntity> algorithmMappingEntityList = algorithmMappingService.list(new LambdaUpdateWrapper<AlgorithmMappingEntity>()
                    .eq(AlgorithmMappingEntity::getSourceId, cloudServerId)
                    .eq(AlgorithmMappingEntity::getCode, algorithmCode));
            if (CollectionUtil.isEmpty(algorithmMappingEntityList)) {
                log.info("V-Linker算法中台.接收赛达视频分析平台告警数据..对应的云服务算法不存在...logId={} cloudServerId:{},algorithmName:{}", logId, cloudServerId, algorithmCode);
                return;
            }
            AlarmNormalizationExt alarmNormalizationExt = new AlarmNormalizationExt();
            for (AlgorithmReceiveAlarmMqDto.AlgorithmsResult algorithmsResult : dto.getAlgorithmsResult()) {
                alarmNormalizationExt.addBoxInfoDto(AlarmNormalizationExt
                        .BoxDto
                        .builder()
                        .boxId(algorithmsResult.getLabel())
                        .x(algorithmsResult.getMinx())
                        .y(algorithmsResult.getMiny())
                        .width(algorithmsResult.getMaxx() - algorithmsResult.getMinx())
                        .height(algorithmsResult.getMaxy() - algorithmsResult.getMiny())
                        .abd_evt_type(algorithmsResult.getAbd_evt_type())
                        .cpc_evt_type(algorithmsResult.getCpc_evt_type())
                        .build());
                alarmNormalizationExt.setInOutCountDto(AlarmNormalizationExt.InOutCountDto.builder()
                        .inNum(algorithmsResult.getIn_num())
                        .outNum(algorithmsResult.getOut_num())
                        .build());
            }

            alarmEntity.setNormalizationExt(JSON.toJSONString(alarmNormalizationExt));

            String imageUrl = dto.getImage();
            String analysisImageNotProbUrl = dto.getAnalysisImageNotProb();
            String analysisImageUrl = dto.getAnalysisImage();
            List<AlgorithmManageEntity> algorithmManageEntityList = algorithmManageService.list(new LambdaQueryWrapper<AlgorithmManageEntity>()
                    .eq(AlgorithmManageEntity::getStatus, 1));
            for (AlgorithmMappingEntity algorithmMappingEntity : algorithmMappingEntityList) {
                Long algorithmId = algorithmMappingEntity.getAlgorithmId();
                AlarmEntity saveAlarmEntity = new AlarmEntity();
                BeanUtils.copyProperties(alarmEntity, saveAlarmEntity);

                saveAlarmEntity.setDeviceId(deviceId);
                saveAlarmEntity.setAlarmSource(AlgAlgorithmSourceEnum.CLOUD_SERVICE.getDicId());
                saveAlarmEntity.setAlgorithmId(algorithmId);
                saveAlarmEntity.setOriginalAlarmStr("vlinker-cloud:" + algorithmCode);

                SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtil.DATE_TIME_PATTERN);
                String alarmDateTime = simpleDateFormat.format(time);
                if (StringUtil.isNotEmpty(alarmDateTime)) {
                    saveAlarmEntity.setYear(alarmDateTime.substring(0, 4));
                    saveAlarmEntity.setMonth(alarmDateTime.substring(0, 7));
                    saveAlarmEntity.setDay(alarmDateTime.substring(0, 10));
                    saveAlarmEntity.setTime(alarmDateTime.substring(11));
                    saveAlarmEntity.setAlarmTime(alarmDateTime);
                }
                saveAlarmEntity.setAlarmTimeLong(time);
                if (StringUtil.isNotEmpty(imageUrl)) {
                    saveAlarmEntity.setOriginalImageUrl(imageUrl);
                }
                if (StringUtil.isNotEmpty(analysisImageNotProbUrl)) {
                    saveAlarmEntity.setAlarmImageNotProbUrl(analysisImageNotProbUrl);
                }
                if (StringUtil.isNotEmpty(analysisImageUrl)) {
                    saveAlarmEntity.setAlarmImageUrl(analysisImageUrl);
                }
                Long alarmId = IdWorker.getId();
                saveAlarmEntity.setId(alarmId);
                Optional<AlgorithmManageEntity> optional = algorithmManageEntityList.stream()
                        .filter(item -> item.getId().equals(algorithmId)).findFirst();
                if (optional.isEmpty()) {
                    log.info("V-Linker算法中台.接收赛达视频分析平台告警数据..告警算法不存在 algorithmId:{}...logId={}", algorithmId, logId);
                    continue;
                }
                if (optional.get().getStatus() != 1) {
                    log.info("V-Linker算法中台.接收赛达视频分析平台告警数据..告警算法未启用...logId={}", logId);
                    continue;
                }
                String finalAlertTypeName = optional.get().getName();
                CallBackMessage callBackMessage = new CallBackMessage() {{
                    log.info("V-Linker算法中台.接收赛达视频分析平台告警数据..向第三方推送告警数据...logId={}", logId);
                    setAlarmId(alarmId);
                    setMsgId(logId);
                    setDeviceId(String.valueOf(deviceId));
                    setDeviceCode(cameraEntity.getThirdCode());
                    setChannelId(cameraEntity.getChannelId());
                    setAlgId(algorithmId);
                    setAlertType(String.valueOf(algorithmId));
                    setAlertTypeName(finalAlertTypeName);
                    setCreateTime(alarmDateTime);
                    setAlertSource(Math.toIntExact(AlgAlgorithmSourceEnum.CLOUD_SERVICE.getTag()));
                    setAlertSourceName(AlgAlgorithmSourceEnum.CLOUD_SERVICE.getName());
                    setOriginalSrcUrl(imageUrl);
                    setSrcNotProbUrl(analysisImageNotProbUrl);
                    setSrcUrl(analysisImageUrl);
                    setOriginalAlarmStr(saveAlarmEntity.getOriginalAlarmStr());
                    setExt(JSON.toJSONString(dto.getAlgorithmsResult()));
                    setNormalizationExt(alarmNormalizationExt);
                }};
                saveAlarmEntity.setCallbackMessage(JSON.toJSONString(callBackMessage));
                // 3.把告警信息推送给第三方
                threadPoolTaskExecutor.execute(() -> callBackInvoke.callBack(saveAlarmEntity, callBackMessage, true, true));
            }
        } catch (Exception e) {
            log.info("V-LINKER算法平台.rocketMq接收告警消息..错误...logId={}, msg={}", logId, e.getMessage(), e);
        } finally {
            log.info("V-LINKER算法平台.rocketMq接收告警消息..结束...logId={}", logId);
        }
    }
}

