/*
package com.saida.services.system.thread;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.mq.rocketMq.RocketMQEnhanceTemplate;
import com.saida.services.common.mq.message.AnalyseTaskBatchMessage;
import com.saida.services.common.tools.CustomerListUtil;
import com.saida.services.system.analyse.pojo.entity.AnalyseTaskBatchEntity;
import com.saida.services.system.analyse.pojo.entity.AnalyseTaskEntity;
import com.saida.services.system.analyse.pojo.vo.AnalyseTaskByBatchIdVo;
import com.saida.services.system.analyse.service.AnalyseTaskBatchService;
import com.saida.services.system.analyse.service.AnalyseTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@Async(ThreadPoolConfig.TASK_EXECUTOR_BEAN_NAME)
public class SendToMqThread {

    @Resource
    private AnalyseTaskService analyseTaskService;
    @Resource
    private SendToCloudServerThread sendToCloudServerThread;
    @Autowired(required = false)
    private RocketMQEnhanceTemplate rocketMQEnhanceTemplate;
    @Resource
    @Lazy
    private AnalyseTaskBatchService analyseTaskBatchService;

    public static final String analyseTaskBatchTopic = "analyse_task_batch";

    */
/**
     * 发送任务批次消息
     *//*

    public void sendAnalyseTaskBatchMq(AnalyseTaskBatchEntity analyseTaskBatchEntity) {
        try {
            // 查询该批次下的任务
            List<AnalyseTaskByBatchIdVo> analyseTaskEntityList = analyseTaskService.getAnalyseTaskByBatchId(analyseTaskBatchEntity.getId(), null);
            if (CollectionUtil.isEmpty(analyseTaskEntityList)) {
                log.info("V-LINKER算法中台.调度中心，任务轮询，发送批次任务到MQ...任务列表为空，程序结束");
                return;
            }
            Map<Long, List<AnalyseTaskByBatchIdVo>> map = analyseTaskEntityList.stream().collect(Collectors.groupingBy(AnalyseTaskEntity::getDeviceId));
            // 查询总任务
            List<AnalyseTaskByBatchIdVo> allAnalyseTaskEntityList = analyseTaskService.getAnalyseTaskByBatchId(null, null);
            Set<Long> keySet = map.keySet();

            Map<Long, List<AnalyseTaskByBatchIdVo>> updateChannelMap = new HashMap<>();

            Map<Long, List<AnalyseTaskByBatchIdVo>> setAiParamsMap = new HashMap<>();
            for (Long deviceId : keySet) {
                List<AnalyseTaskByBatchIdVo> analyseTaskEntityListTemp = map.get(deviceId);
                // 查询该设备下所有的分析任务
                List<AnalyseTaskByBatchIdVo> allAnalyseTaskEntityListTemp = allAnalyseTaskEntityList.stream().filter(t1 -> Objects.equals(t1.getDeviceId(), deviceId)).collect(Collectors.toList());
                // 如果分析任务数量相等
                if (analyseTaskEntityListTemp.size() == allAnalyseTaskEntityListTemp.size()) {
                    // 需要启用通道的设备
                    updateChannelMap.put(deviceId, analyseTaskEntityListTemp);
                    continue;
                }
                List<AnalyseTaskByBatchIdVo> analyseTaskByBatchIdVoList = new ArrayList<>();
                for (AnalyseTaskByBatchIdVo analyseTaskByBatchIdVo : allAnalyseTaskEntityListTemp) {
                    if (1 == analyseTaskByBatchIdVo.getEnable()) {
                        analyseTaskByBatchIdVoList.add(analyseTaskByBatchIdVo);
                    }
                }
                // 需要设置分析算法的设备
                setAiParamsMap.put(deviceId, analyseTaskByBatchIdVoList);
            }
            // 启用通道
            if (CollectionUtil.isNotEmpty(updateChannelMap)) {
                List<List<Long>> partList = CustomerListUtil.subListByPart(Lists.newArrayList(keySet), 10);
                for (List<Long> subList : partList) {
                    List<AnalyseTaskByBatchIdVo> analyseTaskByBatchIdVoList = new ArrayList<>();
                    for (Long deviceId : subList) {
                        analyseTaskByBatchIdVoList.addAll(updateChannelMap.get(deviceId));
                    }
                    sendToCloudServerThread.enableChannel(analyseTaskByBatchIdVoList, null);
                }
            }
            // 设置分析参数
            if (CollectionUtil.isNotEmpty(setAiParamsMap)) {
                Set<Long> setAiParamsMapKeySet = setAiParamsMap.keySet();
                for (Long deviceId : setAiParamsMapKeySet) {
                    AnalyseTaskByBatchIdVo analyseTaskByBatchIdVo = new AnalyseTaskByBatchIdVo();
                    analyseTaskByBatchIdVo.setDeviceId(deviceId);
                    analyseTaskByBatchIdVo.setCloudServerId(setAiParamsMap.get(deviceId).get(0).getCloudServerId());

                    Set<Long> enableTaskIdSet = setAiParamsMap.get(deviceId).stream().map(AnalyseTaskByBatchIdVo::getId).collect(Collectors.toSet());
                    sendToCloudServerThread.setAiParamsList(analyseTaskByBatchIdVo, enableTaskIdSet, null);
                }
            }
            AnalyseTaskBatchMessage analyseTaskBatchMessage = new AnalyseTaskBatchMessage();
            analyseTaskBatchMessage.setAnalyseTaskBatchId(analyseTaskBatchEntity.getId());
            SendResult sendResult = rocketMQEnhanceTemplate.send(analyseTaskBatchTopic, analyseTaskBatchMessage, analyseTaskBatchEntity.getAnalyzeTime());
            if (sendResult.getSendStatus() == SendStatus.SEND_OK) {
                analyseTaskBatchService.update(new LambdaUpdateWrapper<AnalyseTaskBatchEntity>()
                        .eq(AnalyseTaskBatchEntity::getId, analyseTaskBatchEntity.getId())
                        .set(AnalyseTaskBatchEntity::getInProgress, 1)
                        .set(AnalyseTaskBatchEntity::getLastExecuteBegTime, LocalDateTime.now())
                );
            }
            log.info("V-LINKER算法中台.调度中心，任务轮询，发送批次任务到MQ...message={}, sendResult={}", JSON.toJSON(analyseTaskBatchEntity), JSON.toJSON(sendResult));
        } catch (Exception e) {
            analyseTaskBatchService.update(new LambdaUpdateWrapper<AnalyseTaskBatchEntity>()
                    .eq(AnalyseTaskBatchEntity::getId, analyseTaskBatchEntity.getId())
                    .set(AnalyseTaskBatchEntity::getInProgress, 0)
                    .set(AnalyseTaskBatchEntity::getLastExecuteBegTime, LocalDateTime.now())
            );
            log.info("V-LINKER算法中台.调度中心，任务轮询，发送批次任务到MQ..错误...message={}, ", e.getMessage(), e);
        }
    }
}
*/
