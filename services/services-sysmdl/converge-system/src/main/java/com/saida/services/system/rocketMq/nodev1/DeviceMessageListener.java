package com.saida.services.system.rocketMq.nodev1;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.common.mq.message.DeviceMessage;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.DeviceEntity;
import com.saida.services.converge.entity.OpsDeviceDiscoveryEntity;
import com.saida.services.system.job.UpdateDeviceChannelJob;
import com.saida.services.system.ops.service.DeviceModelVersionService;
import com.saida.services.system.ops.service.DeviceService;
import com.saida.services.system.ops.service.OpsDeviceDiscoveryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Date;

@Slf4j
@Component("rocket_mq_listener_device_status")

public class DeviceMessageListener implements VLinkerMqMessageListener {

    @Resource
    private DeviceService deviceService;
    @Resource
    private OpsDeviceDiscoveryService opsDeviceDiscoveryService;
    @Resource
    private UpdateDeviceChannelJob updateDeviceChannelJob;

    @Resource
    private DeviceModelVersionService deviceModelVersionService;

    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("device_status")
                .tag("*")
                .writeQueueNums(1)
                .readQueueNums(1)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        // 此时这里才是最终的业务处理，代码只需要处理资源类关闭异常，其他的可以交给父类重试
        String s = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        DeviceMessage message = JSON.parseObject(s, DeviceMessage.class);
        if (message == null) {
            return;
        }
        log.info("[mq:device_status]收到消息：{}", message);
        if (StringUtil.isEmpty(message.getDevice_id())) {
            log.error("参数错误");
            return;
        }
        deviceModelVersionService.buildDeviceVersion(message.getModel(), message.getVersion());
        DeviceEntity device = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                .eq(DeviceEntity::getDeviceCode, message.getDevice_id()));
        if (device == null) {
            if (StringUtil.isBlank(vlinkerMqMessage.getTag())) {
                log.error("[mq:{}]设备编号：{} 未入库 放在设备发现中-> 但是nodeId是空的！", vlinkerMqMessage.getTopic(), message.getDevice_id());
                return;
            }
            log.error("设备编号：{} 未入库 放在设备发现中->", message.getDevice_id());
            OpsDeviceDiscoveryEntity deviceDiscoveryEntity = opsDeviceDiscoveryService.getOne(new LambdaQueryWrapper<OpsDeviceDiscoveryEntity>()
                    .eq(OpsDeviceDiscoveryEntity::getNodeId, vlinkerMqMessage.getTag())
                    .eq(OpsDeviceDiscoveryEntity::getDeviceCode, message.getDevice_id()));
            if (deviceDiscoveryEntity == null) {
                deviceDiscoveryEntity = new OpsDeviceDiscoveryEntity();
                deviceDiscoveryEntity.setCreateTime(new Date());
                if (message.getStatus() != 1) {
                    // 不在线的要求不落库
                    return;
                }
            } else {
                // 存在了 又下线了 删掉
                if (message.getStatus() != 1) {
                    opsDeviceDiscoveryService.removeById(deviceDiscoveryEntity.getId());
                    return;
                }
            }
            deviceDiscoveryEntity.setDeviceCode(message.getDevice_id());
            deviceDiscoveryEntity.setNodeId(vlinkerMqMessage.getTag());
            if (StringUtil.isBlank(message.getDevice_name())) {
                deviceDiscoveryEntity.setName(message.getDevice_id());
            } else {
                deviceDiscoveryEntity.setName(message.getDevice_name());
            }
            deviceDiscoveryEntity.setModel(message.getModel());
            deviceDiscoveryEntity.setVersion(message.getVersion());
            deviceDiscoveryEntity.setEip(message.getEip());
            deviceDiscoveryEntity.setIip(message.getIip());
            deviceDiscoveryEntity.setStatus(message.getStatus());
            deviceDiscoveryEntity.setCatalogSubscribe(message.getCatalog_subscribe());
            deviceDiscoveryEntity.setAlarmSubscribe(message.getAlarm_subscribe());
            deviceDiscoveryEntity.setPositionSubscribe(message.getPosition_subscribe());
            deviceDiscoveryEntity.setUpdateTime(new Date());
            boolean b = opsDeviceDiscoveryService.saveOrUpdate(deviceDiscoveryEntity);
            log.info("设备发现入库结果：{} deviceDiscoveryEntity:{}", b, deviceDiscoveryEntity);
            return;
        } else {
            // 设备已经入库了 这个只是保全机制
            opsDeviceDiscoveryService.remove(new LambdaQueryWrapper<OpsDeviceDiscoveryEntity>()
                    .eq(OpsDeviceDiscoveryEntity::getDeviceCode, message.getDevice_id())
                    .eq(OpsDeviceDiscoveryEntity::getNodeId, vlinkerMqMessage.getTag()));
        }
        deviceService.updateByDeviceMessage(message, device);
        try {
            log.info("更新设备通道 deviceCode:{},status:{}", device.getDeviceCode(), message.getStatus());
            if (message.getStatus() == 1) {
                updateDeviceChannelJob.updateDeviceChannelBySleep(device.getDeviceCode());
            } else {
                //离线 offline
                updateDeviceChannelJob.updateDeviceChannelByOffline(device.getDeviceCode());
            }
        } catch (Exception e) {
            log.error("更新设备通道异常 device:{}", device, e);
        }
    }
}