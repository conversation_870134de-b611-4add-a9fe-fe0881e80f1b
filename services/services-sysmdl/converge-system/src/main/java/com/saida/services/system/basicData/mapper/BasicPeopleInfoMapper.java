package com.saida.services.system.basicData.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.saida.services.system.basicData.dto.BasicPeoplePageQueryDto;
import com.saida.services.system.basicData.entity.BasicPeopleInfoEntity;
import com.saida.services.system.basicData.vo.BasicPeoplePageQueryVo;
import com.saida.services.system.basicData.vo.PeoplePhotoGroupVo;
import com.saida.services.system.basicData.vo.PeopleRelateVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 基础信息-人员信息表(BasicPeopleInfoEntity)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-05 15:20:01
 */
@Mapper
public interface BasicPeopleInfoMapper extends BaseMapper<BasicPeopleInfoEntity> {

    List<BasicPeoplePageQueryVo> queryByPage(@Param("dto") BasicPeoplePageQueryDto dto);

    List<PeoplePhotoGroupVo> getPeoplePhotoAndGroupIdByPhotoId(@Param("photoIds") List<String> ids);

    PeopleRelateVo selectPeopleRelateInfo(Long peopleId, Long faceId);
}

