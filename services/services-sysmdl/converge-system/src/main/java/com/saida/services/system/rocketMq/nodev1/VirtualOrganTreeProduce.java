package com.saida.services.system.rocketMq.nodev1;

import com.alibaba.fastjson.JSONObject;
import com.saida.services.common.mq.rocketMq.RocketMQEnhanceTemplate;
import com.saida.services.common.mq.message.SharePlatformMessage;
import com.saida.services.common.mq.message.VirtualOrganTreeMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class VirtualOrganTreeProduce {

    //注入增强后的模板，可以自动实现环境隔离，日志记录
    @Autowired(required = false)
    private RocketMQEnhanceTemplate rocketMQEnhanceTemplate;



    /**
     * 同步消息
     */
    public void sync(VirtualOrganTreeMessage message) {
        SendResult send = rocketMQEnhanceTemplate.send(InOutPlatformProduce.VIRTUAL_ORGAN_TREE, message);
        log.info("消息内容：{}, 发送结果：{}", JSONObject.toJSONString(message), send);
    }

    public void sendPlatformShareMessage(SharePlatformMessage message) {
        SendResult send = rocketMQEnhanceTemplate.send(InOutPlatformProduce.VIRTUAL_ORGAN_TREE, message);
        log.info("平台消息:{}，发送结果:{}", JSONObject.toJSONString(message), send);
    }
}
