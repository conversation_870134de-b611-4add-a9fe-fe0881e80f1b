package com.saida.services.system.sys.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONValidator;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.config.S3Config;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.DeviceEntity;
import com.saida.services.converge.entity.SignalNodeEntity;
import com.saida.services.feign.srv.system.IFeignSrvSystemApiController;
import com.saida.services.srv.dto.SrvAddDeviceByWifiDTO;
import com.saida.services.srv.vo.BCBindDeviceVo;
import com.saida.services.system.ops.service.DeviceService;
import com.saida.services.system.ops.service.SignalNodeService;
import com.saida.services.system.sys.dto.AlarmReportingDto;
import com.saida.services.system.sys.dto.NodeRespDto;
import com.saida.services.system.sys.dto.SignalNodeDto;
import com.saida.services.system.sys.dto.SignalNodeReqDto;
import com.saida.services.system.video.service.impl.v2.VideoNodeV2StreamServiceImpl;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.Executor;

/**
 * 私有协议主入口
 */
@Slf4j
@RestController
@RequestMapping("saidaSdk/login")
public class DispatchControllerByUdp {

    @Resource
    private DeviceService deviceService;

    @Resource
    private SignalNodeService signalNodeService;

    @Autowired(required = false)
    private S3Config s3Config;

    private static final String aesKey = "5RdW5Y5@W#HsD7FT";

    private static final AES aes = new AES(aesKey.getBytes());
    @Resource
    private IFeignSrvSystemApiController feignSrvSystemApiController;
    @Lazy
    @Resource(name = ThreadPoolConfig.TASK_EXECUTOR_BEAN_NAME)
    private Executor taskExecutor;

    @PostMapping("/v1/signalNode")
    public String signalNode(@RequestBody SignalNodeReqDto param) {
        try {
            if (param == null || StringUtil.isEmpty(param.getParam())) {
                return Result.error(ResultStatusCode.PARAM_ERROR.CODE, null, null).toJsonString();
            }
            String jsonstr = null;
            try {
                jsonstr = aes.decryptStr(param.getParam());
            } catch (Exception e) {
                log.error("aes 解密失败！:{}", e.getMessage(), e);
                return Result.error(ResultStatusCode.PARAM_ERROR.CODE, null, null).toJsonString();
            }
            if (jsonstr == null || !JSONValidator.from(jsonstr).validate()) {
                return Result.error(ResultStatusCode.PARAM_ERROR.CODE, null, null).toJsonString();
            }
            log.info("signalNode param = {} signature:{}", jsonstr, param.getSignature());
            SignalNodeDto dto = null;
            try {
                dto = JSON.toJavaObject(JSON.parseObject(jsonstr), SignalNodeDto.class);
            } catch (Exception e) {
                log.error("signalNode 转对象失败！{}", e.getMessage(), e);
                return Result.error(ResultStatusCode.PARAM_ERROR.CODE, null, null).toJsonString();
            }

            if (dto == null || StringUtil.isEmpty(dto.getSn()) || dto.getTimestamp() == null) {
                return Result.error(ResultStatusCode.PARAM_ERROR.CODE, null, null).toJsonString();
            }
            //签名格式： md5(设备SN+时间戳+序列化参数)
            String signature = param.getSignature();
            if (StringUtil.isEmpty(signature)) {
                return Result.error(ResultStatusCode.SIGNATURE_ERROR.CODE, null, null).toJsonString();
            }
            String format = String.format("%s%s%s", dto.getSn(), dto.getTimestamp(), JSON.toJSONString(dto, SerializerFeature.SortField));
            String checkSignature = SecureUtil.md5(format);
            if (!signature.equals(checkSignature)) {
                log.error("签名不匹配 -> format={}, signature={}, calcSignature={}", format, signature, checkSignature);
//                return Result.error(ResultStatusCode.SIGNATURE_ERROR.CODE, null, null).toJsonString();
            }
            if (Math.abs(DateTime.now().getTime() - dto.getTimestamp()) > (5 * 60 * 1000L)) {//与服务器时间不同步
                return Result.error(ResultStatusCode.TIME_OUT_SYNC.CODE, null, null).toJsonString();
            }
            DeviceEntity device = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>().eq(DeviceEntity::getDeviceCode, dto.getSn()), false);
            if (device == null) {
                return Result.error(ResultStatusCode.NOT_FOUND_DEVICE.CODE, null, null).toJsonString();
            }
            if (StringUtil.isEmpty(device.getNodeId())) {
                return Result.error(ResultStatusCode.NOT_FOUND_NODE.CODE, null, null).toJsonString();
            }
            SignalNodeEntity node = signalNodeService.getById(device.getNodeId());
            if (node == null) {
                return Result.error(ResultStatusCode.NOT_FOUND_NODE.CODE, null, null).toJsonString();
            }
//            if (StringUtil.isEmpty(node.getSipIp()) || node.getUniversePort() == null) {
//                return Result.error(ResultStatusCode.NOT_FOUND_NODE.CODE, null, null).toJsonString();
//            }
            if (StringUtil.isEmpty(device.getSignalingAddr())) {
                return Result.error(ResultStatusCode.NOT_FOUND_NODE.CODE, null, null).toJsonString();
            }
            String[] split = device.getSignalingAddr().split(":");
            log.info("获取到信令IP：{}, PORT: {},siAddr:{}", node.getSipIp(), node.getSipPort(), device.getSignalingAddr());
            NodeRespDto nodeRespDto = new NodeRespDto() {{
                setHost(split[0]);
                setAuthKey(device.getAuthKey());
                setPlatformId(VideoNodeV2StreamServiceImpl.getPlatformId());
                setPort(Integer.parseInt(split[1]));
                setRegion("");
                setAccessKey("");
                setSecretKey("");
                setEndPoint("");
                setReturnPoint("");
                setBucket("log");
            }};
            if (StringUtil.isNotEmpty(dto.getQrCodeContent())) {
                log.info("qrCodeContent:{}", dto.getQrCodeContent());
                String token = dto.getQrCodeContent().split(" ")[1];
                SrvAddDeviceByWifiDTO srvAddDeviceByWifiDTO = new SrvAddDeviceByWifiDTO();
                srvAddDeviceByWifiDTO.setDeviceSn(device.getDeviceCode());
                srvAddDeviceByWifiDTO.setDeviceName(device.getName());
                srvAddDeviceByWifiDTO.setToken(token);
                DtoResult<BCBindDeviceVo> bcBindDeviceVoDtoResult = feignSrvSystemApiController.addCustomerDevice(srvAddDeviceByWifiDTO);
                log.info("addCustomerDevice:{}", bcBindDeviceVoDtoResult);
                if (!bcBindDeviceVoDtoResult.success()) {
                    // 改为1 代表绑定失败
                    nodeRespDto.setPort(1);
                }
            }
            return Result.ok(nodeRespDto).toJsonString();
        } catch (Exception ex) {
            log.error("signalNode 异常！", ex);
            return Result.error(500, "系统升级中，请稍后", null).toJsonString();
        }
    }

    public static void main(String[] args) {


        SignalNodeDto dto = new SignalNodeDto();
        System.out.println(JSON.toJSONString(dto, SerializerFeature.SortField));


    }


    @PostMapping("/v1/alarmReporting")
    public String alarmReporting(@RequestBody AlarmReportingDto dto, HttpServletRequest request) {
        try {
            log.info("alarmReporting param = {}", JSON.toJSONString(dto));
            if (dto == null || StringUtil.isEmpty(dto.getSn()) || dto.getTimestamp() == null) {
                return Result.error(ResultStatusCode.PARAM_ERROR.CODE, null, null).toJsonString();
            }
            DeviceEntity device = deviceService.getOne(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getDeviceCode, dto.getSn()), false);
            if (device == null) {
                return Result.error(ResultStatusCode.NOT_FOUND_DEVICE.CODE, null, null).toJsonString();
            }
            if (StringUtil.isEmpty(device.getNodeId())) {
                return Result.error(ResultStatusCode.NOT_FOUND_NODE.CODE, null, null).toJsonString();
            }
            return Result.ok().toJsonString();
        } catch (Exception ex) {
            log.error("alarmReporting 异常！", ex);
            return Result.error(500, "系统升级中，请稍后", null).toJsonString();
        }
    }

    @Getter
    @AllArgsConstructor
    enum ResultStatusCode {
        /**
         * 签名错误
         */
        SIGNATURE_ERROR(44001, "签名错误"),

        /**
         * 参数错误
         */
        PARAM_ERROR(44002, "参数错误"),

        /**
         * 设备未入库
         */
        NOT_FOUND_DEVICE(44003, "设备未入库"),

        /**
         * 未找到设备归属节点
         */
        NOT_FOUND_NODE(44004, "未找到设备归属节点"),

        /**
         * 与服务器时间不同步
         */
        TIME_OUT_SYNC(44005, "与服务器时间不同步");

        public final Integer CODE;
        private final String MSG;
    }
}
