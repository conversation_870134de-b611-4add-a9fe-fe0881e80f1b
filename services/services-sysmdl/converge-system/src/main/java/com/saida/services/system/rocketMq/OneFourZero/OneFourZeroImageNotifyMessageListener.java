package com.saida.services.system.rocketMq.OneFourZero;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.dto.AlarmNormalizationExt;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.common.service.FileService;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.OpsDeviceChannelEntity;
import com.saida.services.entities.pojo.FileModel;
import com.saida.services.system.ops.service.OpsDeviceAlarmService;
import com.saida.services.system.ops.service.OpsDeviceChannelService;
import com.saida.services.system.rocketMq.dto.*;
import com.saida.services.system.sys.dto.AlarmNotifyDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Component

public class OneFourZeroImageNotifyMessageListener implements VLinkerMqMessageListener {


    @Resource
    private FileService fileService;

    @Resource
    private OpsDeviceAlarmService opsDeviceAlarmService;

    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;


    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("1400_send_msg")
                .tag("image")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        log.info("image收到消息tag:{},：{}", vlinkerMqMessage.getTag(), messageBody);
        OneFourZeroImageNotifyDto oneFourZeroImageNotifyDto = JSONObject.parseObject(messageBody, OneFourZeroImageNotifyDto.class);
        log.info("image收到消息：{}", oneFourZeroImageNotifyDto);
        Integer JuLongType = null;
        if (oneFourZeroImageNotifyDto.getImageInfo().getEventSort() != null && oneFourZeroImageNotifyDto.getImageInfo().getEventSort() != 0) {
            /**
             * 10：人脸检测
             * 10000：电动车未戴头盔检测
             * 10001：二轮车超载
             * 10002：三轮车违规载人
             */
            if (oneFourZeroImageNotifyDto.getImageInfo().getEventSort() == 10) {
                JuLongType = 11400;
            } else if (oneFourZeroImageNotifyDto.getImageInfo().getEventSort() == 10000) {
                JuLongType = 11401;
            } else if (oneFourZeroImageNotifyDto.getImageInfo().getEventSort() == 10001) {
                JuLongType = 11402;
            } else if (oneFourZeroImageNotifyDto.getImageInfo().getEventSort() == 10002) {
                JuLongType = 11403;
            }
        }
        OneFourZeroImageNotifyDto.FaceListDto faceList = oneFourZeroImageNotifyDto.getFaceList();
        if (faceList != null && faceList.getFaceObject() != null && !faceList.getFaceObject().isEmpty()) {
            for (OneFourZeroFaceNotifyDto oneFourZeroFaceNotifyDto : faceList.getFaceObject()) {
                if (StringUtil.isEmpty(oneFourZeroFaceNotifyDto.getDeviceID())) {
                    log.info("1400_face收到消息：但是没有设备id 过滤");
                    return;
                }
                OpsDeviceChannelEntity channelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                        .eq(OpsDeviceChannelEntity::getDeleteFlag, 1)
                        .eq(OpsDeviceChannelEntity::getOneFourCode, oneFourZeroFaceNotifyDto.getDeviceID()), false);
                if (channelEntity == null) {
                    log.info("1400_face收到消息：但是没有设备 过滤 1400编号:{}", oneFourZeroFaceNotifyDto.getDeviceID());
                    return;
                }

                List<String> imgUrl = new ArrayList<>();

                AtomicReference<DateTime> time = new AtomicReference<>(new DateTime());
                String dateFormat = DateUtil.format(new Date(), "yyyyMMdd");
                if (oneFourZeroFaceNotifyDto.getSubImageList() != null && oneFourZeroFaceNotifyDto.getSubImageList().getSubImageInfoObject() != null) {
                    oneFourZeroFaceNotifyDto.getSubImageList().getSubImageInfoObject().forEach(item -> {
                        String storagePath = item.getStoragePath();
                        if (StringUtil.isNotEmpty(item.getShotTime())){
                            time.set(DateUtil.parse(item.getShotTime(), "yyyyMMddHHmmss"));
                        }
                        String ossObjKey = "alarm/1400/" + oneFourZeroFaceNotifyDto.getDeviceID() + "/" + dateFormat + "/" + IdWorker.getId() + ".jpg";
                        DtoResult<FileModel> fileModelDtoResult = fileService.uploadToS3(storagePath, ossObjKey);
                        if (fileModelDtoResult.success()) {
                            log.info("1400_face收到消息：图片地址：{} 上传后:{}", storagePath, fileModelDtoResult.getData().getUrl());
                            imgUrl.add(fileModelDtoResult.getData().getUrl());
                            item.setStoragePath(fileModelDtoResult.getData().getUrl());
                        }
                    });
                }
                AlarmNotifyDto dto = new AlarmNotifyDto();
                dto.setId(UUID.randomUUID().toString());
                dto.setSn(channelEntity.getDeviceSn());
                dto.setMsg_id(dto.getId());
                dto.setPriority(1);
                dto.setMethod(5);
                dto.setTimestamp(time.get().getTime());
                if (JuLongType == null) {
                    dto.setType(1400);
                } else {
                    dto.setType(JuLongType);
                }
                dto.setSnap_paths(imgUrl);
                JSONObject ext = JSONObject.parseObject(JSONObject.toJSONString(oneFourZeroImageNotifyDto));
                dto.setExt(ext);
                dto.setOriginalAlarmStr("gb-1400:1400");
                dto.setChannel_id(channelEntity.getChannelId());
                log.info("1400_face告警发送：dto:{}", JSON.toJSONString(dto));
                try {
                    opsDeviceAlarmService.handleMessage(dto);
                } catch (Exception e) {
                    log.error("1400_face告警发送失败：dto:{}", JSON.toJSONString(dto), e);
                }
            }
        }


        OneFourZeroImageNotifyDto.PersonListDto personList = oneFourZeroImageNotifyDto.getPersonList();
        if (personList != null && personList.getFaceObject() != null && !personList.getFaceObject().isEmpty()) {
            for (OneFourZeroPersonNotifyDto oneFourZeroPersonNotifyDto : personList.getFaceObject()) {
                if (StringUtil.isEmpty(oneFourZeroPersonNotifyDto.getDeviceID())) {
                    log.info("1400_person收到消息：但是没有设备id 过滤");
                    return;
                }
                OpsDeviceChannelEntity channelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                        .eq(OpsDeviceChannelEntity::getDeleteFlag, 1)
                        .eq(OpsDeviceChannelEntity::getOneFourCode, oneFourZeroPersonNotifyDto.getDeviceID()), false);
                if (channelEntity == null) {
                    log.info("1400_person收到消息：但是没有设备 过滤 1400编号:{}", oneFourZeroPersonNotifyDto.getDeviceID());
                    return;
                }

                List<String> imgUrl = new ArrayList<>();

                AtomicReference<DateTime> time = new AtomicReference<>(new DateTime());
                String dateFormat = DateUtil.format(new Date(), "yyyyMMdd");
                if (oneFourZeroPersonNotifyDto.getSubImageList() != null && oneFourZeroPersonNotifyDto.getSubImageList().getSubImageInfoObject() != null) {
                    oneFourZeroPersonNotifyDto.getSubImageList().getSubImageInfoObject().forEach(item -> {
                        String storagePath = item.getStoragePath();
                        if (StringUtil.isNotEmpty(item.getShotTime())){
                            time.set(DateUtil.parse(item.getShotTime(), "yyyyMMddHHmmss"));
                        }
                        String ossObjKey = "alarm/1400/" + oneFourZeroPersonNotifyDto.getDeviceID() + "/" + dateFormat + "/" + IdWorker.getId() + ".jpg";
                        DtoResult<FileModel> fileModelDtoResult = fileService.uploadToS3(storagePath, ossObjKey);
                        if (fileModelDtoResult.success()) {
                            log.info("1400_person收到消息：图片地址：{} 上传后:{}", storagePath, fileModelDtoResult.getData().getUrl());
                            imgUrl.add(fileModelDtoResult.getData().getUrl());
                            item.setStoragePath(fileModelDtoResult.getData().getUrl());
                        }
                    });
                }
                AlarmNotifyDto dto = new AlarmNotifyDto();
                dto.setId(UUID.randomUUID().toString());
                dto.setSn(channelEntity.getDeviceSn());
                dto.setMsg_id(dto.getId());
                dto.setPriority(1);
                dto.setMethod(5);
                dto.setTimestamp(time.get().getTime());
                if (JuLongType == null) {
                    dto.setType(1401);
                } else {
                    dto.setType(JuLongType);
                }
                dto.setSnap_paths(imgUrl);
                JSONObject ext = JSONObject.parseObject(JSONObject.toJSONString(oneFourZeroImageNotifyDto));
                dto.setExt(ext);
                dto.setOriginalAlarmStr("gb-1400:1401");
                dto.setChannel_id(channelEntity.getChannelId());
                log.info("1400_person告警发送：dto:{}", JSON.toJSONString(dto));
                try {
                    opsDeviceAlarmService.handleMessage(dto);
                } catch (Exception e) {
                    log.error("1400_person告警发送失败：dto:{}", JSON.toJSONString(dto), e);
                }
            }
        }


        OneFourZeroImageNotifyDto.MotorVehicleListDto motorVehiclesList = oneFourZeroImageNotifyDto.getMotorVehiclesList();
        if (motorVehiclesList != null && motorVehiclesList.getMotorVehicleObject() != null && !motorVehiclesList.getMotorVehicleObject().isEmpty()) {
            for (MotorVehicleObjectDto motorVehicleObjectDto : motorVehiclesList.getMotorVehicleObject()) {
                if (StringUtil.isEmpty(motorVehicleObjectDto.getDeviceID())) {
                    log.info("1400_motorVehicle收到消息：但是没有设备id 过滤");
                    return;
                }
                OpsDeviceChannelEntity channelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                        .eq(OpsDeviceChannelEntity::getDeleteFlag, 1)
                        .eq(OpsDeviceChannelEntity::getOneFourCode, motorVehicleObjectDto.getDeviceID()), false);
                if (channelEntity == null) {
                    log.info("1400_motorVehicle收到消息：但是没有设备 过滤 1400编号:{}", motorVehicleObjectDto.getDeviceID());
                    return;
                }

                List<String> imgUrl = new ArrayList<>();

                AtomicReference<DateTime> time = new AtomicReference<>(new DateTime());
                String dateFormat = DateUtil.format(new Date(), "yyyyMMdd");
                if (motorVehicleObjectDto.getSubImageList() != null && motorVehicleObjectDto.getSubImageList().getSubImageInfoObject() != null) {
                    motorVehicleObjectDto.getSubImageList().getSubImageInfoObject().forEach(item -> {
                        String storagePath = item.getStoragePath();
                        if (StringUtil.isNotEmpty(item.getShotTime())){
                            time.set(DateUtil.parse(item.getShotTime(), "yyyyMMddHHmmss"));
                        }
                        String ossObjKey = "alarm/1400/" + motorVehicleObjectDto.getDeviceID() + "/" + dateFormat + "/" + IdWorker.getId() + ".jpg";
                        DtoResult<FileModel> fileModelDtoResult = fileService.uploadToS3(storagePath, ossObjKey);
                        if (fileModelDtoResult.success()) {
                            log.info("1400_motorVehicle收到消息：图片地址：{} 上传后:{}", storagePath, fileModelDtoResult.getData().getUrl());
                            imgUrl.add(fileModelDtoResult.getData().getUrl());
                            item.setStoragePath(fileModelDtoResult.getData().getUrl());
                        }
                    });
                }
                AlarmNotifyDto dto = new AlarmNotifyDto();
                dto.setId(UUID.randomUUID().toString());
                dto.setSn(channelEntity.getDeviceSn());
                dto.setMsg_id(dto.getId());
                dto.setPriority(1);
                dto.setMethod(5);
                dto.setTimestamp(time.get().getTime());
                if (JuLongType == null) {
                    dto.setType(1402);
                } else {
                    dto.setType(JuLongType);
                }
                dto.setSnap_paths(imgUrl);
                JSONObject ext = JSONObject.parseObject(JSONObject.toJSONString(oneFourZeroImageNotifyDto));
                dto.setExt(ext);
                dto.setOriginalAlarmStr("gb-1400:1402");
                dto.setChannel_id(channelEntity.getChannelId());
                AlarmNormalizationExt normalizationExt = new AlarmNormalizationExt();
                normalizationExt.addCarInfoDto(AlarmNormalizationExt
                        .CarInfoDto
                        .builder()
                        .plateNumber(motorVehicleObjectDto.getPlateNo())
                        .build());
                dto.setNormalizationExt(normalizationExt);
                log.info("1400_motorVehicle告警发送：dto:{}", JSON.toJSONString(dto));
                try {
                    opsDeviceAlarmService.handleMessage(dto);
                } catch (Exception e) {
                    log.error("1400_motorVehicle告警发送失败：dto:{}", JSON.toJSONString(dto), e);
                }
            }
        }


        OneFourZeroImageNotifyDto.NonMotorVehicleListDto nonMotorVehicleList = oneFourZeroImageNotifyDto.getNonMotorVehicleList();
        if (nonMotorVehicleList != null && nonMotorVehicleList.getNonMotorVehicleObject() != null && !nonMotorVehicleList.getNonMotorVehicleObject().isEmpty()) {
            for (NonMotorVehicleObjectDto nonMotorVehicleObjectDto : nonMotorVehicleList.getNonMotorVehicleObject()) {
                if (StringUtil.isEmpty(nonMotorVehicleObjectDto.getDeviceID())) {
                    log.info("1400_nonMotorVehicle收到消息：但是没有设备id 过滤");
                    return;
                }
                OpsDeviceChannelEntity channelEntity = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                        .eq(OpsDeviceChannelEntity::getDeleteFlag, 1)
                        .eq(OpsDeviceChannelEntity::getOneFourCode, nonMotorVehicleObjectDto.getDeviceID()), false);
                if (channelEntity == null) {
                    log.info("1400_nonMotorVehicle收到消息：但是没有设备 过滤 1400编号:{}", nonMotorVehicleObjectDto.getDeviceID());
                    return;
                }

                List<String> imgUrl = new ArrayList<>();

                AtomicReference<DateTime> time = new AtomicReference<>(new DateTime());
                String dateFormat = DateUtil.format(new Date(), "yyyyMMdd");
                if (nonMotorVehicleObjectDto.getSubImageList() != null && nonMotorVehicleObjectDto.getSubImageList().getSubImageInfoObject() != null) {
                    nonMotorVehicleObjectDto.getSubImageList().getSubImageInfoObject().forEach(item -> {
                        String storagePath = item.getStoragePath();
                        if (StringUtil.isNotEmpty(item.getShotTime())){
                            time.set(DateUtil.parse(item.getShotTime(), "yyyyMMddHHmmss"));
                        }
                        String ossObjKey = "alarm/1400/" + nonMotorVehicleObjectDto.getDeviceID() + "/" + dateFormat + "/" + IdWorker.getId() + ".jpg";
                        DtoResult<FileModel> fileModelDtoResult = fileService.uploadToS3(storagePath, ossObjKey);
                        if (fileModelDtoResult.success()) {
                            log.info("1400_nonMotorVehicle收到消息：图片地址：{} 上传后:{}", storagePath, fileModelDtoResult.getData().getUrl());
                            imgUrl.add(fileModelDtoResult.getData().getUrl());
                            item.setStoragePath(fileModelDtoResult.getData().getUrl());
                        }
                    });
                }
                AlarmNotifyDto dto = new AlarmNotifyDto();
                dto.setId(UUID.randomUUID().toString());
                dto.setSn(channelEntity.getDeviceSn());
                dto.setMsg_id(dto.getId());
                dto.setPriority(1);
                dto.setMethod(5);
                dto.setTimestamp(time.get().getTime());
                if (JuLongType == null) {
                    dto.setType(1403);
                } else {
                    dto.setType(JuLongType);
                }
                dto.setSnap_paths(imgUrl);
                JSONObject ext = JSONObject.parseObject(JSONObject.toJSONString(oneFourZeroImageNotifyDto));
                dto.setExt(ext);
                dto.setOriginalAlarmStr("gb-1400:1403");
                AlarmNormalizationExt normalizationExt = new AlarmNormalizationExt();
                normalizationExt.addCarInfoDto(AlarmNormalizationExt
                        .CarInfoDto
                        .builder()
                        .plateNumber(nonMotorVehicleObjectDto.getPlateNo())
                        .build());
                dto.setNormalizationExt(normalizationExt);
                dto.setChannel_id(channelEntity.getChannelId());
                log.info("1400_nonMotorVehicle告警发送：dto:{}", JSON.toJSONString(dto));
                try {
                    opsDeviceAlarmService.handleMessage(dto);
                } catch (Exception e) {
                    log.error("1400_nonMotorVehicle告警发送失败：dto:{}", JSON.toJSONString(dto), e);
                }
            }
        }


    }
}

