package com.saida.services.system.rocketMq.nodev1;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.XML;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.dto.AlarmNormalizationExt;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.common.service.FileService;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.OpsDeviceChannelEntity;
import com.saida.services.converge.enums.EhomeAlarmEnums;
import com.saida.services.entities.pojo.FileModel;
import com.saida.services.system.ops.service.OpsDeviceAlarmService;
import com.saida.services.system.ops.service.OpsDeviceChannelService;
import com.saida.services.system.sys.dto.AlarmNotifyDto;
import com.saida.services.system.sys.dto.EhomeAlarmDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component

public class EhomeAlarmMessageListener implements VLinkerMqMessageListener {

    @Resource
    private OpsDeviceAlarmService opsDeviceAlarmService;

    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;

    @Resource
    private FileService fileService;

    private static final Pattern pattern = Pattern.compile("http://\\S+?/pic");

    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("ehome_default_alarm")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        EhomeAlarmDto message = JSON.parseObject(messageBody, EhomeAlarmDto.class);
        handleMessage(message);
    }

    /**
     * 处理告警通知消息。
     * 当收到告警通知消息时，根据消息内容查询设备信息，并根据设备信息和告警通知内容创建或更新告警实体，
     * 对于特定类型的告警，还会通知算法平台。
     *
     * @param message 告警通知消息，包含告警的详细信息。
     */
    protected void handleMessage(EhomeAlarmDto message) {
        if (StringUtil.isEmpty(message.getPic_server_base()) || StringUtil.isEmpty(message.getMessage())) {
            return;
        }
        Matcher matcher = pattern.matcher(message.getMessage());
        String ehomeMsg = matcher.replaceAll("http://" + message.getPic_server_base() + "/pic");

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject = XML.toJSONObject(ehomeMsg);
        } catch (Exception e) {
            log.info("[mq:ehome_default_alarm]：xml解析失败");
        }
        if (jsonObject.isEmpty()) {
            try {
                jsonObject = JSONUtil.parseObj(ehomeMsg);
            } catch (Exception ex) {
                log.error("两次解析都失败了 丢弃：", ex);
                return;
            }
        }
        if (jsonObject.isEmpty()) {
            log.error("[mq:ehome_default_alarm]解析失败 丢弃：");
            return;
        }
        if (jsonObject.containsKey("EventNotificationAlert")) {
            jsonObject = jsonObject.getJSONObject("EventNotificationAlert");
        }

        String eventType = jsonObject.getStr("eventType");
        //人闸
        if (jsonObject.containsKey("PPVSPMessage")) {
            eventType = "PPVSPMessage";
        }
        EhomeAlarmEnums[] beanByEventType = EhomeAlarmEnums.getListByEventType(eventType);
        if (beanByEventType.length == 0) {
            return;
        }
        log.info("[mq:ehome_default_alarm]收到消息 eventType:{} 转成json：{}", eventType, jsonObject);

        JSONObject finalJsonObject = jsonObject;
        String finalEventType = eventType;
        Arrays.stream(beanByEventType).forEach(e -> {
            if (e.getType() == null) {
                return;
            }
            AlarmNormalizationExt alarmNormalizationExt = new AlarmNormalizationExt();
            int typeByEventType = e.getType();
            List<String> snap_paths = new ArrayList<>();
            // 图像识别抓拍 其实是人脸抓拍
            if (typeByEventType == 25) {
                JSONArray faceCapture = finalJsonObject.getJSONArray("faceCapture");
                for (int i = 0; i < faceCapture.size(); i++) {
                    JSONObject targetAttrs = faceCapture.getJSONObject(i).getJSONObject("targetAttrs");
                    if (targetAttrs != null && targetAttrs.containsKey("bkgUrl")) {
                        snap_paths.add(targetAttrs.getStr("bkgUrl"));
                    }

                    JSONArray faces = faceCapture.getJSONObject(i).getJSONArray("faces");
                    for (int j = 0; j < faces.size(); j++) {
                        snap_paths.add(faces.getJSONObject(j).getStr("URL"));
                    }
                }
            } else if (typeByEventType == 56) {
                JSONObject ppvspMessage = finalJsonObject.getJSONObject("PPVSPMessage");
                JSONObject params = ppvspMessage.getJSONObject("Params");
                String picDataUrl = params.getStr("PicDataUrl");
                if (StringUtil.isBlank(picDataUrl)) {
                    picDataUrl = "";
                }
                alarmNormalizationExt.addPeopleInfoDto(AlarmNormalizationExt
                        .PeopleInfoDto.builder()
                        .peopleNumber(params.getStr("employeeNoString"))
                        .build());
                snap_paths.add("http://" + message.getPic_server_base() + picDataUrl);
                finalJsonObject.set("deviceID", params.get("DeviceID"));
                finalJsonObject.set("dateTime", params.get("Time"));
            } else if (typeByEventType == 19) {
                //快速移动侦测
                snap_paths.add(finalJsonObject.getStr("bkgUrl"));
            } else if (typeByEventType == 18) {
                //人员聚集侦测
                snap_paths.add(finalJsonObject.getStr("bkgUrl"));
            } else if (typeByEventType == 13) {
                //区域入侵
                snap_paths.add(finalJsonObject.getStr("bkgUrl"));
            } else if (typeByEventType == 30) {
                //车牌
                JSONObject anpr = finalJsonObject.getJSONObject("ANPR");
                alarmNormalizationExt.addCarInfoDto(AlarmNormalizationExt
                        .CarInfoDto.builder()
                        .plateNumber(anpr.getStr("licensePlate"))
                        .build());
                JSONObject pictureInfoList = anpr.getJSONObject("pictureInfoList");
                JSONArray pictureInfo = pictureInfoList.getJSONArray("pictureInfo");
                for (int i = 0; i < pictureInfo.size(); i++) {
                    JSONObject pictureInfo1 = pictureInfo.getJSONObject(i);
                    snap_paths.add(pictureInfo1.getStr("pictureURL"));
                }
            }

            if ("cityManagement".equals(finalEventType)) {
                // 针对 subEventType 分组处理
                JSONArray result = finalJsonObject.getJSONArray("Result");
                for (int i = 0; i < result.size(); i++) {
                    snap_paths.add(result.getJSONObject(i).getStr("backgroundImageURL"));
                }
                Map<String, JSONArray> groupedSubEvents = groupBySubEventType(result);
                groupedSubEvents.forEach((subEventType, subEventData) -> {
                    if (subEventType.equals(e.getSubEventType())) {
                        // 创建告警 DTO
                        finalJsonObject.set("Result", subEventData);
                        AlarmNotifyDto alarmNotifyDto = createAlarmNotifyDto(finalJsonObject, e.getType(), snap_paths);
                        alarmNotifyDto.setNormalizationExt(alarmNormalizationExt);
                        opsDeviceAlarmService.handleMessage(alarmNotifyDto);
                    }
                });
            } else {
                AlarmNotifyDto alarmNotifyDto = createAlarmNotifyDto(finalJsonObject, e.getType(), snap_paths);
                alarmNotifyDto.setNormalizationExt(alarmNormalizationExt);
                opsDeviceAlarmService.handleMessage(alarmNotifyDto);
            }
        });

    }


    public static Map<String, JSONArray> groupBySubEventType(JSONArray subEvents) {
        Map<String, JSONArray> grouped = new HashMap<>();
        for (int i = 0; i < subEvents.size(); i++) {
            JSONObject subEvent = subEvents.getJSONObject(i);
            String subEventType = subEvent.getStr("subEventType");
            grouped.computeIfAbsent(subEventType, k -> new JSONArray()).add(subEvent);
        }
        return grouped;
    }

    private AlarmNotifyDto createAlarmNotifyDto(JSONObject finalJsonObject, int eventType, List<String> snapPaths) {
        AlarmNotifyDto alarmNotifyDto = new AlarmNotifyDto();
        alarmNotifyDto.setId(UUID.randomUUID().toString());
        alarmNotifyDto.setMsg_id(UUID.randomUUID().toString());
        alarmNotifyDto.setSn(finalJsonObject.getStr("deviceID"));

        OpsDeviceChannelEntity one = opsDeviceChannelService.getOne(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                .eq(OpsDeviceChannelEntity::getDeviceSn, finalJsonObject.getStr("deviceID"))
                .eq(OpsDeviceChannelEntity::getBid, finalJsonObject.getStr("channelID")), false);
        if (one != null) {
            alarmNotifyDto.setChannel_id(one.getChannelId());
        }
        alarmNotifyDto.setPriority(1);
        alarmNotifyDto.setMethod(5);
        alarmNotifyDto.setTimestamp(System.currentTimeMillis());
        String dateTime = finalJsonObject.getStr("dateTime");
        if (StringUtil.isNotEmpty(dateTime)) {
            DateTime parse = DateUtil.parse(dateTime);
            alarmNotifyDto.setTimestamp(parse.getTime());
        }
        alarmNotifyDto.setType(eventType);
        alarmNotifyDto.setExt(finalJsonObject);

        List<String> imgs = new ArrayList<>();
        snapPaths.forEach(img -> {
            InputStream inputStream = null;
            try {
                if (StringUtil.isEmpty(img)) {
                    return;
                }
                URL url = new URL(img);
                URLConnection conn = url.openConnection();
                long fileSize = conn.getContentLengthLong();
                String dateFormat = DateUtil.format(new Date(), "yyyyMMdd");
                String ossObjKey = alarmNotifyDto.getSn() + "/" + dateFormat + "/" + IdWorker.getId() + ".jpg";
                inputStream = conn.getInputStream();
                DtoResult<FileModel> fileModelDtoResult = fileService.uploadToS3(inputStream, ossObjKey, fileSize, "alarm");
                if (fileModelDtoResult.success()) {
                    imgs.add(fileModelDtoResult.getData().getUrl());
                }
            } catch (Exception ex) {
                log.error("解析失败 丢弃[大概率下载不到图片]：{}", ex.getMessage());
            } finally {
                if (inputStream != null) {
                    try {
                        inputStream.close();
                    } catch (IOException ex) {
                        log.error("关闭流失败", ex);
                    }
                }
            }
        });
        Collections.reverse(imgs);
        alarmNotifyDto.setSnap_paths(imgs);
        return alarmNotifyDto;
    }


}
