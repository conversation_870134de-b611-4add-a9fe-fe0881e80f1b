package com.saida.services.system.rocketMq.nodev1;

import com.alibaba.fastjson.JSON;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.system.ops.service.OpsDeviceAlarmService;
import com.saida.services.system.sys.dto.AlarmNotifyDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class AlarmNotifyMessageListenerVLinker implements VLinkerMqMessageListener {
    @Resource
    private OpsDeviceAlarmService opsDeviceAlarmService;

    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("alarm_notify")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        AlarmNotifyDto alarmNotifyDto = JSON.parseObject(messageBody, AlarmNotifyDto.class);
        opsDeviceAlarmService.handleMessage(alarmNotifyDto);
    }

}

