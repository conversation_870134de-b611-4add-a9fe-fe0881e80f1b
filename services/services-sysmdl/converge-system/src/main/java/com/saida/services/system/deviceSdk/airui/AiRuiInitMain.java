package com.saida.services.system.deviceSdk.airui;

import brave.Span;
import brave.Tracer;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.dto.AlarmNormalizationExt;
import com.saida.services.common.service.FileService;
import com.saida.services.converge.entity.DeviceEntity;
import com.saida.services.converge.entity.OpsDeviceChannelEntity;
import com.saida.services.converge.enums.SdkAccessType;
import com.saida.services.converge.enums.VideoAlarmMethodTypeEnums;
import com.saida.services.entities.pojo.FileModel;
import com.saida.services.system.deviceSdk.airui.dto.AiRuiMsgDto;
import com.saida.services.system.deviceSdk.airui.dto.AiRuiSdkDto;
import com.saida.services.system.deviceSdk.airui.req.AiRuiAlarmSubscribeReq;
import com.saida.services.system.deviceSdk.airui.req.AiRuiLoginReq;
import com.saida.services.system.deviceSdk.airui.req.AiRuiPtzFunReq;
import com.saida.services.system.deviceSdk.airui.req.AiRuiPtzQueryPresetSizeReq;
import com.saida.services.system.deviceSdk.airui.resp.AiRuiAlarmResp;
import com.saida.services.system.deviceSdk.airui.resp.AiRuiBaseResp;
import com.saida.services.system.deviceSdk.airui.resp.AiRuiGetPtzResp;
import com.saida.services.system.deviceSdk.airui.resp.AiRuiLoginResp;
import com.saida.services.system.deviceSdk.dto.PtzDto;
import com.saida.services.system.ops.service.DeviceService;
import com.saida.services.system.ops.service.OpsDeviceAlarmService;
import com.saida.services.system.ops.service.OpsDeviceChannelService;
import com.saida.services.system.sys.dto.AlarmNotifyDto;
import com.saida.services.system.video.param.PtzCmdParamVideoNode;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.io.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 艾睿sdk主入口
 * <p>
 * {"uuid":"659b4022-951b-49c2-a7bb-8b6650ad2c0b","command":"device_add","value":{"id":"","ip":"**************","port":80,"username":"admin","password":"system123"}}
 * {"uuid":"659b4022-951b-49c2-a7bb-8b6650ad2c0b","command":"device_info","value":{"id":"139621266640960"}}
 * {"command":"alarm_subscribe","value":{"id":"139621266640960"}}
 * {"command":"alarm_unsubscribe","value":{"id":"139621266640960"}}
 * {"command":"ptz_control","value":{"id":"139621266640960","channel":0,"cmd":2,"speed":5,"stop":0}}
 * {"command":"ptz_control","value":{"id":"139621266640960","channel":0,"cmd":2,"speed":5,"stop":1}}
 * {"command":"ptz_query_preset_size","value":{"id":"139621266640960"}}
 */
@Slf4j
@Component
public class AiRuiInitMain {

    @Value("${sdk.airui.enable:false}")
    private Boolean airuiEnable;

    @Value("${sdk.airui.lib-path:./libs}")
    private String libPath;

    @Value("${sdk.airui.sdk-path:./airui_sdk}")
    private String sdkPath;
    @Resource
    private ThreadPoolConfig threadPoolConfig;
    @Lazy
    @Resource
    private OpsDeviceAlarmService opsDeviceAlarmService;
    @Lazy
    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;
    @Lazy
    @Resource
    private DeviceService deviceService;
    @Resource
    private FileService fileService;
    @Resource
    private Tracer tracer;


    /**
     * 30s 清理一次超过30s没人消费的消息
     */
    @Scheduled(cron = "0/30 * * * * ? ")
    public void clearMsg() {
        if (msgMap.isEmpty()) {
            return;
        }
        StringBuilder stringBuilder = new StringBuilder();
        msgMap.values().forEach(aiRuiMsgDto -> {
            //发送人的时间是空的  告警消息？
            if (aiRuiMsgDto.getAcceptTimestamp() != null && System.currentTimeMillis() - aiRuiMsgDto.getAcceptTimestamp() > 30 * 1000) {
                stringBuilder.append("[").append(aiRuiMsgDto.getSendMsgJson()).append("]");
                msgMap.remove(aiRuiMsgDto.getUuid());
            }
        });
        if (stringBuilder.length() > 0) {
            log.error("清理超过30s未处理的消息：{}", stringBuilder);
        }
    }

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        threadPoolConfig.taskRunner(this::initSdk);
    }

    /**
     * sdk 沟通用的对象
     */
    private static Process process;
    private static BufferedWriter processInput;
    private static BufferedReader processOutput;
    private final ObjectMapper objectMapper = new ObjectMapper();
    /**
     * key： deviceId
     * value ：「deviceId+句柄id」
     */
    @Getter
    public static final ConcurrentHashMap<String, AiRuiSdkDto> handleMap = new ConcurrentHashMap<>();
    /**
     * key： 消息标识
     * value： 消息对象
     */
    private static final ConcurrentHashMap<String, AiRuiMsgDto> msgMap = new ConcurrentHashMap<>();

    private void initSdk() {
        if (!airuiEnable) {
            return;
        }
        try {
            // LD_LIBRARY_PATH=./libs ./rust_ircnetsdk
            ProcessBuilder processBuilder = new ProcessBuilder(sdkPath);
            processBuilder.environment().put("LD_LIBRARY_PATH", libPath);
//            ProcessBuilder processBuilder = new ProcessBuilder("sh", "-c", "LD_LIBRARY_PATH=" + libPath + " " + sdkPath);
//            processBuilder.redirectErrorStream(true);  // 合并错误输出和标准输出
            process = processBuilder.start();
            process.onExit().thenRun(() -> {
                log.info("艾睿sdk Rust 退出 exitValue:{}", process.exitValue());
                taskExecutor.execute(() -> {
                    log.info("等待2s");
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException ignored) {

                    }

                    initSdk();
                });
            });
            // 初始化输入/输出流
            processInput = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
            processOutput = new BufferedReader(new InputStreamReader(process.getInputStream()));
            log.info("艾睿sdk Rust 程序已启动");
            // 创建一个线程持续读取 Rust 程序输出
            Thread outputReaderThread = new Thread(this::readOutput);
            outputReaderThread.setName("airui-sdk");
            outputReaderThread.setDaemon(true); // 设置为守护线程，随主程序退出
            outputReaderThread.start();
            //艾睿的设备
            List<DeviceEntity> list = deviceService.list(new LambdaQueryWrapper<DeviceEntity>()
                    .eq(DeviceEntity::getSdkAccess, SdkAccessType.AIRUI.getCode())
                    .isNotNull(DeviceEntity::getDeviceCode)
                    .isNotNull(DeviceEntity::getIp)
                    .isNotNull(DeviceEntity::getPort)
                    .isNotNull(DeviceEntity::getUsername)
                    .isNotNull(DeviceEntity::getPassword));
            list.forEach(this::loginToDevice);
        } catch (IOException e) {
            log.error("艾睿sdk 启动 Rust 程序时出错", e);
        }
    }

    public synchronized boolean request(AiRuiMsgDto aiRuiMsgDto) {
        if (processInput == null) {
            throw new RuntimeException("艾睿sdk 程序未启动");
        }
        msgMap.put(aiRuiMsgDto.getUuid(), aiRuiMsgDto);
        try {
            processInput.write(aiRuiMsgDto.getSendMsgJson());
            processInput.newLine();  // 确保 Rust 程序接收到指令
            processInput.flush();
            log.info("艾睿sdk 已发送指令：{}", aiRuiMsgDto.getSendMsgJson());
            return true;
        } catch (IOException e) {
            log.error("艾睿sdk 向 Rust 程序发送指令时出错：{}", e.getMessage());
            msgMap.remove(aiRuiMsgDto.getUuid());
            return false;
        }
    }

    @Lazy
    @Resource(name = ThreadPoolConfig.TASK_EXECUTOR_BEAN_NAME)
    private Executor taskExecutor;

    // 持续读取 Rust 程序的输出
    public void readOutput() {
        String line;
        try {
            while ((line = processOutput.readLine()) != null) {
                Span newSpan = tracer.nextSpan().name("AiRuiInitMain").start();
                try (Tracer.SpanInScope ws = tracer.withSpanInScope(newSpan)) {
                    JSONObject jsonObject = JSONObject.parseObject(line);
                    if (!jsonObject.containsKey("uuid")) {
                        continue;
                    }
                    String command = jsonObject.getString("command");
                    if (command.equals("alarm")) {// 报警消息
                        String finalLine = line;
                        taskExecutor.execute(() -> {
                            try {
                                doAlarm(finalLine);
                            } catch (Exception e) {
                                log.error("处理报警消息时出错：{}", e.getMessage(), e);
                            }
                        });
                        continue;
                    } else {
                        log.error("未处理的指令：{}", command);
                    }
                    log.info("艾睿sdk Rust 输出: {}", line);
                    AiRuiMsgDto dto = msgMap.get(jsonObject.getString("uuid"));
                    if (dto == null) {
                        continue;
                    }
                    dto.setAcceptMsgJson(line);
                    dto.setAcceptTimestamp(System.currentTimeMillis());
                    try {
                        dto.getReentrantLock().lock();
                        dto.getCondition().signal();
                        msgMap.remove(dto.getUuid());
                    } catch (Exception e) {
                        log.error("艾睿sdk 处理消息时出错：{}", e.getMessage(), e);
                    } finally {
                        dto.getReentrantLock().unlock();
                    }
                } catch (Exception e) {
                    log.error("艾睿sdk 解析 JSON 失败：{}", e.getMessage(), e);
                    newSpan.error(e);  // 记录异常
                } finally {
                    newSpan.finish();  // 结束 span
                }
            }
        } catch (IOException e) {
            log.error("艾睿sdk 读取 Rust 输出时出错：{}", e.getMessage());
        } finally {
            log.info("艾睿sdk Rust 输出流已关闭");
            process.destroy();
        }
    }


    /**
     * #[doc = "< 温度"]
     * pub const IRC_NET_ALARM_TYPE_IRC_NET_ALARM_TEMP: IRC_NET_ALARM_TYPE = 10001;
     * #[doc = "< 火点"]
     * pub const IRC_NET_ALARM_TYPE_IRC_NET_ALARM_FIRE: IRC_NET_ALARM_TYPE = 10002;
     * #[doc = "< 温升"]
     * pub const IRC_NET_ALARM_TYPE_IRC_NET_ALARM_TEMP_RISE: IRC_NET_ALARM_TYPE = 10003;
     * #[doc = "< 温差"]
     * pub const IRC_NET_ALARM_TYPE_IRC_NET_ALARM_TEMP_DIFF: IRC_NET_ALARM_TYPE = 10004;
     * #[doc = "< 火点脉冲"]
     * pub const IRC_NET_ALARM_TYPE_IRC_NET_ALARM_FIRE_PULSE: IRC_NET_ALARM_TYPE = 10005;
     * #[doc = "< 区域入侵"]
     * pub const IRC_NET_ALARM_TYPE_IRC_NET_ALARM_REGION_INTRUSION: IRC_NET_ALARM_TYPE = 20001;
     * #[doc = "< 绊线入侵"]
     * pub const IRC_NET_ALARM_TYPE_IRC_NET_ALARM_LINE_INTRUSION: IRC_NET_ALARM_TYPE = 20002;
     * #[doc = "<烟雾"]
     * pub const IRC_NET_ALARM_TYPE_IRC_NET_ALARM_SMOKE_DETECT: IRC_NET_ALARM_TYPE = 30001;
     * #[doc = "< 本地"]
     * pub const IRC_NET_ALARM_TYPE_IRC_NET_ALARM_LOCAL: IRC_NET_ALARM_TYPE = 40001;
     */
    private static final Map<Integer, Integer> AlarmTypeMap = new HashMap<Integer, Integer>() {{
        put(20001, VideoAlarmMethodTypeEnums.A5.getType());
        put(20002, VideoAlarmMethodTypeEnums.A6.getType());
        put(10002, VideoAlarmMethodTypeEnums.A20.getType());
        put(30001, VideoAlarmMethodTypeEnums.A20.getType());
    }};

    public void doAlarm(String json) {
        try {
            // 登录成功 记录句柄
            TypeReference<AiRuiBaseResp<AiRuiAlarmResp>> typeReference = new TypeReference<>() {
            };
            // 响应体转换成实体类
            AiRuiBaseResp<AiRuiAlarmResp> baseResp = objectMapper.readValue(json, typeReference);
            if (baseResp.getData() != null) {
                // 原始告警类型
                Integer originalAlarmType = baseResp.getData().getAlarmType();
                Integer alarmType = originalAlarmType;
                if (AlarmTypeMap.containsKey(alarmType)) {
                    alarmType = AlarmTypeMap.get(alarmType);
                } else {
                    log.error("未知的报警类型：{}", alarmType);
                    return;
                }
                AtomicReference<Long> deviceId = new AtomicReference<>();
                handleMap.forEach((key, value) -> {
                    if (value.getHandleId().equals(baseResp.getData().getId())) {
                        deviceId.set(value.getDeviceId());
                    }
                });
                if (deviceId.get() == null) {
                    log.error("设备是空的？ deviceId :{}", deviceId);
                    return;
                }
                DeviceEntity deviceEntity = deviceService.getById(deviceId.get());
                if (deviceEntity == null) {
                    log.error("设备查询库不存在 deviceId :{}", deviceId.get());
                    return;
                }
                List<OpsDeviceChannelEntity> list = opsDeviceChannelService.list(new LambdaQueryWrapper<OpsDeviceChannelEntity>()
                        .eq(OpsDeviceChannelEntity::getDeviceId, deviceId.get())
                        .eq(OpsDeviceChannelEntity::getDeleteFlag, 1)
                        .orderByDesc(OpsDeviceChannelEntity::getId));
                AlarmNotifyDto dto = new AlarmNotifyDto();
                dto.setId(UUID.randomUUID().toString());
                dto.setSn(deviceEntity.getDeviceCode());
                dto.setMsg_id(dto.getId());
                dto.setPriority(1);
                dto.setMethod(5);
                dto.setTimestamp((baseResp.getData().getTimestamp() - (8 * 3600)) * 1000);
                dto.setType(alarmType);
                dto.setSdkAccess(true);
                PtzDto ptzDto = doGetPTZDto(deviceId.get());
                if (ptzDto != null) {
                    dto.setNormalizationExt(AlarmNormalizationExt.builder()
                            .ptzDto(AlarmNormalizationExt.PtzDto.builder()
                                    .pan(ptzDto.getNPTZPan())
                                    .tilt(ptzDto.getNPTZTilt())
                                    .zoom(ptzDto.getNPTZZoom())
                                    .build()).build());
                }
                // 告警图片
                ArrayList<String> snapPaths = new ArrayList<>();
                if (baseResp.getData().getPictures() != null) {
                    log.info("艾睿sdk 告警图片数量：{}", baseResp.getData().getPictures().length);
                    for (String picture : baseResp.getData().getPictures()) {
                        DtoResult<FileModel> fileModelDtoResult = fileService.uploadBase64(picture, ".jpg", "alarm/airui");
                        if (fileModelDtoResult.success()) {
                            snapPaths.add(fileModelDtoResult.getData().getUrl());
                        } else {
                            log.error("艾睿sdk 上传图片失败：{}", fileModelDtoResult.getMessage());
                        }
                    }
                    CollectionUtil.reverse(snapPaths);
                } else {
                    log.error("艾睿sdk 告警图片为空：{}", json);
                }
                dto.setSnap_paths(snapPaths);
//                JSONObject ext = JSONObject.parseObject(json);
//                dto.setExt(ext);
                dto.setOriginalAlarmStr("aiRui-sdk:" + originalAlarmType);
                if (!list.isEmpty()) {
                    // 告警的通道序号 从0开始
                    Integer channel = baseResp.getData().getChannel();
                    // 如果序号在list中  按照下标取  如果不在就直接取第一个就行
                    if (channel >= list.size()) {
                        channel = 0;
                    }
                    OpsDeviceChannelEntity channelEntity = list.get(channel);
                    dto.setChannel_id(channelEntity.getChannelId());
                }
                log.info("艾睿sdk 告警发送：dto:{}", JSON.toJSONString(dto));
                try {
                    opsDeviceAlarmService.handleMessage(dto);
                } catch (Exception e) {
                    log.error("艾睿sdk 告警发送失败：dto:{}", JSON.toJSONString(dto), e);
                }
            }
        } catch (Exception e) {
            log.error("艾睿sdk 告警解析 JSON 失败：{}", e.getMessage());
        }
    }


    public AiRuiSdkDto getAiRuiSdkDtoByDeviceId(Long deviceId) {
        return handleMap.get(String.valueOf(deviceId));
    }

    public void loginToDevice(DeviceEntity deviceEntity) {
        try {
            String uuid = UUID.randomUUID().toString();
            AiRuiLoginReq build = AiRuiLoginReq
                    .builder()
                    .uuid(uuid)
                    .command("device_add")
                    .value(AiRuiLoginReq.AiRuiLoginValue
                            .builder()
                            .id(String.valueOf(deviceEntity.getId()))
                            .ip(deviceEntity.getIp())
                            .port(deviceEntity.getPort())
                            .username(deviceEntity.getUsername())
                            .password(deviceEntity.getPassword())
                            // "value":{"id":"","ip":"**************","port":80,"username":"admin","password":"system123"}}
//                            .ip("**************")
//                            .port(80)
//                            .username("admin")
//                            .password("system123")
                            .build())
                    .build();
            ReentrantLock reentrantLock = new ReentrantLock();
            Condition condition = reentrantLock.newCondition();
            AiRuiMsgDto aiRuiMsgDto = AiRuiMsgDto
                    .builder()
                    .uuid(uuid)
                    .command("device_add")
                    .sendMsgJson(JSON.toJSONString(build))
                    .reentrantLock(reentrantLock)
                    .condition(condition)
                    .build();
            boolean request = request(aiRuiMsgDto);
            if (!request) {
                log.error("艾睿sdk 登录失败！");
                return;
            }
            try {
                reentrantLock.lock();
                boolean await = condition.await(10, TimeUnit.SECONDS);
                if (!await) {
                    log.error("艾睿sdk 等待登录超时！");
                    return;
                }
            } catch (Exception e) {
                log.error("艾睿sdk 等待登录超时！");
                return;
            } finally {
                reentrantLock.unlock();
            }

            // 登录成功 记录句柄
            TypeReference<AiRuiBaseResp<AiRuiLoginResp>> typeReference = new TypeReference<AiRuiBaseResp<AiRuiLoginResp>>() {
            };
            // 响应体转换成实体类
            AiRuiBaseResp<AiRuiLoginResp> baseResp = objectMapper.readValue(aiRuiMsgDto.getAcceptMsgJson(), typeReference);
            if (!"ok".equals(baseResp.getStatus())) {
                log.error("艾睿sdk 登录失败！{}", aiRuiMsgDto.getAcceptMsgJson());
                return;
            }
            if (baseResp.getData() != null) {
                AiRuiLoginResp data = baseResp.getData();
                AiRuiSdkDto aiRuiSdkDto = AiRuiSdkDto.builder()
                        .deviceId(deviceEntity.getId())
                        .handleId(data.getId())
                        .build();
                handleMap.put(String.valueOf(deviceEntity.getId()), aiRuiSdkDto);
            }
            log.info("艾睿设备登录 结束！ deviceCode：{}，ip：{}，port：{}，userName：{}，password：{} loginMsg:{},handleMap:{}"
                    , deviceEntity.getDeviceCode(), deviceEntity.getIp(), deviceEntity.getPort()
                    , deviceEntity.getUsername(), deviceEntity.getPassword()
                    , aiRuiMsgDto.getAcceptMsgJson(), handleMap.containsKey(String.valueOf(deviceEntity.getId())));
            alarmSubscribe(deviceEntity.getId());
        } catch (Exception e) {
            log.error("艾睿sdk 登录失败 出现异常！", e);
        }
    }

    public void alarmSubscribe(Long deviceId) {
        AiRuiSdkDto ruiSdkDto = getAiRuiSdkDtoByDeviceId(deviceId);
        if (ruiSdkDto == null) {
            log.error("deviceId:{} 设备没登录没办法订阅", deviceId);
            return;
        }
        try {
            String uuid = UUID.randomUUID().toString();
            AiRuiAlarmSubscribeReq build = AiRuiAlarmSubscribeReq
                    .builder()
                    .uuid(uuid)
                    .command("alarm_subscribe")
                    .value(AiRuiAlarmSubscribeReq.AiRuiValueDto
                            .builder()
                            .id(ruiSdkDto.getHandleId())
                            .build())
                    .build();
            ReentrantLock reentrantLock = new ReentrantLock();
            Condition condition = reentrantLock.newCondition();
            AiRuiMsgDto aiRuiMsgDto = AiRuiMsgDto
                    .builder()
                    .uuid(uuid)
                    .command("alarm_subscribe")
                    .sendMsgJson(JSON.toJSONString(build))
                    .reentrantLock(reentrantLock)
                    .condition(condition)
                    .build();
            boolean request = request(aiRuiMsgDto);
            if (!request) {
                log.error("艾睿sdk 订阅告警失败！");
                return;
            }
            try {
                reentrantLock.lock();
                boolean await = condition.await(10, TimeUnit.SECONDS);
                if (!await) {
                    log.error("艾睿sdk 订阅告警超时！");
                    return;
                }
            } catch (Exception e) {
                log.error("艾睿sdk 订阅超时！");
                return;
            } finally {
                reentrantLock.unlock();
            }
            log.info("艾睿设备订阅！ deviceId：{}，acceptMsg:{}"
                    , deviceId, aiRuiMsgDto.getAcceptMsgJson());
        } catch (Exception e) {
            log.error("艾睿sdk 设备订阅失败！ 出现异常", e);
        }
    }

    public void stopToDevice(DeviceEntity deviceEntity) {
        AiRuiSdkDto aiRuiSdkDto = getAiRuiSdkDtoByDeviceId(deviceEntity.getId());
        if (aiRuiSdkDto == null) {
            return;
        }
        String uuid = UUID.randomUUID().toString();
        AiRuiLoginReq build = AiRuiLoginReq
                .builder()
                .command("device_remove")
                .uuid(uuid)
                .value(AiRuiLoginReq.AiRuiLoginValue
                        .builder()
                        .id(aiRuiSdkDto.getHandleId())
                        .build())
                .build();
        ReentrantLock reentrantLock = new ReentrantLock();
        Condition condition = reentrantLock.newCondition();
        AiRuiMsgDto aiRuiMsgDto = AiRuiMsgDto
                .builder()
                .uuid(uuid)
                .command("device_remove")
                .sendMsgJson(JSON.toJSONString(build))
                .reentrantLock(reentrantLock)
                .condition(condition)
                .build();
        boolean request = request(aiRuiMsgDto);
        if (!request) {
            log.error("艾睿sdk 退出登录失败！");
            return;
        }
        try {
            reentrantLock.lock();
            boolean await = condition.await(10, TimeUnit.SECONDS);
            if (!await) {
                log.error("艾睿sdk 退出登录超时！");
                return;
            }
        } catch (Exception e) {
            log.error("艾睿sdk 退出登录超时！", e);
        } finally {
            reentrantLock.unlock();
        }
    }


    public PtzDto doGetPTZDto(Long deviceId) {
        AiRuiSdkDto aiRuiSdkDto = getAiRuiSdkDtoByDeviceId(deviceId);
        if (aiRuiSdkDto == null) {
            throw new RuntimeException("艾睿sdk doGetPTZDto失败");
        }
        String uuid = UUID.randomUUID().toString();
        AiRuiPtzQueryPresetSizeReq build = AiRuiPtzQueryPresetSizeReq
                .builder()
                .uuid(uuid)
                .command("ptz_current")
                .value(AiRuiPtzQueryPresetSizeReq.AiRuiLoginValue
                        .builder()
                        .id(aiRuiSdkDto.getHandleId())
                        .build())
                .build();
        ReentrantLock reentrantLock = new ReentrantLock();
        Condition condition = reentrantLock.newCondition();
        AiRuiMsgDto aiRuiMsgDto = AiRuiMsgDto
                .builder()
                .uuid(uuid)
                .command("ptz_current")
                .sendMsgJson(JSON.toJSONString(build))
                .reentrantLock(reentrantLock)
                .condition(condition)
                .build();
        boolean request = request(aiRuiMsgDto);
        if (!request) {
            log.error("艾睿sdk 获取ptz失败！");
            return null;
        }
        try {
            reentrantLock.lock();
            boolean await = condition.await(10, TimeUnit.SECONDS);
            if (!await) {
                log.error("艾睿sdk 获取ptz超时！");
                return null;
            }
        } catch (Exception e) {
            log.error("艾睿sdk 获取ptz超时！");
            return null;
        } finally {
            reentrantLock.unlock();
        }
        log.info("艾睿sdk 读取ptz成功！ res:{}", aiRuiMsgDto.getAcceptMsgJson());
        // 登录成功 记录句柄
        TypeReference<AiRuiBaseResp<AiRuiGetPtzResp>> typeReference = new TypeReference<AiRuiBaseResp<AiRuiGetPtzResp>>() {
        };
        // 响应体转换成实体类
        AiRuiBaseResp<AiRuiGetPtzResp> baseResp = null;
        try {
            baseResp = objectMapper.readValue(aiRuiMsgDto.getAcceptMsgJson(), typeReference);
        } catch (JsonProcessingException e) {
            log.error("艾睿sdk 读取ptz失败（json解析失败）！{}", aiRuiMsgDto.getAcceptMsgJson());
            return null;
        }
        if (!"ok".equals(baseResp.getStatus())) {
            log.error("艾睿sdk 读取ptz失败！{}", aiRuiMsgDto.getAcceptMsgJson());
            return null;
        }
        if (baseResp.getData() != null) {
            PtzDto ptzDto = new PtzDto();
            ptzDto.setNPTZPan(baseResp.getData().getP());
            ptzDto.setNPTZTilt(baseResp.getData().getT());
            ptzDto.setNPTZZoom(baseResp.getData().getZ());
            return ptzDto;
        }
        return null;
    }


    public boolean doSetPTZDto(Long deviceId, Integer nChannelID, PtzDto ptzDto) {
        AiRuiSdkDto aiRuiSdkDto = getAiRuiSdkDtoByDeviceId(deviceId);
        if (aiRuiSdkDto == null) {
            throw new RuntimeException("艾睿sdk doSetPTZDto失败");
        }
        String uuid = UUID.randomUUID().toString();
        AiRuiPtzQueryPresetSizeReq build = AiRuiPtzQueryPresetSizeReq
                .builder()
                .command("ptz_position")
                .uuid(uuid)
                .value(AiRuiPtzQueryPresetSizeReq.AiRuiLoginValue
                        .builder()
                        .id(aiRuiSdkDto.getHandleId())
                        .pan(ptzDto.getNPTZPan())
                        .tilt(ptzDto.getNPTZTilt())
                        .zoom(ptzDto.getNPTZZoom())
                        .build())
                .build();
        ReentrantLock reentrantLock = new ReentrantLock();
        Condition condition = reentrantLock.newCondition();
        AiRuiMsgDto aiRuiMsgDto = AiRuiMsgDto
                .builder()
                .uuid(uuid)
                .command("ptz_position")
                .sendMsgJson(JSON.toJSONString(build))
                .reentrantLock(reentrantLock)
                .condition(condition)
                .build();
        boolean request = request(aiRuiMsgDto);
        if (!request) {
            log.error("艾睿sdk 设置ptz失败！");
            return false;
        }
        try {
            reentrantLock.lock();
            boolean await = condition.await(10, TimeUnit.SECONDS);
            if (!await) {
                log.error("艾睿sdk 设置ptz超时！");
                return false;
            }
        } catch (Exception e) {
            log.error("艾睿sdk 设置ptz超时！");
        } finally {
            reentrantLock.unlock();
        }
        log.info("艾睿sdk 设置ptz成功！ res:{}", aiRuiMsgDto.getAcceptMsgJson());
        return true;
    }

    public boolean doPTZFun(Long deviceId, Integer nChannelID, PtzCmdParamVideoNode ptzControlReq) {
        AiRuiSdkDto aiRuiSdkDto = getAiRuiSdkDtoByDeviceId(deviceId);
        if (aiRuiSdkDto == null) {
            log.error("艾睿设备未登录！aiRuiSdkDto为空 deviceId:{}", deviceId);
            return false;
        }
        String uuid = UUID.randomUUID().toString();
        AiRuiPtzFunReq build = AiRuiPtzFunReq
                .builder()
                .command("ptz_control")
                .uuid(uuid)
                .value(AiRuiPtzFunReq.AiRuiValueDto
                        .builder()
                        .id(aiRuiSdkDto.getHandleId())
                        .channel(nChannelID)
                        .speed(5)
                        .stop(ptzControlReq.getAction() == 1 ? 0 : 1)
                        .build())
                .build();
        //     TOP: 上，BOTTOM: 下，LEFT: 左，RIGHT: 右，TOP_LEFT: 左上，TOP_RIGHT: 右上，BOTTOM_LEFT: 左下，BOTTOM_RIGHT: 右下，
        //     ZOOM_IN: 放大，ZOOM_OUT: 缩小，FOCUS_IN: 近焦距，FOCUS_OUT: 远焦距
        switch (ptzControlReq.getDirection()) {
            case "TOP":
                build.getValue().setCmd(0);
                break;
            case "BOTTOM":
                build.getValue().setCmd(1);
                break;
            case "LEFT":
                build.getValue().setCmd(2);
                break;
            case "RIGHT":
                build.getValue().setCmd(3);
                break;
            case "TOP_LEFT":
                build.getValue().setCmd(4);
                break;
            case "TOP_RIGHT":
                build.getValue().setCmd(5);
                break;
            case "BOTTOM_LEFT":
                build.getValue().setCmd(6);
                break;
            case "BOTTOM_RIGHT":
                build.getValue().setCmd(7);
                break;
            case "ZOOM_IN":
                build.getValue().setCmd(9);
                break;
            case "ZOOM_OUT":
                build.getValue().setCmd(8);
                break;
            case "FOCUS_IN":
                build.getValue().setCmd(10);
                break;
            case "FOCUS_OUT":
                build.getValue().setCmd(11);
                break;
            default:
                // Handle unexpected direction values or add more cases as needed
                break;
        }
        ReentrantLock reentrantLock = new ReentrantLock();
        Condition condition = reentrantLock.newCondition();
        AiRuiMsgDto aiRuiMsgDto = AiRuiMsgDto
                .builder()
                .uuid(uuid)
                .command("ptz_control")
                .sendMsgJson(JSON.toJSONString(build))
                .reentrantLock(reentrantLock)
                .condition(condition)
                .build();
        boolean request = request(aiRuiMsgDto);
        if (!request) {
            log.error("艾睿sdk ptzFun失败！");
            return false;
        }
        try {
            reentrantLock.lock();
            boolean await = condition.await(10, TimeUnit.SECONDS);
            if (!await) {
                log.error("艾睿sdk ptzFun 超时！");
                return false;
            }
        } catch (Exception e) {
            log.error("艾睿sdk ptzFun 超时！");
        } finally {
            reentrantLock.unlock();
        }
        log.info("艾睿sdk ptzFun成功！ res:{}", aiRuiMsgDto.getAcceptMsgJson());
        return true;
    }

    public boolean doPreJump(Long deviceId, Integer nChannelID, int index) {
        AiRuiSdkDto aiRuiSdkDto = getAiRuiSdkDtoByDeviceId(deviceId);
        if (aiRuiSdkDto == null) {
            throw new RuntimeException("艾睿sdk doPreJump 失败");
        }
        String uuid = UUID.randomUUID().toString();
        AiRuiPtzQueryPresetSizeReq build = AiRuiPtzQueryPresetSizeReq
                .builder()
                .command("ptz_position")
                .uuid(uuid)
                .value(AiRuiPtzQueryPresetSizeReq.AiRuiLoginValue
                        .builder()
                        .id(aiRuiSdkDto.getHandleId())
                        .build())
                .build();
        ReentrantLock reentrantLock = new ReentrantLock();
        Condition condition = reentrantLock.newCondition();
        AiRuiMsgDto aiRuiMsgDto = AiRuiMsgDto
                .builder()
                .uuid(uuid)
                .command("ptz_position")
                .sendMsgJson(JSON.toJSONString(build))
                .reentrantLock(reentrantLock)
                .condition(condition)
                .build();
        request(aiRuiMsgDto);
        try {
            reentrantLock.lock();
            boolean await = condition.await(10, TimeUnit.SECONDS);
            if (!await) {
                log.error("艾睿sdk 跳转预置点 超时！");
                return false;
            }
        } catch (Exception e) {
            log.error("艾睿sdk 跳转预置点 异常！", e);
        } finally {
            reentrantLock.unlock();
        }
        log.info("艾睿sdk 跳转预置点 成功！ res:{}", aiRuiMsgDto.getAcceptMsgJson());
        return true;
    }

    @PreDestroy
    public void destroy() throws Exception {
        if (processInput != null) {
            processInput.close();
        }
        if (processOutput != null) {
            processOutput.close();
        }
        if (process != null) {
            process.destroy();
            process.waitFor();
            log.info("艾睿sdk Rust 程序已关闭");
        }
    }
}
