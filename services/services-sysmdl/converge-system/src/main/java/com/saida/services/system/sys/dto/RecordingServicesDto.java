package com.saida.services.system.sys.dto;

import com.saida.services.common.mq.vlinker.BaseMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class RecordingServicesDto extends BaseMessage {
    /**
     * {
     * 	"server_name": "", // 服务名称
     * 	"node_id": "",     // 节点名称
     * 	"internal_ip": "", // 内网IP
     * 	"external_ip": "", // 外网IP
     * 	"domain_name": "", // 域名
     * 	"now_routes": 0,   // 当前负载
     * 	"max_routes": 0    // 最大负载
     * }
     */
    private String server_name;
    private String node_id;
    private String internal_ip;
    private String external_ip;
    private String domain_name;
    private Integer now_routes;
    private Integer max_routes;
    private Integer flow;
    private String service_id;

}
