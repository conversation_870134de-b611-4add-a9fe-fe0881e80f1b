package com.saida.services.system.rocketMq.alg;

import com.alibaba.fastjson.JSON;
import com.saida.services.common.mq.vlinker.*;
import com.saida.services.common.tools.RedisUtil;
import com.saida.services.system.rocketMq.message.FrameRateStatisticsMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 视频流地址监听更新
 */
@Slf4j
@Component

public class FrameRateStatisticsMessageListener implements VLinkerMqMessageListener {

    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("frame_rate_statistics")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        onMessage(messageBody);
    }
    @Resource
    private RedisUtil redisUtil;

    public void onMessage(String message) {
        log.info("[mq:{}]接收到消息：{}", vLinkerTopicConfig().getTopic(), message);
        FrameRateStatisticsMessage taskRecordMessage = JSON.parseObject(message, FrameRateStatisticsMessage.class);
        taskRecordMessage.getDeviceFrameRates().forEach((k, v) -> {
            redisUtil.set("ALG:frame_rate_statistics:" + k, JSON.toJSONString(v), 60 * 5);
        });
    }
}

