package com.saida.services.system.ops.service.impl;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.saida.services.common.config.oss.OSSUtil;
import com.saida.services.common.mq.message.CloudStrategyManagerMessage;
import com.saida.services.common.mq.vlinker.SendResultWrapper;
import com.saida.services.common.mq.vlinker.VLinkerMqTemplate;
import com.saida.services.common.tools.JwtUtil;
import com.saida.services.common.tools.SDNumberUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.converge.entity.DeviceRecordPlanEntity;
import com.saida.services.converge.entity.StorageStrategyEntity;
import com.saida.services.exception.BizRuntimeException;
import com.saida.services.system.ops.mapper.DeviceMapper;
import com.saida.services.system.ops.mapper.StorageStrategyMapper;
import com.saida.services.system.ops.service.DeviceRecordPlanService;
import com.saida.services.system.ops.service.OpsDeviceChannelService;
import com.saida.services.system.ops.service.StorageStrategyService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service("storageStrategyService")
public class StorageStrategyServiceImpl extends ServiceImpl<StorageStrategyMapper, StorageStrategyEntity> implements StorageStrategyService {

    @Autowired
    private DeviceMapper deviceMapper;
    @Resource
    private OpsDeviceChannelService opsDeviceChannelService;
    @Autowired(required = false)
    private VLinkerMqTemplate vLinkerMqTemplate;
    @Autowired
    private OSSUtil ossUtil;

    @Getter
    @AllArgsConstructor
    enum StrategyType {
        /**
         * 按天存储
         */
        DAYS(1, "DAYS"),

        /**
         * 按容量存储
         */
        CAPACITY(2, "CAPACITY");

        private final Integer type;
        private final String code;

        public static StorageStrategyServiceImpl.StrategyType getType(Integer type) {
            for (StorageStrategyServiceImpl.StrategyType t : values()) {
                if (SDNumberUtil.equals(type, t.type)) {
                    return t;
                }
            }
            return null;
        }
    }

    @Override
    public void addOrUpdate(StorageStrategyEntity entity) {
        if (StringUtil.isEmpty(entity.getName())) {
            throw new BizRuntimeException("策略名称必填");
        }
        if (entity.getType() == null) {
            throw new BizRuntimeException("策略类型必选");
        }

        if (StrategyType.getType(entity.getType()) == null) {
            throw new BizRuntimeException("策略类型错误");
        }

        if (StrategyType.getType(entity.getType()) == StrategyType.CAPACITY) {
            throw new BizRuntimeException("暂不支持容量策略");
        }

        if (entity.getNumValue() == null) {
            throw new BizRuntimeException("策略值必填");
        }
        if (entity.getNumValue() < 1) {
            throw new BizRuntimeException("策略值错误");
        }

        if (entity.getId() == null) {// 新增
            entity.setCreateTime(DateTime.now());
            entity.setCreateUser(JwtUtil.getUserId());
            save(entity);
//            reloadCloudStorageRule();
            syncMq(entity.getId(), ActionEnum.EDIT);
        } else {// 修改
            entity.setUpdateTime(DateTime.now());
            entity.setUpdateUser(JwtUtil.getUserId());
            updateById(entity);
            syncMq(entity.getId(), ActionEnum.EDIT);
        }
    }

//    private void reloadCloudStorageRule() {
//        List<CloudStorageEntity> clouds = cloudStorageMapper.selectList(null);
//        List<StorageStrategyEntity> strategys = list(null);
//
//        for (CloudStorageEntity c : clouds) {
//
//            OSSBean build = OSSBean
//                    .builder()
//                    .accessKey(c.getKeyId())
//                    .secretKey(c.getSecret())
//                    .endPoint(c.getEndPoint())
//                    .bucket(c.getBucket())
//                    .build();
//            OSSMethodInterface ossMethodInterface = ossUtil.getOSSMethodInterface(OSSTypeEnum.getByTypeId(c.getType()));
//
//            BucketLifecycleConfiguration bucketLifecycleConfiguration = new BucketLifecycleConfiguration();
//            List<BucketLifecycleConfiguration.Rule> rules = new ArrayList<BucketLifecycleConfiguration.Rule>();
//            bucketLifecycleConfiguration.setRules(rules);
//            for (StorageStrategyEntity s : strategys) {
//                BucketLifecycleConfiguration.Rule r = new BucketLifecycleConfiguration.Rule();
//                r.setId(String.valueOf(s.getId()));
//                r.setPrefix(String.format("r/%s", s.getId()));
//                r.setStatus("Enabled");
//                r.setExpirationInDays(s.getNumValue());
//                rules.add(r);
//            }
//            ossMethodInterface.setBucketLifecycleConfiguration(build, bucketLifecycleConfiguration);
//        }
//    }

    @Override
    public void syncMq(Long id, ActionEnum action) {
        StorageStrategyEntity strategy = getById(id);
        if (strategy == null) {
            log.error("存储策略不存在");
            return;
        }
        String mq_topic = "cloud_strategy_manager";
        SendResultWrapper sendRet = vLinkerMqTemplate.send(mq_topic, new CloudStrategyManagerMessage() {{
            setId(strategy.getId());
            setAction(action.getAction());
            setName(strategy.getName());
            switch (Objects.requireNonNull(StrategyType.getType(strategy.getType()))) {
                case DAYS:
                    setType(StrategyType.DAYS.getCode());
                    break;
                case CAPACITY:
                    setType(StrategyType.CAPACITY.getCode());
                    break;
            }
            setValue(strategy.getNumValue());
        }});
        log.info("同步云存策略结果：{}", sendRet);
    }

    @Override
    public IPage<StorageStrategyEntity> listPage(StorageStrategyEntity entity) {
        return page(new Page<>(entity.getPageNum(), entity.getPageSize()),
                new LambdaQueryWrapper<StorageStrategyEntity>()
                        .eq(entity.getType() != null, StorageStrategyEntity::getType, entity.getType())
                        .like(!StringUtil.isEmpty(entity.getName()), StorageStrategyEntity::getName, entity.getName())
        );
    }

    @Override
    public List<StorageStrategyEntity> getList(StorageStrategyEntity entity) {
        return list(new LambdaQueryWrapper<StorageStrategyEntity>()
                .eq(entity.getType() != null, StorageStrategyEntity::getType, entity.getType())
                .like(!StringUtil.isEmpty(entity.getName()), StorageStrategyEntity::getName, entity.getName())
        );
    }

    @Override
    public StorageStrategyEntity getInfo(Long id) {
        return getById(id);
    }

    @Resource
    private DeviceRecordPlanService deviceRecordPlanService;
    @Override
    public void delete(Long id) {
        long count = deviceRecordPlanService.count(new LambdaQueryWrapper<DeviceRecordPlanEntity>()
                .eq(DeviceRecordPlanEntity::getStrategyId, id));
        if (count > 0) {
            throw new BizRuntimeException("有设备正在使用该策略，无法删除");
        }
        syncMq(id, ActionEnum.DEL);
        removeById(id);
    }
}