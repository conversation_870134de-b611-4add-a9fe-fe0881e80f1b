package com.saida.services.system.peopleDeployControl.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * description your class purpose
 *
 * <AUTHOR>
 * @version PeopleIdentifyDeployControlPageQryDto v1.0.0
 * @since 2025/6/17 9:50
 */
@Data
public class PeopleIdentifyDeployControlPageQryVo {
    /**
     * id
     */
    private Long id;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 云服务id
     */
    private Long cloudServerId;
    /**
     * 时间计划id
     */
    private Long timeTemplateId;
    /**
     * 时间计划名称
     */
    private String timeTemplateName;
    /**
     * 人脸相似度
     */
    private String faceSimilarity;
    /**
     * 相同人告警间隔
     */
    private Integer samePersonAlarmInterval;
    /**
     * 相同人告警间隔单位，1-秒，2-分，3-时，4-天
     */
    private Integer samePersonAlarmIntervalUnit;
    /**
     * 运行状态 0：未启用 1：运行中
     */
    private Integer status;
    /**
     * 最后开始运行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime runStartTime;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 分组id
     */
    private List<Long> groupIds;
    /**
     * 分组名称，逗号分割
     */
    private String groupNames;

    /**
     * 相机id
     */
    private List<Long> cameraIds;
    /**
     * 相机名称，逗号分割
     */
    private String cameraNames;
    /**
     * 算法id
     */
    private List<Long> algorithmIds;
    /**
     * 算法名称，逗号分割
     */
    private String algorithmNames;

    /**
     * 运行时长（分钟）
     */
    private Long runDuration;
}
