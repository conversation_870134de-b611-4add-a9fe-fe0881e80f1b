package com.saida;


import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.saida.services.algorithm.dto.AlgorithmReceiveAlarmMqDto;
import com.saida.services.algorithm.entity.AlgorithmMappingEntity;
import com.saida.services.common.dto.AlarmNormalizationExt;
import com.saida.services.common.tools.DateUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.enums.AlgAlgorithmSourceEnum;
import com.saida.services.system.ConvergeSystemApplication;
import com.saida.services.system.alarm.entity.AlarmEntity;
import com.saida.services.system.algorithm.service.AlgorithmMappingService;
import com.saida.services.system.callback.CallBackInvoke;
import com.saida.services.system.callback.CallBackMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.List;

@Slf4j
@SpringBootTest(classes = ConvergeSystemApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
public class AlarmTest {

    @Lazy
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private CallBackInvoke callBackInvoke;
    @Resource
    private AlgorithmMappingService algorithmMappingService;

    @Test
    public void test() {
        AlgorithmReceiveAlarmMqDto dto = new AlgorithmReceiveAlarmMqDto();
        dto.setAlgorithmCode("H_DET_PERSON");
        dto.setDeviceId(1936001001879375872L);
        dto.setTime(System.currentTimeMillis());
        dto.setImage("https://test-vlinker-minio-api.sdvideo.cn:48801/v-linker/common/2025/06/23/9f8a6989-43f0-451b-b443-569b4ebe0c29.png");
        dto.setAnalysisImage("http://192.168.1.1/analysisImage.jpg");
        dto.setAnalysisImageNotProb("http://192.168.1.1/analysisImageNotProb.jpg");
        dto.setTaskId(IdWorker.getId());
        dto.setMsgId(IdWorker.getId());
        dto.setWidth(1920);
        dto.setHeight(1080);
        String algorithmCode = dto.getAlgorithmCode();
        Long deviceId = dto.getDeviceId();
        Long time = dto.getTime();
        String imageUrl = dto.getImage();
        String analysisImageNotProbUrl = dto.getAnalysisImageNotProb();
        String analysisImageUrl = dto.getAnalysisImage();
        List<AlgorithmMappingEntity> algorithmMappingEntityList = algorithmMappingService.list(new LambdaUpdateWrapper<AlgorithmMappingEntity>()
                .eq(AlgorithmMappingEntity::getSourceId, "1927922805882519553")
                .eq(AlgorithmMappingEntity::getCode, algorithmCode));
        AlarmEntity saveAlarmEntity = new AlarmEntity();
        saveAlarmEntity.setTaskId(dto.getTaskId());
        saveAlarmEntity.setMsgId(String.valueOf(dto.getMsgId()));
        saveAlarmEntity.setExt(JSON.toJSONString(dto.getAlgorithmsResult()));
//        saveAlarmEntity.setCloudServerId(cloudServerId);
        saveAlarmEntity.setNormalizationExt(JSON.toJSONString(new Object()));
        saveAlarmEntity.setDeviceId(deviceId);
        saveAlarmEntity.setAlarmSource(AlgAlgorithmSourceEnum.CLOUD_SERVICE.getDicId());
        saveAlarmEntity.setAlgorithmId(algorithmMappingEntityList.get(0).getAlgorithmId());
        saveAlarmEntity.setOriginalAlarmStr("vlinker-cloud:" + algorithmCode);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtil.DATE_TIME_PATTERN);
        String alarmDateTime = simpleDateFormat.format(time);
        if (StringUtil.isNotEmpty(alarmDateTime)) {
            saveAlarmEntity.setYear(alarmDateTime.substring(0, 4));
            saveAlarmEntity.setMonth(alarmDateTime.substring(0, 7));
            saveAlarmEntity.setDay(alarmDateTime.substring(0, 10));
            saveAlarmEntity.setTime(alarmDateTime.substring(11));
            saveAlarmEntity.setAlarmTime(alarmDateTime);
        }
        saveAlarmEntity.setAlarmTimeLong(time);
        saveAlarmEntity.setOriginalImageUrl(imageUrl);
        saveAlarmEntity.setAlarmImageNotProbUrl(analysisImageNotProbUrl);
        saveAlarmEntity.setAlarmImageUrl(analysisImageUrl);
        Long alarmId = IdWorker.getId();
        saveAlarmEntity.setId(alarmId);
        CallBackMessage callBackMessage = new CallBackMessage() {{
            setAlarmId(alarmId);
            setMsgId(IdUtil.fastSimpleUUID());
            setDeviceId(String.valueOf(deviceId));
            setDeviceCode("");
//            setChannelId(saveAlarmEntity.getChannelId());
            setAlgId(saveAlarmEntity.getAlgorithmId());
            setAlertType(String.valueOf(saveAlarmEntity.getAlgorithmId()));
            setAlertTypeName("人测试");
            setCreateTime(alarmDateTime);
            setAlertSource(Math.toIntExact(AlgAlgorithmSourceEnum.CLOUD_SERVICE.getTag()));
            setAlertSourceName(AlgAlgorithmSourceEnum.CLOUD_SERVICE.getName());
            setOriginalSrcUrl(imageUrl);
            setSrcNotProbUrl(analysisImageNotProbUrl);
            setSrcUrl(analysisImageUrl);
            setOriginalAlarmStr(saveAlarmEntity.getOriginalAlarmStr());
            setExt(JSON.toJSONString(dto.getAlgorithmsResult()));
            AlarmNormalizationExt alarmNormalizationExt = new AlarmNormalizationExt();
            setNormalizationExt(alarmNormalizationExt);
        }};
        saveAlarmEntity.setCallbackMessage(JSON.toJSONString(callBackMessage));
        // 3.把告警信息推送给第三方
        threadPoolTaskExecutor.execute(() -> callBackInvoke.callBack(saveAlarmEntity, callBackMessage, true, true));
    }

}
