package com.saida.services.system.alram.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.dahuatech.hutool.json.JSONObject;
import com.dahuatech.hutool.json.JSONUtil;
import com.saida.services.algorithm.dto.PushAlarmDto;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.service.FileService;
import com.saida.services.common.tools.DateUtil;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.entities.pojo.FileModel;
import com.saida.services.enums.AlgAlgorithmSourceEnum;
import com.saida.services.open.dto.OpenNoticeOnLineMsgDTO;
import com.saida.services.open.dto.OpenNoticeThirdPartyAppMsgDTO;
import com.saida.services.open.entity.AlgorithmMappingEntity;
import com.saida.services.open.entity.DeviceInfoEntity;
import com.saida.services.open.entity.ThirdPartyPlatformsAuthEntity;
import com.saida.services.open.enums.OpenAlgorithmSourceEnum;
import com.saida.services.open.enums.OpenNoticeThirdPartyAppEnum;
import com.saida.services.system.biz.service.dahuaIcc.DaHuaRedisConstant;
import com.saida.services.system.biz.service.dahuaIcc.enums.EventCategoryEnums;
import com.saida.services.system.biz.service.dahuaIcc.model.event.eventSubcribe.AlarmVO;
import com.saida.services.system.biz.service.dahuaIcc.model.event.eventSubcribe.ReceiveMsgVO;
import com.saida.services.system.biz.service.dahuaIcc.model.event.eventSubcribe.StateVO;
import com.saida.services.system.sys.service.AlgorithmMappingService;
import com.saida.services.system.sys.service.DeviceInfoService;
import com.saida.services.system.sys.service.ThirdPartyPlatformsAuthService;
import com.saida.services.system.thread.AlarmHandleThread;
import com.saida.services.system.thread.MsgPushThread;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashSet;

@Slf4j
@Component
public class DaHuaAlarmReceiveService {

    @Resource
    private DeviceInfoService deviceInfoService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private AlarmHandleThread alarmHandleThread;
    @Resource
    private AlgorithmMappingService algorithmMappingService;
    @Resource
    private ThirdPartyPlatformsAuthService thirdPartyPlatformsAuthService;
    @Resource
    private MsgPushThread msgPushThread;
    @Resource
    private FileService fileService;

    /**
     * 接收并处理大华AI告警数据。
     *
     * @param data 告警数据的JSON字符串。
     * @return 处理结果的DTO。
     */
    public DtoResult<Void> receiveData(JSONObject data) {
        String logId = IdUtil.fastSimpleUUID();
        log.info("V-LINKER能力开放平台.接收大华事件订阅数据...logId={}, data={}", logId, JSONUtil.toJsonStr(data));

        try {
            ReceiveMsgVO receiveMsgVO = JSONUtil.toBean(data, ReceiveMsgVO.class);
            if (ObjectUtil.isNull(receiveMsgVO) || ObjectUtil.isNull(receiveMsgVO.getInfo())) {
                log.info("V-LINKER能力开放平台.接收大华事件订阅数据为空...logId={}, data={}", logId, data);
                return DtoResult.ok();
            }

            if (EventCategoryEnums.alarm.getCode().equals(receiveMsgVO.getCategory())) {
                return handleAlarmEvent(logId, receiveMsgVO);
            } else if (EventCategoryEnums.state.getCode().equals(receiveMsgVO.getCategory())) {
                return handleStateEvent(logId, receiveMsgVO);
            } else if (EventCategoryEnums.business.getCode().equals(receiveMsgVO.getCategory())) {
                return handleBusinessEvent(logId, receiveMsgVO);
            }
        } catch (Exception e) {
            log.error("V-LINKER能力开放平台.接收大华事件订阅数据..错误...logId={}, msg={}", logId, e.getMessage(), e);
        }
        return DtoResult.ok();
    }

    private DtoResult<Void> handleBusinessEvent(String logId, ReceiveMsgVO receiveMsgVO) {
        return DtoResult.ok();
    }

    private DtoResult<Void> handleAlarmEvent(String logId, ReceiveMsgVO receiveMsgVO) {
        JSONObject info = receiveMsgVO.getInfo();
        AlarmVO alarmVO = JSONUtil.toBean(info, AlarmVO.class);
        DeviceInfoEntity deviceInfoEntity = getDeviceInfoByChannelId(alarmVO.getNodeCode(), logId);
        if (deviceInfoEntity == null) {
            return DtoResult.ok();
        }
        ThirdPartyPlatformsAuthEntity authEntity = thirdPartyPlatformsAuthService.getById(deviceInfoEntity.getTripartiteId());
        if (authEntity == null) {
            log.info("V-LINKER能力开放平台.接收大华事件订阅数据（告警）..授权不存在...logId={}, deviceId={}", logId, alarmVO.getDeviceCode());
            return DtoResult.ok();
        }
        String accessToken = redisTemplate.opsForValue().get(DaHuaRedisConstant.DA_HUA_TOKEN_KEY + authEntity.getAppKey());
        if (StringUtil.isBlank(accessToken)) {
            return DtoResult.error("大华token不存在");
        }
        AlgorithmMappingEntity algorithmMappingEntity = algorithmMappingService.getOne(
                new LambdaQueryWrapper<AlgorithmMappingEntity>()
                        .eq(AlgorithmMappingEntity::getSourceId, OpenAlgorithmSourceEnum.DA_HUA_ICC.getCode())
                        .like(AlgorithmMappingEntity::getCode, String.valueOf(alarmVO.getAlarmType())), false
        );
        if (algorithmMappingEntity == null) {
            log.info("V-LINKER能力开放平台.接收大华事件订阅数据..对应算法不存在...logId={}", logId);
            return DtoResult.ok();
        }
        PushAlarmDto pushAlarmDto = buildPushAlarmDto(deviceInfoEntity, alarmVO, algorithmMappingEntity, authEntity, accessToken);
        alarmHandleThread.handleAlarm(pushAlarmDto);

        return DtoResult.ok();
    }

    private DtoResult<Void> handleStateEvent(String logId, ReceiveMsgVO receiveMsgVO) {
        StateVO stateVO = JSONUtil.toBean(receiveMsgVO.getInfo(), StateVO.class);

        DeviceInfoEntity deviceInfoEntity = getDeviceInfoByChannelId(stateVO.getChannelId(), logId);
        if (deviceInfoEntity == null) {
            return DtoResult.ok();
        }
        deviceInfoEntity.setOnlineStatus(stateVO.getStatus());
        deviceInfoService.updateById(deviceInfoEntity);

        HashSet<String> deviceIdSet = new HashSet<>();
        deviceIdSet.add(deviceInfoEntity.getDeviceId());

        // 创建在线状态通知消息
        OpenNoticeOnLineMsgDTO openNoticeOnLineMsgDTO = new OpenNoticeOnLineMsgDTO();
        openNoticeOnLineMsgDTO.setDeviceIdSet(deviceIdSet);
        openNoticeOnLineMsgDTO.setOnline(stateVO.getStatus());

        // 创建第三方应用通知对象
        OpenNoticeThirdPartyAppMsgDTO openNoticeThirdPartyAppMsgDTO = new OpenNoticeThirdPartyAppMsgDTO();
        openNoticeThirdPartyAppMsgDTO.setMsgId(IdUtil.fastSimpleUUID());
        openNoticeThirdPartyAppMsgDTO.setMsgType(OpenNoticeThirdPartyAppEnum.ONE);
        openNoticeThirdPartyAppMsgDTO.setData(JSON.toJSON(openNoticeOnLineMsgDTO));

        // 向应用推送上下线消息
        msgPushThread.notifyTheApp(openNoticeThirdPartyAppMsgDTO, deviceInfoEntity.getTripartiteSn());
        return DtoResult.ok();
    }

    private DeviceInfoEntity getDeviceInfoByCode(String deviceCode, String logId) {
        DeviceInfoEntity deviceInfoEntity = deviceInfoService.getOne(
                new LambdaQueryWrapper<DeviceInfoEntity>()
                        .eq(DeviceInfoEntity::getTripartiteSn, deviceCode).last("LIMIT 1"), false
        );
        if (deviceInfoEntity == null) {
            log.info("V-LINKER能力开放平台.接收大华事件订阅数据..设备不存在...logId={}, deviceId={}", logId, deviceCode);
        }
        return deviceInfoEntity;
    }

    private DeviceInfoEntity getDeviceInfoByChannelId(String channelId, String logId) {
        DeviceInfoEntity deviceInfoEntity = deviceInfoService.getOne(
                new LambdaQueryWrapper<DeviceInfoEntity>()
                        .eq(DeviceInfoEntity::getChannelId, channelId).last("LIMIT 1"), false
        );
        if (deviceInfoEntity == null) {
            log.info("V-LINKER能力开放平台.接收大华事件订阅数据..设备不存在...logId={}, channelId={}", logId, channelId);
        }
        return deviceInfoEntity;
    }

    private PushAlarmDto buildPushAlarmDto(DeviceInfoEntity deviceInfoEntity, AlarmVO alarmVO, AlgorithmMappingEntity algorithmMappingEntity, ThirdPartyPlatformsAuthEntity authEntity, String accessToken) {
        PushAlarmDto pushAlarmDto = new PushAlarmDto();
        pushAlarmDto.setMsgReqNo(IdUtil.fastSimpleUUID());
        pushAlarmDto.setDeviceCode(deviceInfoEntity.getId());
        pushAlarmDto.setAlertType(String.valueOf(algorithmMappingEntity.getAlgorithmId()));
        pushAlarmDto.setAlertTypeName(algorithmMappingEntity.getName());
        pushAlarmDto.setAlertTime(DateUtil.convertTimestampToDateTime(Long.valueOf(alarmVO.getAlarmDate()) * 1000));
        pushAlarmDto.setAlertSource(Math.toIntExact(AlgAlgorithmSourceEnum.CLOUD_BASED_CAMERA.getTag()));
        String ossUrl = String.format("%s/evo-apigw/evo-oss/%s?token=%s",
                authEntity.getHost(),
                alarmVO.getAlarmPicture(),
                accessToken
        );
        // 毛都图片转存
        String dateFormat = cn.hutool.core.date.DateUtil.format(new Date(), "yyyyMMdd");
        String ossObjKey = "alarm/gqmd/" + deviceInfoEntity.getId() + "/" + dateFormat + "/" + IdWorker.getId() + ".jpg";
        DtoResult<FileModel> fileModelDtoResult = fileService.uploadToS3(ossUrl, ossObjKey);
        if (fileModelDtoResult.success()) {
            log.info("干起毛都接收大华告警消息：原始图片地址：{} 上传后:{}", ossUrl, fileModelDtoResult.getData().getUrl());
            pushAlarmDto.setSrcUrl(fileModelDtoResult.getData().getUrl());
        }
        return pushAlarmDto;
    }

}