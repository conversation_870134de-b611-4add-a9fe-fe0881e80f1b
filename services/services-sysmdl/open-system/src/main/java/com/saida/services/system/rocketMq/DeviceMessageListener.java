//package com.saida.services.system.rocketMq;
//
//import com.alibaba.fastjson.JSON;
//import com.saida.services.common.mq.VLinkerMqMessageListener;
//import com.saida.services.common.mq.message.OsdMessage;
//import com.saida.services.open.entity.DeviceInfoEntity;
//import com.saida.services.system.sys.entity.UavDeviceRefEntity;
//import com.saida.services.system.sys.service.DeviceInfoService;
//import com.saida.services.system.sys.service.UavDeviceRefService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.rocketmq.common.message.MessageExt;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.nio.charset.StandardCharsets;
//import java.util.Date;
//import java.util.Objects;
//
///**
// * <AUTHOR>
// */
//@Slf4j
//@Component
//@ConditionalOnProperty(prefix = "rocketmq", name = "enable", havingValue = "true")
//public class DeviceMessageListener implements VLinkerMqMessageListener {
//
//    @Resource
//    private UavDeviceRefService uavDeviceRefService;
//    @Resource
//    private DeviceInfoService deviceInfoService;
//
//    @Override
//    public String getTopic() {
//        return "osd_message";
//    }
//
//
//
//    @Override
//    public void onMessage(MessageExt messageExt) {
//        String messageBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
//        OsdMessage message = JSON.parseObject(messageBody, OsdMessage.class);
//        handleMessage(message);
//    }
//
//    public void handleMessage(OsdMessage message) {
//        // 查询sn
//        DeviceInfoEntity entity = deviceInfoService.findByTripartiteSn(message.getSn());
//        DeviceInfoEntity deviceEntity = convert2DeviceInfoEntity(message, entity);
//        UavDeviceRefEntity uavEntity = convert2UavDeviceRefEntity(message, entity);
//        if (Objects.isNull(entity)) {
//            deviceInfoService.save(deviceEntity);
//            uavDeviceRefService.save(uavEntity);
//        } else {
//            deviceInfoService.updateById(deviceEntity);
//            uavDeviceRefService.updateBySn(uavEntity);
//        }
//    }
//
//    private DeviceInfoEntity convert2DeviceInfoEntity(OsdMessage message, DeviceInfoEntity entity) {
//        DeviceInfoEntity res = new DeviceInfoEntity();
//        if (Objects.nonNull(entity)) {
//            res.setId(entity.getId());
//        }
//
//        res.setDeviceSn(message.getSn());
//        res.setDeviceName(message.getName());
//        res.setTripartiteSn(message.getSn());
//        // TODO
////        res.setDeviceModel(message.getModel());
//        res.setLon(message.getLon());
//        res.setLat(message.getLat());
//        res.setType(10000700006L);
//        res.setDeviceType(10000700006L);
//        res.setOnlineStatus(1);
//        return res;
//    }
//
//    private UavDeviceRefEntity convert2UavDeviceRefEntity(OsdMessage message, DeviceInfoEntity entity) {
//        UavDeviceRefEntity res = UavDeviceRefEntity.builder()
//                .sn(message.getSn())
//                .domain(message.getDomain())
//                .firmwareVersion(message.getFirmwareVersion())
//                .onlineTime(Objects.isNull(entity) || entity.getOnlineStatus() == 0 ? new Date() : null)
//                .onlineStatus(1)
//                .mainControlSn(message.getMainControlSn())
//                .dockModeCode(message.getDockModeCode())
//                .firmwareStatus(message.getFirmwareStatus())
//                .airConditionerState(message.getAirConditionerState())
//                .jobNumber(message.getJobNumber())
//                .workingCurrent(message.getWorkingCurrent())
//                .workingVoltage(message.getWorkingVoltage())
//                .landPointIsConfigured(message.getLandPointIsConfigured())
//                .backupBatterySwitch(message.getBackupBatterySwitch())
//                .backupBatteryTemperature(message.getBackupBatteryTemperature())
//                .humidity(message.getHumidity())
//                .temperature(message.getTemperature())
//                .environmentTemperature(message.getEnvironmentTemperature())
//                .nightLightsState(message.getNightLightsState())
//                .heightLimit(message.getHeightLimit())
//                .distanceLimit(message.getDistanceLimit())
//                .obstacleAvoidance(message.getObstacleAvoidance())
//                .flysafeDatabaseVersion(message.getFlysafeDatabaseVersion())
//                .batteryCapacityPercent(message.getBatteryCapacityPercent())
//                .batteryVoltage(message.getBatteryVoltage())
//                .batteryTemperature(message.getBatteryTemperature())
//                .build();
//        if (Objects.nonNull(entity)) {
//            res.setId(entity.getId());
//        }
//        return res;
//    }
//
//
//
//}