package com.saida.services.system.biz.service.dahuaIcc.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dahuatech.hutool.json.JSONUtil;
import com.dahuatech.icc.exception.ClientException;
import com.dahuatech.icc.oauth.model.v202010.GeneralResponse;
import com.dahuatech.icc.oauth.model.v202010.OauthConfigUserPwdInfo;
import com.dahuatech.icc.oauth.utils.HttpUtils;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.open.dahua.req.DoorSwitchRequest;
import com.saida.services.open.dahua.req.DoorSwitchStatusRequest;
import com.saida.services.open.dahua.req.QueryRecordRequest;
import com.saida.services.open.dahua.resp.DevicePageResponse;
import com.saida.services.open.dahua.resp.DoorChannelStatusResponse;
import com.saida.services.open.dahua.resp.QueryRecordResponse;
import com.saida.services.open.entity.ThirdPartyPlatformsAuthEntity;
import com.saida.services.system.biz.service.dahuaIcc.enums.DaHuaIccApiEnum;
import com.saida.services.system.biz.service.dahuaIcc.model.DaHuaCommonIccResponse;
import com.saida.services.system.biz.service.dahuaIcc.model.brm.device.DevicePageRequest;
import com.saida.services.system.biz.service.dahuaIcc.model.event.eventSubcribe.SubscribeRequest;
import com.saida.services.system.biz.service.dahuaIcc.model.oauth.GetTokenRequest;
import com.saida.services.system.biz.service.dahuaIcc.model.video.ptzControl.*;
import com.saida.services.system.biz.service.dahuaIcc.model.video.realTimePreview.*;
import com.saida.services.system.biz.service.dahuaIcc.model.video.videoReplay.*;
import com.saida.services.system.biz.service.dahuaIcc.uitl.RSAUtil;
import com.saida.services.system.biz.service.dahuaIcc.uitl.RestTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Slf4j
@Component
public class DaHuaVideoUtil {

    /**
     * 获取Oauth配置信息
     *
     * @return Oauth配置信息
     */
    public static OauthConfigUserPwdInfo getOauthConfig(ThirdPartyPlatformsAuthEntity entity) {
        String ipPort = entity.getHost().replace("https://", "").replace("http://", "");
        OauthConfigUserPwdInfo oauthConfigUserPwdInfo = new OauthConfigUserPwdInfo(
                ipPort.split(":")[0],
                entity.getAppKey(),
                entity.getAppSecret(),
                entity.getUserName(),
                entity.getPassword(),
                false,
                // https端口 默认443 http端口 默认83
                ipPort.split(":")[1],
                ipPort.split(":")[1]
        );
        oauthConfigUserPwdInfo.getHttpConfigInfo().setReadTimeout(-1L);
        oauthConfigUserPwdInfo.getHttpConfigInfo().setConnectionTimeout(-1L);
        return oauthConfigUserPwdInfo;
    }

    /**
     * 获取全量视频设备信息-递归获取
     */
    public DevicePageResponse getDeviceList(ThirdPartyPlatformsAuthEntity entity, BaseRequest req) {
        DevicePageRequest devicePageRequest = new DevicePageRequest();
        devicePageRequest.setPageNum(req.getPageNum());
        devicePageRequest.setPageSize(req.getPageSize());
        //不传查所有
//        List<Integer> categorys = new ArrayList<>();
//        //视频设备
//        categorys.add(1);
//        devicePageRequest.setCategorys(categorys);
        DevicePageResponse currentResponse = getDevicePage(entity, devicePageRequest);

        if (currentResponse.isSuccess() && currentResponse.getData() != null) {
            Integer totalPages = currentResponse.getData().getTotalPage();

            if (totalPages != null && totalPages > req.getPageNum()) {
                // 递归获取后续页
                req.setPageNum(req.getPageNum() + 1);
                DevicePageResponse nextResponse = getDeviceList(entity, req);

                // 合并数据
                if (nextResponse.isSuccess() && nextResponse.getData() != null) {
                    currentResponse.getData().getPageData().addAll(nextResponse.getData().getPageData());
                    currentResponse.getData().setTotalRows(currentResponse.getData().getTotalRows() + nextResponse.getData().getTotalRows());
                }
            }
        }
        return currentResponse;
    }


    /**
     * 设备全量数据同步
     */
    public DevicePageResponse getDevicePage(ThirdPartyPlatformsAuthEntity entity, DevicePageRequest devicePageRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(entity);
        DevicePageResponse response = null;
        try {
            log.info("大华ICC-分页获取设备信息列表,config：{},devicePageRequest:{}", JSONUtil.toJsonStr(config), JSONUtil.toJsonStr(devicePageRequest));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.POST_DEVICE_PAGE_LIST.getPath(), devicePageRequest, null, DaHuaIccApiEnum.POST_All_DEVICE_LIST.getHttpMethod(), config, DevicePageResponse.class);
            log.info("大华ICC-分页获取设备信息列表,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-分页获取设备信息列表异常", e);
        }
        return response;
    }

    public RtspUrlResponse getRtspUrl(ThirdPartyPlatformsAuthEntity entity, RtspUrlRequest rtspUrlRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(entity);
        RtspUrlResponse response = null;
        try {
            log.info("大华ICC-rtsp实时预览,request:{}", JSONUtil.toJsonStr(rtspUrlRequest));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.RTSP_VIDEO_LIVE_URL.getPath(), rtspUrlRequest, null, DaHuaIccApiEnum.RTSP_VIDEO_LIVE_URL.getHttpMethod(), config, RtspUrlResponse.class);
            log.info("大华ICC-rtsp实时预览,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-rtsp实时预览异常", e);
        }
        return response;
    }

    public String getAccessToken(ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity) {
        try {
            GetTokenRequest getTokenRequest = new GetTokenRequest();
            String publicKey = getPublicKey(thirdPartyPlatformsAuthEntity);
            getTokenRequest.setGrant_type("password");
            getTokenRequest.setUsername(thirdPartyPlatformsAuthEntity.getUserName());
            getTokenRequest.setPassword(RSAUtil.encrypt(thirdPartyPlatformsAuthEntity.getPassword(), publicKey));
            getTokenRequest.setClient_id(thirdPartyPlatformsAuthEntity.getAppKey());
            getTokenRequest.setClient_secret(thirdPartyPlatformsAuthEntity.getAppSecret());
            getTokenRequest.setPublic_key(publicKey);
            RestTemplate restTemplate = RestTemplateUtil.getRestTemplate();
            log.info("大华ICC-getToken,request:{}", JSONUtil.toJsonStr(getTokenRequest));
            ResponseEntity<String> response = restTemplate.postForEntity(thirdPartyPlatformsAuthEntity.getHost() + DaHuaIccApiEnum.GET_ACCESS_TOKEN.getPath(), getTokenRequest, String.class);
            log.info("大华ICC-getToken,response:{}", response.getBody());
            Map data = (Map) com.alibaba.fastjson.JSON.parseObject(response.getBody(), new TypeReference<Map<String, Object>>() {
            }).get("data");
            return data.get("access_token").toString();
        } catch (Exception e) {
            log.error("OauthDemo,getToken,error:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取公钥(用于调认证接口时对密码进行RSA加密)
     * 原生代码调用，不使用sdk
     *
     * @return 公钥
     * @throws Exception
     */
    public String getPublicKey(ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity) {
        String host = thirdPartyPlatformsAuthEntity.getHost();
        String url = host + DaHuaIccApiEnum.GET_PUBLIC_KEY.getPath();
        log.info("大华ICC-获取公钥,url:{}", url);
        RestTemplate restTemplate = RestTemplateUtil.getRestTemplate();
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        log.info("大华ICC-获取公钥,response:{}", response.getBody());
        Map data = (Map) JSON.parseObject(response.getBody(), new TypeReference<Map<String, Object>>() {
        }).get("data");
        String publicKey = data.get("publicKey").toString();
        log.info("大华ICC-获取公钥,publicKey:{}", publicKey);
        return publicKey;
    }

    public HlsUrlResponse getOtherStreamUrl(ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity, HlsUrlRequest hlsUrlRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(thirdPartyPlatformsAuthEntity);
        HlsUrlResponse response = null;
        try {
            log.info("大华ICC-HLS、FLV、RTMP实时预览接口方式,request:{}", JSONUtil.toJsonStr(hlsUrlRequest));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.HLS_FLV_RTMP_VIDEO_LIVE_URL.getPath(), hlsUrlRequest, null, DaHuaIccApiEnum.HLS_FLV_RTMP_VIDEO_LIVE_URL.getHttpMethod(), config, HlsUrlResponse.class);
            log.info("大华ICC-HLS、FLV、RTMP实时预览接口方式,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-HLS、FLV、RTMP实时预览接口方式异常", e);
        }
        return response;
    }

    public StartTalkResponse rtcConnect(ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity, StartTalkRequest startTalkRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(thirdPartyPlatformsAuthEntity);
        StartTalkResponse response = null;
        try {
            log.info("大华ICC-开始语音对讲,request:{}", JSONUtil.toJsonStr(startTalkRequest));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.OPEN_VOICE_INTERCOM.getPath(), startTalkRequest, null, DaHuaIccApiEnum.OPEN_VOICE_INTERCOM.getHttpMethod(), config, StartTalkResponse.class);
            log.info("大华ICC-开始语音对讲,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-开始语音对讲接口异常", e);
        }
        return response;
    }

    public DaHuaCommonIccResponse rtcUnConnect(ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity, StopTalkRequest startTalkRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(thirdPartyPlatformsAuthEntity);
        DaHuaCommonIccResponse response = null;
        try {
            log.info("大华ICC-结束语音对讲,request:{}", JSONUtil.toJsonStr(startTalkRequest));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.STOP_VOICE_INTERCOM.getPath(), startTalkRequest, null, DaHuaIccApiEnum.STOP_VOICE_INTERCOM.getHttpMethod(), config, DaHuaCommonIccResponse.class);
            log.info("大华ICC-结束语音对讲,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-结束语音对讲接口异常", e);
        }
        return response;
    }

    public ChannelMonthRecordStatusResponse getRecordMonthStatus(ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity, ChannelMonthRecordStatusRequest channelMonthRecordStatusRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(thirdPartyPlatformsAuthEntity);
        ChannelMonthRecordStatusResponse response = null;
        try {
            log.info("大华ICC-获取月录像状态,request:{}", JSONUtil.toJsonStr(channelMonthRecordStatusRequest));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.GET_CHANNEL_MONTH_RECORD_STATUS.getPath(), channelMonthRecordStatusRequest, null, DaHuaIccApiEnum.GET_CHANNEL_MONTH_RECORD_STATUS.getHttpMethod(), config, ChannelMonthRecordStatusResponse.class);
            log.info("大华ICC-获取月录像状态,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-获取月录像状态接口异常", e);
        }
        return response;
    }

    public RegularVideoRecordResponse getRecordTimeLineStatus(ThirdPartyPlatformsAuthEntity platformsAuthEntity, RegularVideoRecordRequest regularVideoRecordRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(platformsAuthEntity);
        RegularVideoRecordResponse response = null;
        try {
            log.info("大华ICC-获取录像时间轴状态,request:{}", JSONUtil.toJsonStr(regularVideoRecordRequest));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.QUERY_RECORDS.getPath(), regularVideoRecordRequest, null, DaHuaIccApiEnum.QUERY_RECORDS.getHttpMethod(), config, RegularVideoRecordResponse.class);
            log.info("大华ICC-获取录像时间轴状态,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-获取录像时间轴状态接口异常", e);
        }
        return response;
    }

    public HlsUrlResponse getVideoBackUrl(ThirdPartyPlatformsAuthEntity platformsAuthEntity, HlsPlaybackRequest regularVideoRecordRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(platformsAuthEntity);
        HlsUrlResponse response = null;
        try {
            log.info("大华ICC-获取录像回放URL,request:{}", JSONUtil.toJsonStr(regularVideoRecordRequest));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.VIDEO_STREAM_RECORD.getPath(), regularVideoRecordRequest, null, DaHuaIccApiEnum.VIDEO_STREAM_RECORD.getHttpMethod(), config, HlsUrlResponse.class);
            log.info("大华ICC-获取录像回放URL,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-获取录像回放URL接口异常", e);
        }
        return response;
    }

    public String downloadCloudPlaybackUrl(ThirdPartyPlatformsAuthEntity platformsAuthEntity, VideoDownloadRequest videoDownloadRequest) {
        String channelId = videoDownloadRequest.getChannelId();
        String deviceCode = channelId.substring(0, channelId.indexOf("$"));
        String channelSeq = channelId.substring(channelId.lastIndexOf("$") + 1);
        StringBuilder videoDownloadUrl = new StringBuilder();
        videoDownloadUrl.append(platformsAuthEntity.getHost());
        videoDownloadUrl.append(DaHuaIccApiEnum.VIDEO_DOWNLOAD.getPath());
        videoDownloadUrl.append("?vcuid=" + deviceCode + "%24" + channelSeq);
        videoDownloadUrl.append("&subtype=" + videoDownloadRequest.getSubtype());
        videoDownloadUrl.append("&starttime=" + videoDownloadRequest.getStarttime());
        videoDownloadUrl.append("&endtime=" + videoDownloadRequest.getEndtime());
        videoDownloadUrl.append("&videoType=" + videoDownloadRequest.getVideoType());
        videoDownloadUrl.append("&token=" + videoDownloadRequest.getToken());
        videoDownloadUrl.append("&recordType=" + videoDownloadRequest.getVideoRecordType());
        log.info("大华ICC-获取录像下载的地址:{}", videoDownloadUrl);
        return videoDownloadUrl.toString();
    }

    public OperateDirectResponse ptzOperateDirect(ThirdPartyPlatformsAuthEntity platformsAuthEntity, OperateDirectRequest operateDirectRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(platformsAuthEntity);
        OperateDirectResponse response = null;
        try {
            log.info("大华ICC-云台方向控制,request:{}", JSONUtil.toJsonStr(operateDirectRequest));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.PTZ_CONTROL_DIRECT.getPath(), operateDirectRequest, null, DaHuaIccApiEnum.PTZ_CONTROL_DIRECT.getHttpMethod(), config, OperateDirectResponse.class);
            log.info("大华ICC-云台方向控制,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-云台方向控制接口异常", e);
        }
        return response;
    }

    public OperateCameraResponse ptzOperateCamera(ThirdPartyPlatformsAuthEntity platformsAuthEntity, OperateCameraRequest operateCameraRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(platformsAuthEntity);
        OperateCameraResponse response = null;
        try {
            log.info("大华ICC-云台镜头控制,request:{}", JSONUtil.toJsonStr(operateCameraRequest));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.PTZ_CONTROL_CRMERA.getPath(), operateCameraRequest, null, DaHuaIccApiEnum.PTZ_CONTROL_CRMERA.getHttpMethod(), config, OperateCameraResponse.class);
            log.info("大华ICC-云台镜头控制,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-云台镜头控制接口异常", e);
        }
        return response;
    }

    public GetPresetPointsResponse getPresetPoints(ThirdPartyPlatformsAuthEntity platformsAuthEntity, GetPresetPointsRequest getPresetPointsRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(platformsAuthEntity);
        GetPresetPointsResponse response = null;
        try {
            log.info("大华ICC-获取预置点,request:{}", JSONUtil.toJsonStr(getPresetPointsRequest));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.GET_PRESET_POINTS.getPath(), getPresetPointsRequest, null, DaHuaIccApiEnum.GET_PRESET_POINTS.getHttpMethod(), config, GetPresetPointsResponse.class);
            log.info("大华ICC-获取预置点,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-获取预置点接口异常", e);
        }
        return response;
    }


    public OperatePresetPointResponse ptzOperatePresetPoint(ThirdPartyPlatformsAuthEntity platformsAuthEntity, OperatePresetPointRequest operatePresetPointRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(platformsAuthEntity);
        OperatePresetPointResponse response = null;
        try {
            log.info("大华ICC-云台预置点控制,request:{}", JSONUtil.toJsonStr(operatePresetPointRequest));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.OPERATE_PRESET_POINT.getPath(), operatePresetPointRequest, null, DaHuaIccApiEnum.OPERATE_PRESET_POINT.getHttpMethod(), config, OperatePresetPointResponse.class);
            log.info("大华ICC-云台预置点控制,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-云台预置点控制接口异常", e);
        }
        return response;
    }

    //事件订阅
    public GeneralResponse eventSubscribe(ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity, SubscribeRequest subscribeRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(thirdPartyPlatformsAuthEntity);
        GeneralResponse generalResponse = null;
        try {
            log.info("大华ICC-事件订阅,request:{}", JSONUtil.toJsonStr(subscribeRequest));
            generalResponse = HttpUtils.executeJson(DaHuaIccApiEnum.EVENT_SUBSCRIBE.getPath(), subscribeRequest, null, DaHuaIccApiEnum.EVENT_SUBSCRIBE.getHttpMethod(), config, GeneralResponse.class);
            log.info("大华ICC-事件订阅接口调用成功,generalResponse：{}", JSONUtil.toJsonStr(generalResponse));
        } catch (ClientException e) {
            log.error("大华ICC-事件订阅接口异常", e);
        }
        return generalResponse;
    }

    public QueryRecordResponse queryDoorRecord(ThirdPartyPlatformsAuthEntity platformsAuthEntity, QueryRecordRequest queryRecordRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(platformsAuthEntity);
        QueryRecordResponse response = null;
        try {
            log.info("大华ICC-查询门禁记录,request:{}", JSONUtil.toJsonStr(queryRecordRequest));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.QUERY_DOOR_PAGE_RECORD.getPath(), queryRecordRequest, null, DaHuaIccApiEnum.QUERY_DOOR_PAGE_RECORD.getHttpMethod(), config, QueryRecordResponse.class);
            log.info("大华ICC-查询门禁记录,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-查询门禁记录接口异常", e);
        }
        return response;
    }

    public GeneralResponse closeDoor(ThirdPartyPlatformsAuthEntity platformsAuthEntity, DoorSwitchRequest doorSwitchRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(platformsAuthEntity);
        GeneralResponse response = null;
        try {
            log.info("大华ICC-门禁开关,request:{}", JSONUtil.toJsonStr(doorSwitchRequest));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.CLOSE_DOOR.getPath(), doorSwitchRequest, null, DaHuaIccApiEnum.CLOSE_DOOR.getHttpMethod(), config, GeneralResponse.class);
            log.info("大华ICC-门禁开关,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-门禁开关接口异常", e);
        }
        return response;
    }

    public GeneralResponse openDoor(ThirdPartyPlatformsAuthEntity platformsAuthEntity, DoorSwitchRequest doorSwitchRequest) {
        OauthConfigUserPwdInfo config = getOauthConfig(platformsAuthEntity);
        GeneralResponse response = null;
        try {
            log.info("大华ICC-门禁开关,request:{}", JSONUtil.toJsonStr(doorSwitchRequest));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.OPEN_DOOR.getPath(), doorSwitchRequest, null, DaHuaIccApiEnum.OPEN_DOOR.getHttpMethod(), config, GeneralResponse.class);
            log.info("大华ICC-门禁开关,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-门禁开关接口异常", e);
        }
        return response;
    }


    /**
     * 通用的设备分页查询
     *
     * @param thirdPartyPlatformsAuthEntity
     * @param req
     * @return com.saida.services.open.dahua.resp.DevicePageResponse
     * <AUTHOR>
     * @since 2025/06/11 10:08:37
     */
    public DevicePageResponse getComonnectDevicePage(ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity, DevicePageRequest req) {
            DevicePageResponse currentResponse = getDevicePage(thirdPartyPlatformsAuthEntity, req);
            if (currentResponse.isSuccess() && currentResponse.getData() != null) {
                Integer totalPages = currentResponse.getData().getTotalPage();
                if (totalPages != null && totalPages > req.getPageNum()) {
                    // 递归获取后续页
                    req.setPageNum(req.getPageNum() + 1);
                    DevicePageResponse nextResponse = getComonnectDevicePage(thirdPartyPlatformsAuthEntity, req);
                    // 合并数据
                    if (nextResponse.isSuccess() && nextResponse.getData() != null) {
                        currentResponse.getData().getPageData().addAll(nextResponse.getData().getPageData());
                        currentResponse.getData().setTotalRows(currentResponse.getData().getTotalRows() + nextResponse.getData().getTotalRows());
                    }
                }
            }
            return currentResponse;

    }

    public DoorChannelStatusResponse doorChannelStatus(ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity, DoorSwitchStatusRequest req) {
        DoorChannelStatusResponse response = null;
        try {
            log.info("大华ICC-门禁通道状态,request:{}", JSONUtil.toJsonStr(req));
            response = HttpUtils.executeJson(DaHuaIccApiEnum.DOOR_CHANNEL_STATUS.getPath(), req, null, DaHuaIccApiEnum.DOOR_CHANNEL_STATUS.getHttpMethod(), getOauthConfig(thirdPartyPlatformsAuthEntity), DoorChannelStatusResponse.class);
            log.info("大华ICC-门禁通道状态,response:{}", JSONUtil.toJsonStr(response));
        } catch (ClientException e) {
            log.error("大华ICC-门禁通道状态接口异常", e);
        }
        return response;
    }
}
