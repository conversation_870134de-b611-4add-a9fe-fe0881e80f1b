package com.saida.services.system.thread;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.tools.LocalUrlUtil;
import com.saida.services.common.tools.OkHttpUtil;
import com.saida.services.feign.srv.system.IFeignSrvSystemApiController;
import com.saida.services.open.dto.OpenNoticeThirdPartyAppMsgDTO;
import com.saida.services.open.entity.DeviceInfoEntity;
import com.saida.services.system.sys.entity.ClientAppEntity;
import com.saida.services.system.sys.service.ClientAppService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
@Async(ThreadPoolConfig.TASK_EXECUTOR_BEAN_NAME)
public class MsgPushThread {

    @Resource
    private ClientAppService clientAppService;
    @Resource
    private IFeignSrvSystemApiController feignSrvSystemApiController;

    public void notifyTheApp(OpenNoticeThirdPartyAppMsgDTO openNoticeThirdPartyAppMsgDTO, String deviceSn) {
        // 推融合
        try {
            feignSrvSystemApiController.receiveOpenMsg(openNoticeThirdPartyAppMsgDTO);
            log.info("V-LINKER能力开放平台.推送第三方消息..调用Feign接口...req={}", JSON.toJSON(openNoticeThirdPartyAppMsgDTO));
        } catch (Exception e) {
            log.error("V-LINKER能力开放平台.推送第三方消息，调用Feign接口失败...data={}, msg={}", JSON.toJSON(openNoticeThirdPartyAppMsgDTO), e.getMessage());
        }

        // 推设备绑定的应用和内部应用
        try {
            this.notifyTheHttp(openNoticeThirdPartyAppMsgDTO, deviceSn);
            log.info("V-LINKER能力开放平台.推送第三方消息..调用Feign接口...req={}", JSON.toJSON(openNoticeThirdPartyAppMsgDTO));
        } catch (Exception e) {
            log.error("V-LINKER能力开放平台.推送第三方消息，调用Feign接口失败...data={}, msg={}", JSON.toJSON(openNoticeThirdPartyAppMsgDTO), e.getMessage());
        }
    }

    public void notifyTheHttp(OpenNoticeThirdPartyAppMsgDTO openNoticeThirdPartyAppMsgDTO, String deviceSn) {
        // 创建DeviceInfoEntity对象，用于查询与设备关联的应用信息
        DeviceInfoEntity deviceInfoEntity = new DeviceInfoEntity();
        // 设置设备序列号
        deviceInfoEntity.setTripartiteSn(deviceSn);
        // 根据设备信息查询关联的应用列表
        List<ClientAppEntity> clientAppEntityList = clientAppService.getListByDevice(deviceInfoEntity);

        List<ClientAppEntity> internalList = clientAppService.list(new LambdaQueryWrapper<ClientAppEntity>()
                .eq(ClientAppEntity::getInternal, 1)
        );

        // 通过id 去重两个list
        // 使用Stream合并并去重
        List<ClientAppEntity> mergedList = new ArrayList<>(Stream.concat(clientAppEntityList.stream(), internalList.stream())
                .collect(Collectors.toMap(ClientAppEntity::getId, entity -> entity,
                        (e1, e2) -> e1))
                .values());

        // 遍历应用列表，为每个应用推送消息
        mergedList.forEach(clientAppEntity -> {
            try {
                String noticeUrl = clientAppEntity.getVideoStatusUrlV1();
                // 检查协议，确保是http(s)
                if (!LocalUrlUtil.validUrl(noticeUrl)) {
                    log.info("V-LINKER能力开放平台.推送第三方消息..通知地址协议不是http或者https.,.pushUrl={}, req={}", noticeUrl, JSON.toJSON(openNoticeThirdPartyAppMsgDTO));
                    return;
                }
                // 初始化响应信息
                // 通知对应应用
                String resp = null;
                int httpCode = 0;
                try {
                    // 使用TypeReference来指定JSON反序列化后的类型，以便正确处理返回的数据。
                    TypeReference<Map<String, Object>> typeReference = new TypeReference<>() {
                    };
                    // 执行HTTP POST请求，推送告警数据。这里首先将baseAlarmDto转换为JSON字符串，然后反序列化为Map类型进行推送。
                    Map<String, Object> map = JSON.parseObject(JSON.toJSONString(openNoticeThirdPartyAppMsgDTO), typeReference);
                    // 创建请求头信息
                    Map<String, String> headerMap = new HashMap<>();
                    // 设置应用密钥
                    headerMap.put("appKey", clientAppEntity.getAppKey());
                    // 发送POST请求，推送加密后的消息数据，并保存响应信息
                    try (Response response = OkHttpUtil.doPostJson(noticeUrl, headerMap, map)) {
                        httpCode = response.code();
                        // 判断是否成功
                        if (response.isSuccessful()) {
                            // 检查是否存在响应体（避免读取阻塞）
                            if (response.body() != null && response.body().contentLength() != 0) {
                                resp = response.body().string();  // 存在响应数据
                            } else {
                                resp = "";  // 无响应数据，避免阻塞
                            }
                        } else {
                            log.error("V-LINKER能力开放平台.推送第三方消息..错误...pushUrl={}, req={}, httpCode={}, resp={}", noticeUrl, JSON.toJSON(openNoticeThirdPartyAppMsgDTO), httpCode, resp);
                            return;
                        }
                    }
                    // 记录推送成功的日志
                    log.info("V-LINKER能力开放平台.推送第三方消息..成功...pushUrl={}, req={}, httpCode={}, resp={}", noticeUrl, JSON.toJSON(openNoticeThirdPartyAppMsgDTO), httpCode, resp);
                } catch (Exception e) {
                    // 记录推送失败的日志
                    log.error("V-LINKER能力开放平台.推送第三方消息..错误...pushUrl={}, httpCode={}, req={}, resp={}, msg={}", noticeUrl, httpCode, JSON.toJSON(openNoticeThirdPartyAppMsgDTO), resp, e.getMessage(), e);
                }
            } catch (Exception e) {
                log.info("V-LINKER能力开放平台.推送第三方消息..错误...msg={}", e.getMessage(), e);
            }
        });
    }
}