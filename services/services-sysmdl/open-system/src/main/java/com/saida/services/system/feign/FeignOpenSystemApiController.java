package com.saida.services.system.feign;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dahuatech.hutool.json.JSONUtil;
import com.dahuatech.icc.oauth.model.v202010.GeneralResponse;
import com.saida.services.algorithm.dto.InnerAlgorithmSubscribeDto;
import com.saida.services.algorithm.dto.InnerAlgorithmUnsubscribeDto;
import com.saida.services.algorithm.dto.InnerDeviceListDto;
import com.saida.services.algorithm.dto.PushAlarmDto;
import com.saida.services.algorithm.req.VlinkerAlgServerTaskSaveOrUpdateReq;
import com.saida.services.algorithm.req.VlinkerAlgorithmEditBoxFaceReq;
import com.saida.services.algorithm.req.VlinkerAlgorithmGetAlgBoxReq;
import com.saida.services.algorithm.resp.VlinkerAlgorithmGetAlgBoxResp;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.tools.StringUtil;
import com.saida.services.constant.ThirdPartyPlatformsBizComponent;
import com.saida.services.converge.dto.FeignUpdateDeviceOnlineDto;
import com.saida.services.converge.entity.dto.ConvDeviceAndChannelDTO;
import com.saida.services.converge.entity.system.ConvSysOrgEntity;
import com.saida.services.converge.qxNode.req.AddCarInfoGateReq;
import com.saida.services.converge.qxNode.req.DeleteCarInfoGateReq;
import com.saida.services.converge.vo.DeviceCapacityVo;
import com.saida.services.converge.vo.DownloadLocalPlaybackVo;
import com.saida.services.converge.vo.GbControlLocalPlaybackVo;
import com.saida.services.converge.vo.GbStopDownloadLocalPlaybackVo;
import com.saida.services.deviceApi.req.CommonGetHumanoidMarkersReq;
import com.saida.services.deviceApi.req.CommonGetSoundAndLightShockReq;
import com.saida.services.deviceApi.resp.*;
import com.saida.services.feign.converge.system.IFeignConvergeSystemApiController;
import com.saida.services.feign.open.system.IFeignOpenSystemApiController;
import com.saida.services.iot.dto.DataPushDto;
import com.saida.services.open.biz.resp.*;
import com.saida.services.open.dahua.req.DoorSwitchRequest;
import com.saida.services.open.dahua.req.DoorSwitchStatusRequest;
import com.saida.services.open.dahua.req.QueryRecordRequest;
import com.saida.services.open.dahua.resp.DevicePageResponse;
import com.saida.services.open.dahua.resp.DoorChannelStatusResponse;
import com.saida.services.open.dahua.resp.QueryRecordResponse;
import com.saida.services.open.deviceApi.req.*;
import com.saida.services.open.dto.*;
import com.saida.services.open.dto.rtc.RtcConnectDto;
import com.saida.services.open.entity.AlgorithmManageEntity;
import com.saida.services.open.entity.DeviceInfoEntity;
import com.saida.services.open.entity.IotDataSubscribeEntity;
import com.saida.services.open.entity.ThirdPartyPlatformsAuthEntity;
import com.saida.services.open.enums.OpenNoticeThirdPartyAppEnum;
import com.saida.services.open.enums.OpenThirdPartyPlatformsTypeEnum;
import com.saida.services.open.req.AddPeopleByHumanGateReq;
import com.saida.services.open.req.DeletePeopleByHumanGateReq;
import com.saida.services.open.resp.api.DeviceBaseInfoResp;
import com.saida.services.open.vo.IotDataSubscribeListByDeviceIdVo;
import com.saida.services.srv.entity.VisualDeviceEntity;
import com.saida.services.system.alram.service.AlarmPushService;
import com.saida.services.system.api.service.ApiAlgorithmService;
import com.saida.services.system.api.service.FeignApiVideoService;
import com.saida.services.system.biz.service.dahuaIcc.DaHuaRedisConstant;
import com.saida.services.system.biz.service.dahuaIcc.biz.DaHuaIccBiz;
import com.saida.services.system.biz.service.dahuaIcc.model.brm.device.DevicePageRequest;
import com.saida.services.system.inner.service.InnerAlgorithmService;
import com.saida.services.system.inner.service.InnerDeviceService;
import com.saida.services.system.iot.service.IotDataSubscribeService;
import com.saida.services.system.iot.service.IotDeviceOnlineSubscribeService;
import com.saida.services.system.sys.entity.ClientAppEntity;
import com.saida.services.system.sys.service.ClientAppService;
import com.saida.services.system.sys.service.DeviceInfoService;
import com.saida.services.system.sys.service.ThirdPartyPlatformsAuthService;
import com.saida.services.system.sys.service.impl.FeignOpenSystemApiService;
import com.saida.services.system.thread.IotDataHandleThread;
import com.saida.services.system.thread.IotDataPushThread;
import com.saida.services.system.thread.MsgPushThread;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class FeignOpenSystemApiController implements IFeignOpenSystemApiController {

    @Resource
    private FeignApiVideoService feignApiVideoService;
    @Resource
    private DeviceInfoService deviceInfoService;
    @Resource
    private MsgPushThread msgPushThread;
    @Resource
    private InnerAlgorithmService innerAlgorithmService;
    @Resource
    private InnerDeviceService innerDeviceService;
    @Resource
    private AlarmPushService alarmPushService;
    @Resource
    private IotDataSubscribeService iotDataSubscribeService;
    @Resource
    private IotDeviceOnlineSubscribeService iotDeviceOnlineSubscribeService;
    @Resource
    private IotDataHandleThread iotDataHandleThread;
    @Resource
    private ClientAppService clientAppService;
    @Resource
    private IotDataPushThread iotDataPushThread;
    @Resource
    private FeignOpenSystemApiService feignOpenSystemApiService;
    @Resource
    private IFeignConvergeSystemApiController iFeignConvergeSystemApiController;

    @Resource
    private ApiAlgorithmService apiAlgorithmService;
    @Resource
    private DaHuaIccBiz daHuaIccBiz;
    @Resource
    private ThirdPartyPlatformsAuthService thirdPartyPlatformsAuthService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Lazy
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public DtoResult<DeviceBaseInfoResp> getDeviceBasicInfo(BaseVideoDto baseVideoDto) {
        return feignOpenSystemApiService.getDeviceBasicInfo(baseVideoDto);
    }

    @Override
    public DtoResult<ThirdPartyPlatformsVideoLiveUrlResp> getVideoLiveUrl(VideoLiveUrlDto videoLiveUrlDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(videoLiveUrlDto.getDeviceId(), videoLiveUrlDto.getDeviceSN(), videoLiveUrlDto.getChannelId());
        return feignApiVideoService.getVideoLiveUrl(videoLiveUrlDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<Void> stopLive(VideoLiveUrlDto videoLiveUrlDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(videoLiveUrlDto.getDeviceId(), videoLiveUrlDto.getDeviceSN(), videoLiveUrlDto.getChannelId());
        return feignApiVideoService.stopLive(videoLiveUrlDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<DeviceInfoEntity> getDeviceInfo(BaseVideoDto dto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(dto.getDeviceId(), null, null);
        return DtoResult.ok(deviceInfoEntity);
    }

    @Override
    public DtoResult<List<DeviceInfoEntity>> getAllDeviceList() {
        return feignOpenSystemApiService.getAllDeviceList();
    }

    /**
     * 远程控制摄像头的PTZ（Pan/Tilt/Zoom）功能。
     * 通过向指定的设备序列号（deviceSN）和通道号（channelId）发送操作（action）和命令（command），
     * 来实现对摄像头的移动、缩放等操作。速度（speed）用于控制操作的快慢。
     *
     * @return 返回操作结果，封装在DtoResult<Void>中。
     */
    @Override
    @PostMapping("/feign/open-system/ptzControl")
    public DtoResult<Void> ptzControl(PtzControlDto ptzControlDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(ptzControlDto.getDeviceId(), ptzControlDto.getDeviceSN(), ptzControlDto.getChannelId());
        return feignApiVideoService.ptzControl(ptzControlDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<List<ThirdPartyPlatformsPrePointListResp>> getPrePointList(PrePointListDto prePointListDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(prePointListDto.getDeviceId(), prePointListDto.getDeviceCode(), prePointListDto.getChannelCode());
        return feignApiVideoService.getPrePointList(prePointListDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<Void> setPrePoint(SetPrePointDto setPrePointDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(setPrePointDto.getDeviceId(), setPrePointDto.getDeviceCode(), setPrePointDto.getChannelCode());
        return feignApiVideoService.setPrePoint(setPrePointDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<Void> deletePrePoint(DelPrePointDto delPrePointDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(delPrePointDto.getDeviceId(), delPrePointDto.getDeviceCode(), delPrePointDto.getChannelCode());
        return feignApiVideoService.deletePrePoint(delPrePointDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<Void> jumpPrePoint(JumpPrePointDto jumpPrePointDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(jumpPrePointDto.getDeviceId(), jumpPrePointDto.getDeviceCode(), jumpPrePointDto.getChannelCode());
        return feignApiVideoService.jumpPrePoint(jumpPrePointDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<ThirdPartyPlatformsRtcConnectResp> rtcConnect(RtcConnectDto dto) {
        return feignOpenSystemApiService.rtcConnect(dto);
    }

    @Override
    public DtoResult<Void> rtcUnConnect(RtcConnectDto dto) {
        return feignOpenSystemApiService.rtcUnConnect(dto);
    }

    @Override
    public DtoResult<ThirdPartyPlatformsBaseGetFillLightStatusResp> setStatusLight(StatusLightDto dto) {
        return feignOpenSystemApiService.setStatusLight(dto);
    }

    @Override
    public DtoResult<CommonSetStatusLightResp> getStatusLight(StatusLightDto dto) {
        return feignOpenSystemApiService.getStatusLight(dto);
    }

    @Override
    public DtoResult<ThirdPartyPlatformsFlipVideoResp> flipVideo(FlipVideoDto dto) {
        return feignOpenSystemApiService.flipVideo(dto);
    }


    @Override
    public DtoResult<ThirdPartyPlatformsFlipVideoResp> getFlipVideo(FlipVideoDto dto) {
        return feignOpenSystemApiService.getFlipVideo(dto);
    }

    @Override
    public DtoResult<ThirdPartyPlatformsSdCardInfoResp> getSdCardInfo(SdCardInfoDto dto) {
        return feignOpenSystemApiService.getSdCardInfo(dto);
    }

    @Override
    public DtoResult<Void> sdCardInfoFormat(SdCardInfoDto dto) {
        return feignOpenSystemApiService.sdCardInfoFormat(dto);
    }

    @Override
    public DtoResult<CommonGetSideAlgorithmResp> getSideAlgorithmInfo(OpenGetSideAlgorithmReq dto) {
        return feignOpenSystemApiService.getSideAlgorithmInfo(dto);
    }

    @Override
    public DtoResult<CommonSetSideAlgorithmResp> setSideAlgorithmInfo(OpenSetSideAlgorithmReq dto) {
        return feignOpenSystemApiService.setSideAlgorithmInfo(dto);
    }

    @Override
    public DtoResult<CommonGetNowDeviceVersionResp> getNowDeviceVersionInfo(OpenDeviceReq deviceReq) {
        return feignOpenSystemApiService.getNowDeviceVersionInfo(deviceReq);
    }

    @Override
    public DtoResult<CommonGetDeviceVersionListResp> getNowDeviceVersionListInfo(OpenDevicePageReq openDevicePageReq) {
        return feignOpenSystemApiService.getNowDeviceVersionListInfo(openDevicePageReq);
    }

    @Override
    public DtoResult<CommonGetNowDeviceVersionResp> deviceUpgrade(OpenDeviceReq deviceReq) {
        return feignOpenSystemApiService.deviceUpgrade(deviceReq);
    }

    @Override
    public DtoResult<Void> deviceRestart(OpenDeviceReq deviceReq) {
        return feignOpenSystemApiService.deviceRestart(deviceReq);
    }

    @Override
    public DtoResult<Void> deviceReset(OpenDeviceReq deviceReq) {
        return feignOpenSystemApiService.deviceReset(deviceReq);
    }

    @Override
    public DtoResult<CommonGetOsdInfoResp> getOsdInfo(OpenDeviceReq deviceReq) {
        return feignOpenSystemApiService.getOsdInfo(deviceReq);
    }

    @Override
    public DtoResult<CommonCallDeviceByVideoResp> callDeviceByVideo(OpenCallDeviceByVideoReq deviceReq) {
        return feignOpenSystemApiService.callDeviceByVideo(deviceReq);
    }

    @Override
    public DtoResult<CommonGetOsdInfoResp> setOsdInfo(OpenSetOsdInfoReq deviceReq) {
        return feignOpenSystemApiService.setOsdInfo(deviceReq);
    }

    @Override
    public DtoResult<CommonSetDormancyResp> setDormancyParameter(OpenSetDormancyParameterReq deviceReq) {
        return feignOpenSystemApiService.setDormancyParameter(deviceReq);
    }

    @Override
    public DtoResult<CommonGetDormancyResp> getDormancyParameter(OpenDeviceReq deviceReq) {
        return feignOpenSystemApiService.getDormancyParameter(deviceReq);
    }

    /**
     * 通过Feign调用视频服务，获取指定设备和通道的快照。
     */
    @Override
    public DtoResult<ThirdPartyPlatformsSnapshotResp> snapshot(SnapshotDto snapshotDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(snapshotDto.getDeviceId(), snapshotDto.getDeviceSN(), snapshotDto.getChannelId());
        return feignApiVideoService.snapshot(snapshotDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<List<ThirdPartyPlatformsRecordTimeLineResp>> getRecordTimeLine(RecordTimeLineDto recordTimeLineDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(recordTimeLineDto.getDeviceId(), null, null);
        return feignApiVideoService.getRecordTimeLine(recordTimeLineDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<ThirdPartyPlatformsVideoBackUrlResp> getVideoBackUrl(VideoBackUrlDto videoBackUrlDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(videoBackUrlDto.getDeviceId(), null, null);
        return feignApiVideoService.getVideoBackUrl(videoBackUrlDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<ThirdPartyPlatformsVideoBackUrlResp> downloadDaHuaPlaybackUrl(VideoBackUrlDto videoBackUrlDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(videoBackUrlDto.getDeviceId(), null, null);
        return feignApiVideoService.downloadDaHuaPlaybackUrl(videoBackUrlDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<ThirdPartyPlatformsVideoBackUrlResp> downloadCloudPlaybackUrl(VideoBackUrlDto videoBackUrlDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(videoBackUrlDto.getDeviceId(), null, null);
        return feignApiVideoService.getVideoBackUrl(videoBackUrlDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<GbControlLocalPlaybackVo> controlPlayback(ControlPlaybackDto controlPlaybackDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(controlPlaybackDto.getDeviceId(), null, null);
        return feignApiVideoService.controlPlayback(controlPlaybackDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<Void> stopPlayback(StopPlaybackDto stopPlaybackDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(stopPlaybackDto.getDeviceId(), null, null);
        return feignApiVideoService.stopPlayback(stopPlaybackDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<DownloadLocalPlaybackVo> downloadPlayback(DownloadPlaybackDto downloadPlaybackDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(downloadPlaybackDto.getDeviceId(), null, null);
        return feignApiVideoService.downloadPlayback(downloadPlaybackDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<GbStopDownloadLocalPlaybackVo> stopDownloadPlayback(StopDownloadPlaybackDto stopDownloadPlaybackDto) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(stopDownloadPlaybackDto.getDeviceId(), null, null);
        return feignApiVideoService.stopDownloadPlayback(stopDownloadPlaybackDto, deviceInfoEntity);
    }

    @Override
    public DtoResult<Void> addOrUpdatePeople(AddPeopleByHumanGateReq addPeopleByHumanGateReq) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(addPeopleByHumanGateReq.getDeviceId(), null, null);
        return feignApiVideoService.addOrUpdatePeople(addPeopleByHumanGateReq, deviceInfoEntity);
    }

    @Override
    public DtoResult<Void> deletePeople(DeletePeopleByHumanGateReq deletePeopleByHumanGateReq) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(deletePeopleByHumanGateReq.getDeviceId(), null, null);
        return feignApiVideoService.deletePeople(deletePeopleByHumanGateReq, deviceInfoEntity);
    }

    @Override
    public DtoResult<Void> addOrUpdateCarInfo(AddCarInfoGateReq addCarInfoGateReq) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(addCarInfoGateReq.getDeviceId(), null, null);
        return feignApiVideoService.addOrUpdateCarInfo(addCarInfoGateReq, deviceInfoEntity);
    }

    @Override
    public DtoResult<Void> deleteCarInfo(DeleteCarInfoGateReq deleteCarInfoGateReq) {
        DeviceInfoEntity deviceInfoEntity = feignOpenSystemApiService.getDeviceInfo(deleteCarInfoGateReq.getDeviceId(), null, null);
        return feignApiVideoService.deleteCarInfo(deleteCarInfoGateReq, deviceInfoEntity);
    }

    /**
     * 根据设备序列号和在线状态更新设备的在线状态，并推送消息给第三方应用。
     *
     * @param dto 包含设备序列号和在线状态的数据传输对象。
     * @return 返回操作结果的數據轉換對象，通常表示操作成功。
     */
    @Override
    public DtoResult<Void> updateDeviceOnline(FeignUpdateDeviceOnlineDto dto) {
        // 提取设备序列号和在线状态
        String deviceSn = dto.getDeviceSn();
        String channelId = dto.getChannelId();
        Integer online = dto.getOnline();

        // 根据设备序列号查询设备信息
        List<DeviceInfoEntity> deviceInfoEntityList = deviceInfoService.list(
                new LambdaQueryWrapper<DeviceInfoEntity>()
                        .eq(DeviceInfoEntity::getTripartiteSn, deviceSn)
                        .eq(StringUtil.isNotEmpty(channelId), DeviceInfoEntity::getChannelId, channelId));

        // 如果设备信息为空，记录错误日志并返回成功结果
        if (CollectionUtil.isEmpty(deviceInfoEntityList)) {
            log.error("V-LINKER能力开放平台.推送第三方消息，设备ID为空..程序结束...deviceSN={}", deviceSn);
            return DtoResult.ok();
        }

        Set<String> deviceIdSet = deviceInfoEntityList.stream().map(DeviceInfoEntity::getId).collect(Collectors.toSet());
        // 更新数据库状态：0-离线；1-在线
        deviceInfoService.update(new LambdaUpdateWrapper<DeviceInfoEntity>()
                .in(DeviceInfoEntity::getId, deviceIdSet)
                .set(DeviceInfoEntity::getOnlineStatus, online));

        // 创建在线状态通知消息
        OpenNoticeOnLineMsgDTO openNoticeOnLineMsgDTO = new OpenNoticeOnLineMsgDTO();
        openNoticeOnLineMsgDTO.setDeviceIdSet(deviceIdSet);
        openNoticeOnLineMsgDTO.setOnline(online);

        // 创建第三方应用通知对象
        OpenNoticeThirdPartyAppMsgDTO openNoticeThirdPartyAppMsgDTO = new OpenNoticeThirdPartyAppMsgDTO();
        openNoticeThirdPartyAppMsgDTO.setMsgId(IdUtil.fastSimpleUUID());
        openNoticeThirdPartyAppMsgDTO.setMsgType(OpenNoticeThirdPartyAppEnum.ONE);
        openNoticeThirdPartyAppMsgDTO.setData(JSON.toJSON(openNoticeOnLineMsgDTO));

        // 向应用推送消息
        msgPushThread.notifyTheApp(openNoticeThirdPartyAppMsgDTO, deviceSn);

        // 返回操作成功结果
        return DtoResult.ok();
    }

    @Override
    public DtoResult<List<AlgorithmManageEntity>> getAlgorithmList() {
        return innerAlgorithmService.getAlgorithmList();
    }

    @Override
    public DtoResult<List<AlgorithmManageEntity>> getToCAlgorithmList() {
        return innerAlgorithmService.getToCAlgorithmList();
    }

    @Override
    public DtoResult<Void> subscribe(InnerAlgorithmSubscribeDto dto) {
        return innerAlgorithmService.subscribe(dto);
    }

    @Override
    public DtoResult<Void> unsubscribe(InnerAlgorithmUnsubscribeDto dto) {
        return innerAlgorithmService.unsubscribe(dto);
    }

    @Override
    public DtoResult<List<DeviceInfoEntity>> getDeviceList(InnerDeviceListDto dto) {
        return innerDeviceService.getDeviceListByAppId(dto);
    }

    @Override
    public DtoResult<Void> acceptDataFromVlinkAlgorithm(PushAlarmDto pushAlarmDto) {
        return alarmPushService.receiveData(pushAlarmDto);
    }

    /**
     * 好像没有人用这个  不过我记得有个噪音的？
     */
    @Deprecated
    @Override
    public DtoResult<Void> acceptDataFromVlinkIot(DataPushDto dto) {
        String imei = dto.getImei();
        if (StringUtil.isEmpty(imei)) {
            log.info("V-LINKER能力开放平台.接收物联网设备数据推送，imei为空..程序结束...data={}", JSON.toJSON(dto));
            return DtoResult.error("imei不能为空！");
        }
        DeviceInfoEntity deviceInfoEntity = deviceInfoService.getOne(new LambdaQueryWrapper<DeviceInfoEntity>()
                .eq(DeviceInfoEntity::getTripartiteSn, imei)
                .eq(DeviceInfoEntity::getType, OpenThirdPartyPlatformsTypeEnum.IOT.getDicId())
        );
        if (null == deviceInfoEntity) {
            log.info("V-LINKER能力开放平台.接收物联网设备数据推送，设备不存在..程序结束...data={}", JSON.toJSON(dto));
            return DtoResult.error("设备不存在！");
        }
        // 1.推送融合平台 v1 还有必要推送么
        iotDataPushThread.pushIotDataToSrv(deviceInfoEntity.getId(), dto);

        // 2.推送订阅者
        IotDataSubscribeEntity iotDataSubscribeEntity = new IotDataSubscribeListByDeviceIdVo();
        iotDataSubscribeEntity.setDeviceId(deviceInfoEntity.getId());
        List<IotDataSubscribeListByDeviceIdVo> iotDataSubscribeListByDeviceIdVoList = iotDataSubscribeService.getIotDataSubscribeListByDeviceId(iotDataSubscribeEntity);
        iotDataHandleThread.handleIotData(iotDataSubscribeListByDeviceIdVoList, dto);
        List<ClientAppEntity> list = clientAppService.list(new LambdaQueryWrapper<ClientAppEntity>()
                .eq(ClientAppEntity::getInternal, 1)
                .isNotNull(ClientAppEntity::getIotStatusUrlV1));
        List<IotDataSubscribeListByDeviceIdVo> internal = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (ClientAppEntity clientAppEntity : list) {
                IotDataSubscribeListByDeviceIdVo iotDataSubscribeListByDeviceIdVo = new IotDataSubscribeListByDeviceIdVo();
                iotDataSubscribeListByDeviceIdVo.setDeviceId(deviceInfoEntity.getId());
                iotDataSubscribeListByDeviceIdVo.setAppId(clientAppEntity.getId());
                iotDataSubscribeListByDeviceIdVo.setIotStatusUrlV1(clientAppEntity.getIotStatusUrlV1());
                internal.add(iotDataSubscribeListByDeviceIdVo);
            }
        }
        iotDataHandleThread.handleIotData(internal, dto);
        return DtoResult.ok();
    }

    @Override
    public DtoResult<Void> acceptDataFromVlinkIotV2(DataPushDto dto) {
        log.info("V-LINKER能力开放平台.接收物联网设备数据推送V2，data={}", JSON.toJSON(dto));
        String imei = dto.getImei();
        if (StringUtil.isEmpty(imei)) {
            log.info("V-LINKER能力开放平台.接收物联网设备数据推送V2，imei为空..程序结束...data={}", JSON.toJSON(dto));
            return DtoResult.error("imei不能为空！");
        }
        DeviceInfoEntity deviceInfoEntity = deviceInfoService.getOne(new LambdaQueryWrapper<DeviceInfoEntity>()
                .eq(DeviceInfoEntity::getTripartiteSn, imei)
                .eq(DeviceInfoEntity::getType, OpenThirdPartyPlatformsTypeEnum.IOT.getDicId())
        );
        if (null == deviceInfoEntity) {
            log.info("V-LINKER能力开放平台.接收物联网设备数据推送V2，设备不存在..程序结束...data={}", JSON.toJSON(dto));
            return DtoResult.error("设备不存在！");
        }

        // 1.推送物联消息订阅者
        IotDataSubscribeEntity iotDataSubscribeEntity = new IotDataSubscribeListByDeviceIdVo();
        iotDataSubscribeEntity.setDeviceId(deviceInfoEntity.getId());
        List<IotDataSubscribeListByDeviceIdVo> iotDeviceOnlineSubscribeList = iotDeviceOnlineSubscribeService.getIotDeviceOnlineSubscribeListByDeviceId(iotDataSubscribeEntity);
        List<IotDataSubscribeListByDeviceIdVo> iotDataSubscribeListByDeviceIdVoList = iotDataSubscribeService.getIotDataSubscribeListByDeviceId(iotDataSubscribeEntity);
        iotDataHandleThread.handleIotDataV2(iotDataSubscribeListByDeviceIdVoList, dto);

        List<ClientAppEntity> list = clientAppService.list(new LambdaQueryWrapper<ClientAppEntity>()
                .eq(ClientAppEntity::getInternal, 1)
                .isNotNull(ClientAppEntity::getIotStatusUrlV1));
        List<IotDataSubscribeListByDeviceIdVo> internal = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            for (ClientAppEntity clientAppEntity : list) {
                if (StringUtil.isNotBlank(clientAppEntity.getIotStatusUrlV1()) && iotDataSubscribeListByDeviceIdVoList.stream().anyMatch(e -> e.getIotStatusUrlV1().equals(clientAppEntity.getIotStatusUrlV1()))) {
                    continue;
                }
                IotDataSubscribeListByDeviceIdVo iotDataSubscribeListByDeviceIdVo = new IotDataSubscribeListByDeviceIdVo();
                iotDataSubscribeListByDeviceIdVo.setDeviceId(deviceInfoEntity.getId());
                iotDataSubscribeListByDeviceIdVo.setAppId(clientAppEntity.getId());
                iotDataSubscribeListByDeviceIdVo.setIotStatusUrlV1(clientAppEntity.getIotStatusUrlV1());
                internal.add(iotDataSubscribeListByDeviceIdVo);
            }
        }
        // 1.推送融合平台
        iotDataPushThread.pushIotDataToSrv(deviceInfoEntity.getId(), dto);
        iotDataHandleThread.handleIotDataV2(internal, dto);

        // 2.维护设备在线数据
        iotDataHandleThread.handleIotOnLineData(deviceInfoEntity.getId(), iotDeviceOnlineSubscribeList, dto);
        return DtoResult.ok();
    }

    @Override
    public DtoResult<Void> unSubscribeIot(String appKey, Set<String> deviceIdSet) {
        if (CollectionUtil.isEmpty(deviceIdSet) || null == appKey) {
            return DtoResult.ok();
        }
        ClientAppEntity clientAppEntity = clientAppService.getOne(new LambdaQueryWrapper<ClientAppEntity>()
                .eq(ClientAppEntity::getAppKey, appKey));
        if (null == clientAppEntity) {
            return DtoResult.ok();
        }
        iotDataSubscribeService.remove(new LambdaUpdateWrapper<IotDataSubscribeEntity>()
                .eq(IotDataSubscribeEntity::getAppId, clientAppEntity.getId())
                .in(IotDataSubscribeEntity::getDeviceId, deviceIdSet)
        );
        return DtoResult.ok();
    }

    @Override
    public DtoResult<Void> editAlgorithmSubscribe(List<EditAlgorithmSubscribeFeignDto> dataList) {
        return feignOpenSystemApiService.editAlgorithmSubscribe(dataList);
    }

    @Override
    public DtoResult<Void> syncOrgFromConv(ConvSysOrgEntity convSysOrgEntity) {
        return feignOpenSystemApiService.syncOrgFromConv(convSysOrgEntity);
    }

    @Override
    public DtoResult<Void> syncDeviceByOrgFromConv(SyncDeviceByOrgFromConvDto dto) {
        return feignOpenSystemApiService.syncDeviceByOrgFromConv(dto);
    }

    @Override
    public DtoResult<Void> syncAllDeviceChannelDataFromConv() {
        return feignOpenSystemApiService.syncAllDeviceChannelDataFromConv();
    }

    @Override
    public DtoResult<Void> syncIncreDeviceChannelDataFromConv(SyncIncreDeviceChannelDataFromConvDto dto) {
        try {
            return feignOpenSystemApiService.syncIncreDeviceChannelDataFromConv(dto);
        } catch (Exception e) {
            log.error("同步增量设备通道数据失败...msg={}", e.getMessage(), e);
            return DtoResult.error("同步增量设备通道数据失败!");
        }
    }

    @Override
    public DtoResult<DeviceCapacityVo> getDeviceCapacity(GetDeviceCapacityDto dto) {
        return feignOpenSystemApiService.getDeviceCapacity(dto.getDeviceId());
    }

    @Override
    public DtoResult<Long> distributeAlgorithmTasks(DistributeAlgorithmTasksDto dto) {
        return feignOpenSystemApiService.distributeAlgorithmTasks(dto);
    }

    @Override
    public DtoResult<Void> deliverTheCloudStoragePolicy(DeliverTheCloudStoragePolicyDto dto) {
        return feignOpenSystemApiService.deliverTheCloudStoragePolicy(dto);
    }

    @Override
    public DtoResult<List<DeviceInfoEntity>> syncDeviceAndChannelName(List<VisualDeviceEntity> visualDeviceEntityList) {
        return feignOpenSystemApiService.syncDeviceAndChannelName(visualDeviceEntityList);
    }

    @Override
    public DtoResult<java.util.Map<String, Object>> getAllAlarmParameters() {
        return iFeignConvergeSystemApiController.getAllAlarmParameters();
    }

    @Override
    public ConvDeviceAndChannelDTO getDeviceChannelList(DeviceInfoEntity deviceInfoEntity) {
        return feignOpenSystemApiService.getDeviceChannelList(deviceInfoEntity);
    }

    @Override
    public DtoResult<Void> acceptIotDeviceOnOffline(IotDataSubscribeEntity subscribeEntity) {
        // 更新能开设备在离线状态
        deviceInfoService.update(new LambdaUpdateWrapper<DeviceInfoEntity>()
                .set(DeviceInfoEntity::getOnlineStatus, subscribeEntity.getOnlineStatus())
                .eq(StringUtil.isNotBlank(subscribeEntity.getDeviceId()), DeviceInfoEntity::getId, subscribeEntity.getDeviceId())
                .eq(StringUtil.isNotBlank(subscribeEntity.getImei()), DeviceInfoEntity::getTripartiteSn, subscribeEntity.getImei()));
        List<IotDataSubscribeListByDeviceIdVo> iotDeviceOnlineSubscribeList = iotDeviceOnlineSubscribeService.getIotDeviceOnlineSubscribeListByDeviceId(subscribeEntity);
        iotDataHandleThread.handleIotOnOrOffLineData(iotDeviceOnlineSubscribeList, subscribeEntity.getOnlineStatus());
        return DtoResult.ok();
    }

    @Override
    public DtoResult<Long> taskSaveOrUpdate(VlinkerAlgServerTaskSaveOrUpdateReq serverTaskListReq) {
        return apiAlgorithmService.saveOrUpdateAlgServerTask(serverTaskListReq, null);
    }

    @Override
    public DtoResult<List<VlinkerAlgorithmGetAlgBoxResp>> getAlgBoxList(VlinkerAlgorithmGetAlgBoxReq getAlgBoxReq) {
        return apiAlgorithmService.getAlgBoxList(getAlgBoxReq);
    }

    @Override
    public DtoResult<Integer> editBoxFace(VlinkerAlgorithmEditBoxFaceReq editBoxFaceReq) {
        return apiAlgorithmService.editBoxFace(editBoxFaceReq);
    }

    @Override
    public DtoResult<Integer> delBoxFace(VlinkerAlgorithmEditBoxFaceReq editBoxFaceReq) {
        return apiAlgorithmService.delBoxFace(editBoxFaceReq);
    }

    @Override
    public DtoResult<Void> setDualCameraLinkage(OpenSetDualCameraLinkageReq deviceReq) {
        return feignOpenSystemApiService.setDualCameraLinkage(deviceReq);
    }

    @Override
    public DtoResult<Void> setSoundAndLightShock(OpenSetSoundAndLightShockReq deviceReq) {
        return feignOpenSystemApiService.setSoundAndLightShock(deviceReq);
    }

    @Override
    public DtoResult<Void> oneClickPatrol(OpenDeviceReq deviceReq) {
        return feignOpenSystemApiService.oneClickPatrol(deviceReq);
    }

    @Override
    public DtoResult<Void> setHumanoidMarkers(OpenSetHumanoidMarkersReq deviceReq) {
        return feignOpenSystemApiService.setHumanoidMarkers(deviceReq);
    }

    @Override
    public DtoResult<Void> gimbalCalibration(OpenDeviceReq deviceReq) {
        return feignOpenSystemApiService.gimbalCalibration(deviceReq);
    }

    @Override
    public DtoResult<DeviceInfoEntity> getDeviceInfoByImei(String imei) {
        return DtoResult.ok(deviceInfoService.findByTripartiteSn(imei));
    }

    @Override
    public DtoResult<QueryRecordResponse.Data> queryDoorRecord(QueryRecordRequest queryRecordRequest) {
        String componentName = ThirdPartyPlatformsBizComponent.VideoBizComponent.daHuaBizComponentName;
        List<ThirdPartyPlatformsAuthEntity> thirdPartyPlatformsAuthEntityList = thirdPartyPlatformsAuthService.list(new LambdaQueryWrapper<ThirdPartyPlatformsAuthEntity>()
                .eq(ThirdPartyPlatformsAuthEntity::getComponentName, componentName)
        );
        if (CollectionUtil.isEmpty(thirdPartyPlatformsAuthEntityList)) {
            return DtoResult.error("未查询到大华平台的授权信息");
        }
        QueryRecordResponse queryRecordResponse = new QueryRecordResponse();
        QueryRecordResponse.Data data1 = new QueryRecordResponse.Data();
        queryRecordResponse.setData(data1);
        for (ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity : thirdPartyPlatformsAuthEntityList) {
            QueryRecordResponse responseTemp = null;
            try {
                Future<QueryRecordResponse> future = threadPoolTaskExecutor.submit( () ->  daHuaIccBiz.queryDoorRecord(thirdPartyPlatformsAuthEntity, queryRecordRequest));
                responseTemp = future.get(10, TimeUnit.SECONDS); // 最长等待10s
                if  (Objects.nonNull(responseTemp) && "200".equals(responseTemp.getCode()) &&  "0".equals(responseTemp.getCode())) {
                    QueryRecordResponse.Data data = responseTemp.getData();
                    String accessToken = redisTemplate.opsForValue().get(DaHuaRedisConstant.DA_HUA_TOKEN_KEY + thirdPartyPlatformsAuthEntity.getAppKey());
                    if (StringUtil.isBlank(accessToken)) {
                        return DtoResult.error("大华token不存在");
                    }
                    //抓拍图片处理
                    data.getPageData().forEach(item -> {
                        if(StringUtil.isNotBlank(item.getRecordImageUrl())){
                            String newImageUrl = String.format("%s/evo-apigw/evo-oss/%s?token=%s",
                                    thirdPartyPlatformsAuthEntity.getHost(),
                                    item.getRecordImageUrl(),
                                    accessToken
                            );
                            item.setRecordImageUrl(newImageUrl);
                        }
                    });
                    List<QueryRecordResponse.Data.PageData> pageData = queryRecordResponse.getData().getPageData();
                    if (pageData != null) {
                        pageData.addAll(data.getPageData());
                    } else {
                        pageData = new ArrayList<>(data.getPageData());
                        queryRecordResponse.getData().setPageData(pageData);
                    }
                }
            } catch (Exception e) {
                log.error("大华查询门禁记录异常,thirdPartyPlatformsAuthEntity:{}", JSONUtil.toJsonStr(thirdPartyPlatformsAuthEntity), e);
            }
        }
        return DtoResult.ok(queryRecordResponse.getData());
    }

    @Override
    public DtoResult<Boolean> closeDoor(DoorSwitchRequest doorSwitchRequest) {
        String channelCode = doorSwitchRequest.getChannelCodeList().get(0);
        // 1.查询设备信息
        List<DeviceInfoEntity> deviceInfoEntityList = deviceInfoService.list(new LambdaQueryWrapper<DeviceInfoEntity>().eq(DeviceInfoEntity::getChannelId, channelCode));
        if (CollectionUtil.isEmpty(deviceInfoEntityList)) {
            return DtoResult.error("找不到该设备，请重试！");
        }
        // 2.找到该设备的授权信息
        ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity = thirdPartyPlatformsAuthService.getById(deviceInfoEntityList.get(0).getTripartiteId());
        if (null == thirdPartyPlatformsAuthEntity) {
            return DtoResult.error("找不到对应的授权信息，请重试！");
        }
        GeneralResponse iccResponse = daHuaIccBiz.closeDoor(thirdPartyPlatformsAuthEntity, doorSwitchRequest);
        if  ("0".equals(iccResponse.getCode())) {
            return DtoResult.ok(iccResponse.isSuccess());
        } else {
            return DtoResult.error(iccResponse.getErrMsg());
        }
    }

    @Override
    public DtoResult<Boolean> openDoor(DoorSwitchRequest doorSwitchRequest) {
        String channelCode = doorSwitchRequest.getChannelCodeList().get(0);
        // 1.查询设备信息
        List<DeviceInfoEntity> deviceInfoEntityList = deviceInfoService.list(new LambdaQueryWrapper<DeviceInfoEntity>().eq(DeviceInfoEntity::getChannelId, channelCode));
        if (CollectionUtil.isEmpty(deviceInfoEntityList)) {
            return DtoResult.error("找不到该设备，请重试！");
        }
        // 2.找到该设备的授权信息
        ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity = thirdPartyPlatformsAuthService.getById(deviceInfoEntityList.get(0).getTripartiteId());
        if (null == thirdPartyPlatformsAuthEntity) {
            return DtoResult.error("找不到对应的授权信息，请重试！");
        }
        GeneralResponse iccResponse = daHuaIccBiz.openDoor(thirdPartyPlatformsAuthEntity, doorSwitchRequest);
        if  ("0".equals(iccResponse.getCode())) {
            return DtoResult.ok(iccResponse.isSuccess());
        } else {
            return DtoResult.error(iccResponse.getErrMsg());
        }
    }

    @Override
    public DtoResult<DevicePageResponse.PageVO> getDoorDevicePage() {
        String componentName = ThirdPartyPlatformsBizComponent.VideoBizComponent.daHuaBizComponentName;
        List<ThirdPartyPlatformsAuthEntity> thirdPartyPlatformsAuthEntityList = thirdPartyPlatformsAuthService.list(new LambdaQueryWrapper<ThirdPartyPlatformsAuthEntity>()
                .eq(ThirdPartyPlatformsAuthEntity::getComponentName, componentName)
        );
        if (CollectionUtil.isEmpty(thirdPartyPlatformsAuthEntityList)) {
            return DtoResult.error("未查询到大华平台的授权信息");
        }
        DevicePageResponse response = new DevicePageResponse();
        response.setData(new DevicePageResponse.PageVO());
        for (ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity : thirdPartyPlatformsAuthEntityList) {
            DevicePageRequest devicePageRequest = new DevicePageRequest();
            devicePageRequest.setPageNum(1);
            devicePageRequest.setPageSize(50);
            //8-门禁设备
            devicePageRequest.setCategorys(List.of(8));
            DevicePageResponse responseTemp = null;
            try {
                Future<DevicePageResponse> future = threadPoolTaskExecutor.submit( () -> daHuaIccBiz.getComonnectDevicePage(thirdPartyPlatformsAuthEntity, devicePageRequest));
                responseTemp = future.get(10, TimeUnit.SECONDS); // 最长等待10s
                if  (Objects.nonNull(responseTemp) && "0".equals(responseTemp.getCode())) {
                    List<DevicePageResponse.PageVO.DeviceInfoVO> pageData = response.getData().getPageData();
                    if (pageData != null) {
                        pageData.addAll(responseTemp.getData().getPageData());
                    } else {
                        pageData = new ArrayList<>(responseTemp.getData().getPageData());
                        response.getData().setPageData(pageData);
                    }
                    pageData.addAll(responseTemp.getData().getPageData());
                }
            } catch (Exception e) {
                log.error("大华查询设备列表异常,thirdPartyPlatformsAuthEntity:{}", JSONUtil.toJsonStr(thirdPartyPlatformsAuthEntity), e);
            }
        }
        return DtoResult.ok(response.getData());
    }

    @Override
    public DtoResult<List<DoorChannelStatusResponse.DeviceChannel>> doorChannelStatus(DoorSwitchStatusRequest req) {
        String channelCode = req.getChannelCodes().get(0);
        // 1.查询设备信息
        List<DeviceInfoEntity> deviceInfoEntityList = deviceInfoService.list(new LambdaQueryWrapper<DeviceInfoEntity>().eq(DeviceInfoEntity::getChannelId, channelCode));
        if (CollectionUtil.isEmpty(deviceInfoEntityList)) {
            return DtoResult.error("找不到该设备，请重试！");
        }
        // 2.找到该设备的授权信息
        ThirdPartyPlatformsAuthEntity thirdPartyPlatformsAuthEntity = thirdPartyPlatformsAuthService.getById(deviceInfoEntityList.get(0).getTripartiteId());
        if (null == thirdPartyPlatformsAuthEntity) {
            return DtoResult.error("找不到对应的授权信息，请重试！");
        }
        DoorChannelStatusResponse response = daHuaIccBiz.doorChannelStatus(thirdPartyPlatformsAuthEntity, req);
        if  ("0".equals(response.getCode())) {
            return DtoResult.ok(response.getData());
        } else {
            return DtoResult.error(response.getErrMsg());
        }
    }

    @Override
    public DtoResult<CommonGetSoundAndLightShockReq> getSoundAndLightShock(OpenDeviceReq deviceReq) {
        return feignOpenSystemApiService.getSoundAndLightShock(deviceReq);
    }

    @Override
    public DtoResult<CommonGetHumanoidMarkersReq> getHumanoidMarkers(OpenDeviceReq deviceReq) {
        return feignOpenSystemApiService.getHumanoidMarkers(deviceReq);
    }
}