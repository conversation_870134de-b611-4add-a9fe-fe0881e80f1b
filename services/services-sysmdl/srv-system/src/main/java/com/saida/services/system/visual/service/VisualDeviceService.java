package com.saida.services.system.visual.service;

import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.service.IBaseService;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.open.dto.OpenNoticeOnLineMsgDTO;
import com.saida.services.open.entity.DeviceInfoEntity;
import com.saida.services.srv.entity.SrvVisualParentDeviceEntity;
import com.saida.services.srv.entity.VisualDeviceAuthEntity;
import com.saida.services.srv.entity.VisualDeviceEntity;
import com.saida.services.system.visual.dto.*;
import com.saida.services.system.visual.vo.DeviceListPageVo;
import com.saida.services.system.visual.vo.SyncOpenDeviceListVo;
import com.saida.services.visual.dto.AddDeviceDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 视频监控
 *
 * <AUTHOR>
 * email ${email}
 * date 2023-06-07 10:53:48
 */
public interface VisualDeviceService extends IBaseService<VisualDeviceEntity> {

    Result listPage(DevicePageListByYyDto dto, BaseRequest baseRequest);

    void excelExport(HttpServletResponse response, DevicePageListByYyDto dto);

    Result listPageByShare(DevicePageListDtoByYyShare dto, BaseRequest baseRequest);

    Result systemDeviceListPageByOrg(DevicePageListBySystemDto dto, BaseRequest baseRequest);

    Result systemDeviceListPageByUser(DevicePageListBySystemDto dto, BaseRequest baseRequest);

    DtoResult<BasePageInfoEntity<DeviceListPageVo>> treeDeviceListPageByOrg(DevicePageListByTreeDto dto, BaseRequest baseRequest);

    DtoResult<BasePageInfoEntity<DeviceListPageVo>> treeDeviceListPageByUser(DevicePageListByTreeDto dto, BaseRequest baseRequest);

    Result add(AddDeviceDto dto);

    Result info(String id);

    DtoResult<VisualDeviceEntity> infoByApp(Long deviceId);

    DtoResult<InfoByBCAppDto> infoByBCApp(VideoLiveUrlDto videoLiveUrlDto);

    DtoResult<SrvVisualParentDeviceEntity> infoByDeviceCodeCode(String deviceCodeCode);

    DtoResult<Integer> getOnlineStatusByDeviceCodeCode(String deviceCodeCode);

    Result edit(VisualDeviceEntity entity);

    DtoResult<Void> syncDeviceAndChannelName(SyncDeviceAndChannelNameDto dto);

    Result editByPark(VisualDeviceEntityByPark entity);

    Result bindBroadcast(BindBroadcastBoxDto dto);

    Result bindBox(BindBroadcastBoxDto dto);

    void genInfo(List<DeviceListPageVo> resultList);

    void genBoxName(List<DeviceListPageVo> resultList);

    Result getCountGroupOnline(DevicePageListByYyDto dto);

    SyncOpenDeviceListVo syncDevice(List<DeviceInfoEntity> list, VisualDeviceAuthEntity visualDeviceAuthEntity, Long userId);

    List<DeviceInfoListResultDto> getDeviceInfoList(DeviceInfoListParamDto dto);

    void updateDeviceOnline(OpenNoticeOnLineMsgDTO dto);

    Result editVolume(VisualDeviceEntity entity);
}