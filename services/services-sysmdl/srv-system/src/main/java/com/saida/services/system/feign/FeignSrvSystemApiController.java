package com.saida.services.system.feign;

import com.alibaba.fastjson.JSON;
import com.saida.services.algorithm.dto.PushAlarmDto;
import com.saida.services.common.base.DtoResult;
import com.saida.services.feign.srv.system.IFeignSrvSystemApiController;
import com.saida.services.open.dto.IotDataPushDto;
import com.saida.services.open.dto.IotOnOffLinePushDto;
import com.saida.services.open.dto.OpenNoticeOnLineMsgDTO;
import com.saida.services.open.dto.OpenNoticeThirdPartyAppMsgDTO;
import com.saida.services.open.entity.DeviceInfoEntity;
import com.saida.services.open.enums.OpenNoticeThirdPartyAppEnum;
import com.saida.services.srv.dto.SrvAddDeviceByWifiDTO;
import com.saida.services.srv.dto.VlinkConvMsg;
import com.saida.services.srv.vo.BCBindDeviceVo;
import com.saida.services.system.sys.service.AppUserGroupDeviceService;
import com.saida.services.system.visual.service.ReceiveAlarmPushService;
import com.saida.services.system.visual.service.VisualDeviceService;
import com.saida.services.system.websocket.appbc.AppBcWebSocketServerSend;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
public class FeignSrvSystemApiController implements IFeignSrvSystemApiController {

    @Resource
    private ReceiveAlarmPushService receiveAlarmPushService;
    @Resource
    private com.saida.services.system.visual.service.impl.FeignSrvSystemApiService feignSrvSystemApiService;
    @Resource
    private VisualDeviceService visualDeviceService;
    @Resource
    private AppUserGroupDeviceService appUserGroupDeviceService;

    @Override
    public DtoResult<Void> vlinkOpenAlarmReceive(PushAlarmDto dto) {
        return receiveAlarmPushService.vlinkOpenAlarmSub(dto);
    }

    @Resource
    private AppBcWebSocketServerSend appBcWebSocketServerSend;

    @Override
    public DtoResult<Void> vlinkConvMsg(VlinkConvMsg vlinkConvMsg) {
        log.info("vlinkConvMsg:{}", vlinkConvMsg);
        return appBcWebSocketServerSend.sendMsgByVlinkConvMsg(vlinkConvMsg);
    }

    @Override
    public DtoResult<Void> vlinkOpenIotDataReceive(IotDataPushDto dto) {
        return receiveAlarmPushService.vlinkOpenIotDataReceive(dto);
    }

    @Override
    public DtoResult<Void> receiveOpenMsg(OpenNoticeThirdPartyAppMsgDTO dto) {
        log.info("V-LINKER视频融合平台.接收能开消息...data={}", JSON.toJSON(dto));
        Object object = dto.getData();
        if (OpenNoticeThirdPartyAppEnum.ONE.equals(dto.getMsgType())) {
            OpenNoticeOnLineMsgDTO openNoticeOnLineMsgDTO = JSON.parseObject(JSON.toJSONString(object), OpenNoticeOnLineMsgDTO.class);
            visualDeviceService.updateDeviceOnline(openNoticeOnLineMsgDTO);
        }
        return DtoResult.ok();
    }

    @Override
    public DtoResult<Void> syncAllDeviceChannelDataFromOpen() {
        return feignSrvSystemApiService.syncAllDeviceChannelDataFromOpen();
    }

    @Override
    public DtoResult<Void> syncIncreDeviceChannelDataFromOpen(List<DeviceInfoEntity> deviceInfoEntityList) {
        return feignSrvSystemApiService.syncIncreDeviceChannelDataFromOpen(deviceInfoEntityList);
    }

    @Override
    public DtoResult<Void> vlinkOpenIotOnOffLineReceive(IotOnOffLinePushDto dto) {
        return receiveAlarmPushService.vlinkOpenIotOnOffLineReceive(dto);
    }

    @Override
    public DtoResult<BCBindDeviceVo> addCustomerDevice(SrvAddDeviceByWifiDTO dto) {
        return appUserGroupDeviceService.addDeviceByWifi(dto);
    }
}