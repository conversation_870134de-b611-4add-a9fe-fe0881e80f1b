package com.saida.services.system.visual.service.impl;

import cn.comtom.cbs.sdk.api.response.dev.DeviceUpdateResponse;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Sets;
import com.saida.services.common.base.DtoResult;
import com.saida.services.common.base.Result;
import com.saida.services.common.config.ThreadPoolConfig;
import com.saida.services.common.entity.BasePageInfoEntity;
import com.saida.services.common.service.BaseServiceImpl;
import com.saida.services.common.tools.*;
import com.saida.services.converge.entity.DeviceEntity;
import com.saida.services.converge.entity.OpsDeviceChannelEntity;
import com.saida.services.converge.entity.dto.ConvDeviceAndChannelDTO;
import com.saida.services.converge.enums.AccessWayType;
import com.saida.services.converge.vo.DeviceCapacityVo;
import com.saida.services.entities.base.BaseRequest;
import com.saida.services.entities.pojo.CountDto;
import com.saida.services.enumeration.OrgTypeEnum;
import com.saida.services.enumeration.StatusEnum;
import com.saida.services.enumeration.UserTypeEnum;
import com.saida.services.feign.iot.system.IFeignIotProductSystemApiController;
import com.saida.services.feign.open.system.IFeignOpenSystemApiController;
import com.saida.services.iot.entity.ProductEntity;
import com.saida.services.jpush.JiGuangPushBean;
import com.saida.services.jpush.JiGuangPushService;
import com.saida.services.open.dto.DeviceCapacityDto;
import com.saida.services.open.dto.GetDeviceCapacityDto;
import com.saida.services.open.dto.OpenNoticeOnLineMsgDTO;
import com.saida.services.open.entity.DeviceInfoEntity;
import com.saida.services.open.enums.DeviceCapacityEnum;
import com.saida.services.open.enums.OpenThirdPartyPlatformsTypeEnum;
import com.saida.services.srv.dto.SrvDeviceOperateDto;
import com.saida.services.srv.entity.SrvAppUserGroupDeviceEntity;
import com.saida.services.srv.entity.SrvVisualParentDeviceEntity;
import com.saida.services.srv.entity.VisualDeviceAuthEntity;
import com.saida.services.srv.entity.VisualDeviceEntity;
import com.saida.services.srv.enums.SrvCameraPlatformTypeEnum;
import com.saida.services.srv.excel.SrvDeviceListExcel;
import com.saida.services.system.constants.SrvThirdPartyPlatformsBizComponent;
import com.saida.services.system.iot.entity.IotDeviceDataEntity;
import com.saida.services.system.iot.entity.IotDeviceDataRecordEntity;
import com.saida.services.system.iot.service.IotDeviceDataRecordService;
import com.saida.services.system.iot.service.IotDeviceDataService;
import com.saida.services.system.noise.entity.NoisePushLogEntity;
import com.saida.services.system.noise.mapper.NoisePushLogMapper;
import com.saida.services.system.sys.dto.GroupAndFamilyNameByDeviceCodeCodeParamDTO;
import com.saida.services.system.sys.dto.GroupAndFamilyNameByDeviceCodeCodeResultDTO;
import com.saida.services.system.sys.dto.UserPermissionDto;
import com.saida.services.system.sys.entity.OrgEntity;
import com.saida.services.system.sys.entity.UserEntity;
import com.saida.services.system.sys.service.*;
import com.saida.services.system.sys.vo.BCDeviceDetailVo;
import com.saida.services.system.sys.vo.OrgTypeByIdVo;
import com.saida.services.system.visual.biz.BroadcastBiz;
import com.saida.services.system.visual.biz.VisualVideoBiz;
import com.saida.services.system.visual.biz.comtom.audition.LiveByWebParam;
import com.saida.services.system.visual.biz.pojo.resp.CommonVideoLiveResp;
import com.saida.services.system.visual.dto.*;
import com.saida.services.system.visual.entity.*;
import com.saida.services.system.visual.enums.ToCMsgTypeEnum;
import com.saida.services.system.visual.mapper.VisualDeviceMapper;
import com.saida.services.system.visual.mapper.VisualDeviceOrgMapper;
import com.saida.services.system.visual.service.*;
import com.saida.services.system.visual.vo.DeviceListPageVo;
import com.saida.services.system.visual.vo.IotDataVo;
import com.saida.services.system.visual.vo.SyncOpenDeviceListVo;
import com.saida.services.tools.attr.AttrUtil;
import com.saida.services.visual.dto.AddDeviceDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class VisualDeviceServiceImpl extends BaseServiceImpl<VisualDeviceMapper, VisualDeviceEntity> implements VisualDeviceService {

    @Resource
    private VisualDeviceAuthService visualDeviceAuthService;
    @Resource
    private OrgService orgService;
    @Resource
    private AttributeDetailService attributeDetailService;
    @Resource
    private VisualDeviceService visualDeviceService;
    @Resource
    private VisualDeviceOrgService visualDeviceOrgService;
    @Resource
    private SceneInfoService sceneInfoService;
    @Resource
    private VisualDeviceTreeService visualDeviceTreeService;
    @Resource
    private VisualCameraBoxService visualCameraBoxService;
    @Resource
    private VisualDeviceOrgMapper visualDeviceOrgMapper;
    @Resource
    private NoisePushLogMapper noisePushLogMapper;
    @Resource
    private UserService userService;
    @Resource
    private VisualParentDeviceService visualParentDeviceService;
    @Resource
    private OrgRenewService orgRenewService;
    @Resource
    private VisualDeviceOnlineLogService visualDeviceOnlineLogService;
    @Resource
    private IFeignOpenSystemApiController iFeignOpenSystemApiController;
    @Resource
    private VisualMsgService visualMsgService;
    @Resource(name = ThreadPoolConfig.TASK_EXECUTOR_BEAN_NAME)
    private Executor taskExecutor;
    @Resource
    private JiGuangPushService jiGuangPushService;
    @Resource
    private AppUserGroupDeviceService appUserGroupDeviceService;
    @Resource
    private UserMsgPermissionService userMsgPermissionService;
    @Resource
    private IotDeviceDataRecordService iotDeviceDataRecordService;
    @Resource
    private IotDeviceDataService iotDeviceDataService;
    @Resource
    private IFeignIotProductSystemApiController feignIotProductSystemApiController;

    /**
     * 运营设备管理-设备列表（设备-组织，visual_device_org）
     */
    @Override
    public Result listPage(DevicePageListByYyDto dto, BaseRequest baseRequest) {
        Long orgId = (null == dto.getOrgId()) ? JwtUtil.getOrgId() : dto.getOrgId();
        // 包含下级
        if (dto.getSubLevel()) {
            Set<Long> orgIdSet = new HashSet<>();
            orgIdSet.add(orgId);
            orgIdSet.addAll(orgService.getChildIds(orgId, true));
            dto.setOrgIdSet(orgIdSet);
            dto.setOrgId(null);
        } else {
            dto.setOrgId(orgId);
            dto.setOrgIdSet(new HashSet<>());
        }
        try (Page<DeviceListPageVo> page = PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize())) {
            if (StringUtil.isNotEmpty(baseRequest.getSortField()) && baseRequest.getSorted() != null) {
                PageHelper.orderBy(String.format("t2.%s %s", StringUtil.toUnderscore(baseRequest.getSortField()), baseRequest.getSorted().getCode()));
            } else {
                PageHelper.orderBy("t2.id desc");
            }
            List<DeviceListPageVo> resultList = visualDeviceOrgMapper.selectVisualDeviceList(dto);
            this.genInfo(resultList);
            return Result.ok(new BasePageInfoEntity<>(page));
        }
    }

    @Override
    public void excelExport(HttpServletResponse response, DevicePageListByYyDto dto) {
        try {
            Long orgId = (null == dto.getOrgId()) ? JwtUtil.getOrgId() : dto.getOrgId();
            // 包含下级
            if (dto.getSubLevel()) {
                Set<Long> orgIdSet = new HashSet<>();
                orgIdSet.add(orgId);
                orgIdSet.addAll(orgService.getChildIds(orgId, true));
                dto.setOrgIdSet(orgIdSet);
                dto.setOrgId(null);
            } else {
                dto.setOrgId(orgId);
                dto.setOrgIdSet(new HashSet<>());
            }
            List<DeviceListPageVo> resultList = visualDeviceOrgMapper.selectVisualDeviceList(dto);
            this.genInfo(resultList);

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String yyyymmdd = DateUtil.format(new Date(), "yyyyMMdd");
            String fileName = URLEncoder.encode("通道列表" + yyyymmdd, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "fileName");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            response.setHeader("fileName", fileName + ".xlsx");
            List<SrvDeviceListExcel> data = new ArrayList<>();
            for (DeviceListPageVo deviceListPageVo : resultList) {
                SrvDeviceListExcel excel = JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(deviceListPageVo)), SrvDeviceListExcel.class);
                Long deviceType = deviceListPageVo.getDeviceType();
                if (Objects.equals(deviceType, SrvCameraPlatformTypeEnum.TYPE_1.getDicId())) {
                    excel.setDeviceTypeName("视频设备");
                } else if (Objects.equals(deviceType, SrvCameraPlatformTypeEnum.TYPE_2.getDicId())) {
                    excel.setDeviceTypeName("广播设备");
                } else if (Objects.equals(deviceType, SrvCameraPlatformTypeEnum.TYPE_3.getDicId())) {
                    excel.setDeviceTypeName("盒子设备");
                } else if (Objects.equals(deviceType, SrvCameraPlatformTypeEnum.TYPE_4.getDicId())) {
                    excel.setDeviceTypeName("物联设备");
                } else {
                    excel.setDeviceTypeName("未知类型");
                }
                excel.setStatus("离线");
                if (Objects.equals(1, deviceListPageVo.getIsOnline())) {
                    excel.setStatus("在线");
                }
                excel.setIsOwnerName(Objects.equals(1, deviceListPageVo.getIsOwner()) ? "是" : "否");
                data.add(excel);
            }
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(EasyExcelStyleUtil.getHeadStyle(), EasyExcelStyleUtil.getContentStyle());
            EasyExcel.write(response.getOutputStream(), SrvDeviceListExcel.class)
                    .autoCloseStream(Boolean.FALSE)
                    .sheet("通道列表")
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .doWrite(data);
        } catch (Exception e) {
            log.error("导出通道列表失败...msg={}", e.getMessage(), e);
        }
    }

    /**
     * 运营设备管理-设备汇聚-设备列表（设备-组织，visual_device_org）
     */
    @Override
    public Result listPageByShare(DevicePageListDtoByYyShare dto, BaseRequest baseRequest) {
        if (null == dto.getShareOrgId()) {
            return Result.error("汇聚组织ID不能为空，请检查后重试！");
        }
        if (Objects.equals(dto.getOrgId(), dto.getShareOrgId())) {
            return Result.error("不能汇聚自有设备，请重新选择！");
        }
        Long orgId = (null == dto.getOrgId()) ? JwtUtil.getOrgId() : dto.getOrgId();
        // 获取该组织下所有的企业ID
        List<Long> orgIdList = orgService.getChildEnterpriseIds(orgId, null);
        Set<Long> orgIdSet = Sets.newHashSet(orgIdList);
        // 移出要汇聚的企业ID
        orgIdSet.remove(dto.getShareOrgId());
        dto.setOrgIdSet(orgIdSet);
        // 要分享的组织id
        Long shareOrgId = dto.getShareOrgId();
        // 查询出当前要分享组织下的所有设备
        List<VisualDeviceOrgEntity> visualDeviceOrgEntityList = visualDeviceOrgService.list(new LambdaQueryWrapper<VisualDeviceOrgEntity>()
                .eq(VisualDeviceOrgEntity::getOrgId, shareOrgId)
        );
        if (!CollectionUtils.isEmpty(visualDeviceOrgEntityList)) {
            dto.setNotInDeviceIdSet(visualDeviceOrgEntityList.stream().map(VisualDeviceOrgEntity::getDeviceId).collect(Collectors.toSet()));
        }
        // 只显示监控设备
        Long deviceType1 = SrvCameraPlatformTypeEnum.TYPE_1.getDicId();
        dto.setDeviceType(deviceType1);
        // 只显示自有设备
        dto.setIsOwner(1);
        Page<DeviceListPageVo> page = PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize());
        List<DeviceListPageVo> resultList = visualDeviceOrgMapper.selectVisualDeviceListByShare(dto);
        this.genInfo(resultList);
        return Result.ok(new BasePageInfoEntity<>(page));
    }

    /*
     * 系统管理-设备管理，设备列表（用户-组织，visual_device_user）
     */
    @Override
    public Result systemDeviceListPageByUser(DevicePageListBySystemDto dto, BaseRequest baseRequest) {
        Page<DeviceListPageVo> page = PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize());
        List<DeviceListPageVo> resultList = visualDeviceOrgMapper.getSystemDeviceListByUser(dto);
        this.genBoxName(resultList);
        this.genInfo(resultList);
        return Result.ok(new BasePageInfoEntity<>(page));
    }

    /**
     * 系统管理-设备管理-设备列表（设备-组织，visual_device_org）
     */
    @Override
    public Result systemDeviceListPageByOrg(DevicePageListBySystemDto dto, BaseRequest baseRequest) {
        Page<DeviceListPageVo> page = PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize());
        List<DeviceListPageVo> resultList = visualDeviceOrgMapper.getSystemDeviceListByOrg(dto);
        this.genBoxName(resultList);
        this.genInfo(resultList);
        return Result.ok(new BasePageInfoEntity<>(page));
    }

    /**
     * 设备树管理-设备列表（设备-用户，visual_device_user）
     */
    @Override
    public DtoResult<BasePageInfoEntity<DeviceListPageVo>> treeDeviceListPageByUser(DevicePageListByTreeDto dto, BaseRequest baseRequest) {
        Page<DeviceListPageVo> page = PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize());
        List<DeviceListPageVo> resultList = visualDeviceOrgMapper.getTreeDeviceListByUser(dto);
        this.genBoxName(resultList);
        if (dto.getRegular() != null && dto.getRegular() == 18) {
            this.getNoiseInfo(resultList);
        }
        if (Objects.equals(dto.getDeviceType(), SrvCameraPlatformTypeEnum.TYPE_4.getDicId()) || ObjectUtil.isNotNull(dto.getProductId())) {
            // 物联设备查询上报数据
            this.getIotData(resultList);
        }
        this.genInfo(resultList);
        return DtoResult.ok(new BasePageInfoEntity<>(page));
    }

    /**
     * 设备树管理-设备列表（设备-组织，visual_device_org）
     */
    @Override
    public DtoResult<BasePageInfoEntity<DeviceListPageVo>> treeDeviceListPageByOrg(DevicePageListByTreeDto dto, BaseRequest baseRequest) {
        Page<DeviceListPageVo> page = PageHelper.startPage(baseRequest.getPageNum(), baseRequest.getPageSize());
        List<DeviceListPageVo> resultList = visualDeviceOrgMapper.getTreeDeviceListByOrg(dto);
        this.genBoxName(resultList);
        if (ObjectUtil.isNotNull(dto.getRegular()) && dto.getRegular() == 18) {
            this.getNoiseInfo(resultList);
        }
        if (Objects.equals(dto.getDeviceType(), SrvCameraPlatformTypeEnum.TYPE_4.getDicId()) || ObjectUtil.isNotNull(dto.getProductId())) {
            // 物联设备查询上报数据
            this.getIotData(resultList);
        }
        this.genInfo(resultList);
        return DtoResult.ok(new BasePageInfoEntity<>(page));
    }

    private void getIotData(List<DeviceListPageVo> resultList) {
        List<Long> ids = resultList.stream().map(DeviceListPageVo::getDeviceId).collect(Collectors.toList());
        // 查询每个设备最新的数据上报记录
        List<IotDeviceDataRecordEntity> iotDeviceDataRecords = iotDeviceDataRecordService.listByDeviceIds(ids);
        if (CollectionUtil.isEmpty(iotDeviceDataRecords)) {
            return;
        }
        Map<Long, List<IotDeviceDataRecordEntity>> dataRecordByDeviceMap = iotDeviceDataRecords.stream().collect(Collectors.groupingBy(IotDeviceDataRecordEntity::getDeviceId));
        // 根据上报记录获取详细字段数据
        List<IotDeviceDataEntity> iotDeviceFieldDatas = iotDeviceDataService.getFieldData(iotDeviceDataRecords.stream().map(IotDeviceDataRecordEntity::getId).collect(Collectors.toList()));
        if (CollectionUtil.isEmpty(iotDeviceFieldDatas)) {
            return;
        }
        Map<Long, List<IotDeviceDataEntity>> fieldDatasByRecordIdMap = iotDeviceFieldDatas.stream().collect(Collectors.groupingBy(IotDeviceDataEntity::getRecordId));
        resultList.forEach(item -> {
            if (dataRecordByDeviceMap.containsKey(item.getDeviceId())) {
                List<IotDeviceDataRecordEntity> iotDeviceDataRecordEntities = dataRecordByDeviceMap.get(item.getDeviceId());
                IotDeviceDataRecordEntity dataRecordEntity = iotDeviceDataRecordEntities.get(0);
                Long recordId = dataRecordEntity.getId();
                if (fieldDatasByRecordIdMap.containsKey(recordId)) {
                    List<IotDataVo> iotDataVoList = fieldDatasByRecordIdMap.get(recordId).stream().map(item1 -> {
                        IotDataVo iotDataVo = new IotDataVo();
                        BeanUtils.copyProperties(item1, iotDataVo);
                        iotDataVo.setReportTime(dataRecordEntity.getCreateTime());
                        return iotDataVo;
                    }).collect(Collectors.toList());
                    item.setIotDataVoList(iotDataVoList);
                }
            }
        });
    }

    /**
     * 查询噪音设备的噪音数据和阈值数据
     *
     * @param resultList
     * @return void
     */
    private void getNoiseInfo(List<DeviceListPageVo> resultList) {
        List<Long> ids = resultList.stream().map(DeviceListPageVo::getDeviceId).collect(Collectors.toList());
        List<NoisePushLogEntity> noisePushLogEntities = noisePushLogMapper.listByDeviceIds(ids);
        Map<Long, NoisePushLogEntity> noisePushLogMap = noisePushLogEntities.stream().collect(Collectors.toMap(NoisePushLogEntity::getDeviceId, Function.identity()));
        resultList.forEach(item -> {
            NoisePushLogEntity noisePushLogEntity = noisePushLogMap.get(item.getDeviceId());
            if (ObjectUtil.isNotNull(noisePushLogEntity)) {
                item.setVal(noisePushLogEntity.getVal());
                item.setValue(noisePushLogEntity.getValue());
                item.setWarnTime(noisePushLogEntity.getCreateTime());
                if (ObjectUtil.isNotNull(noisePushLogEntity.getOperateSymbol())) {
                    if (noisePushLogEntity.getOperateSymbol() == 1) {
                        item.setIsWarn(noisePushLogEntity.getValue().compareTo(noisePushLogEntity.getVal()) >= 0 ? 1 : 0);
                    } else if (noisePushLogEntity.getOperateSymbol() == 2) {
                        item.setIsWarn(noisePushLogEntity.getValue().compareTo(noisePushLogEntity.getVal()) < 0 ? 1 : 0);
                    }
                } else {
                    item.setIsWarn(0);
                }
            }
        });
    }

    @Override
    public Result add(AddDeviceDto dto) {
        // 构建查询条件
        LambdaQueryWrapper<VisualDeviceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<VisualDeviceEntity>()
                .eq(VisualDeviceEntity::getDeviceCode, dto.getDeviceCode())
                .eq(VisualDeviceEntity::getAuthId, dto.getAuthId());
        // 如果通道编码不为空，则需要根据设备编码和通道编码判断唯一
        if (StringUtil.isNotEmpty(dto.getChannelCode())) {
            lambdaQueryWrapper.eq(VisualDeviceEntity::getChannelCode, dto.getChannelCode());
        }
        VisualDeviceEntity visualDevice = this.getAny(lambdaQueryWrapper);
        if (null != visualDevice) {
            return Result.error("设备已存在，请重新输入！");
        }
        // 1.保存监控设备信息 visual_device
        Long cameraId = IdWorker.getId();
        VisualDeviceEntity visualDeviceEntity = new VisualDeviceEntity();
        BeanUtils.copyProperties(dto, visualDeviceEntity);
        visualDeviceEntity.setId(cameraId);
        visualDeviceEntity.setBelongOrgId(dto.getOrgId());
        this.save(visualDeviceEntity);

        Long deviceTreeId = null;
        // 判断当前选中的组织类型
        OrgTypeByIdVo vo = orgService.getOrgTypeById(dto.getOrgId());
        OrgTypeEnum orgTypeEnum = vo.getOrgTypeEnum();
        OrgEntity orgEntity = vo.getOrgEntity();
        if (OrgTypeEnum.QI_YE.equals(orgTypeEnum)) {
            // 查询是否有根设备树
            VisualDeviceTreeEntity visualDeviceTreeEntity = visualDeviceTreeService.getAny(new LambdaQueryWrapper<VisualDeviceTreeEntity>()
                    .eq(VisualDeviceTreeEntity::getOrgId, dto.getOrgId())
                    .eq(VisualDeviceTreeEntity::getParentId, 0)
            );
            // 2.如果根设备树不存在，则构造一个根设备树
            if (visualDeviceTreeEntity == null) {
                deviceTreeId = IdWorker.getId();
                VisualDeviceTreeEntity visualDeviceTreeEntityInsert = new VisualDeviceTreeEntity();
                visualDeviceTreeEntityInsert.setId(deviceTreeId);
                visualDeviceTreeEntityInsert.setName(orgEntity.getName());
                visualDeviceTreeEntityInsert.setSort(0);
                visualDeviceTreeEntityInsert.setParentId(0L);
                visualDeviceTreeEntityInsert.setIdChain(String.valueOf(deviceTreeId));
                visualDeviceTreeEntityInsert.setOrgId(dto.getOrgId());
                visualDeviceTreeService.save(visualDeviceTreeEntityInsert);
            } else {
                deviceTreeId = visualDeviceTreeEntity.getId();
            }
        }

        // 3.保存设备和组织的绑定关系
        VisualDeviceOrgEntity visualDeviceOrgEntity = new VisualDeviceOrgEntity();
        visualDeviceOrgEntity.setDeviceId(cameraId);
        visualDeviceOrgEntity.setOrgId(dto.getOrgId());
        visualDeviceOrgEntity.setAppScene(dto.getAppScene());
        visualDeviceOrgEntity.setIsOwner(1);
        if (null != deviceTreeId) {
            visualDeviceOrgEntity.setDeviceTreeId(deviceTreeId);
        }
        visualDeviceOrgService.save(visualDeviceOrgEntity);


        return Result.ok("新增成功");
    }

    @Override
    public Result info(String id) {
        // 1.查询设备-组织绑定关系 visual_camera_org
        VisualDeviceOrgEntity visualDeviceOrgEntity = visualDeviceOrgService.getById(id);
        if (visualDeviceOrgEntity == null) {
            return Result.error("数据不存在，请检查后重试！");
        }
        // 2.查询设备信息 visual_camera
        VisualDeviceEntity visualDeviceEntity = this.getById(visualDeviceOrgEntity.getDeviceId());
        if (visualDeviceEntity == null) {
            return Result.error("数据不存在，请检查后重试！");
        }
        DeviceListPageVo vo = new DeviceListPageVo();
        BeanUtils.copyProperties(visualDeviceEntity, vo);
        vo.setId(visualDeviceOrgEntity.getId());
        vo.setOrgId(visualDeviceOrgEntity.getOrgId());
        vo.setAppScene(visualDeviceOrgEntity.getAppScene());
        VisualDeviceAuthEntity visualDeviceAuthEntity = visualDeviceAuthService.getById(visualDeviceEntity.getAuthId());
        if (null != visualDeviceAuthEntity && null != visualDeviceAuthEntity.getId()) {
            vo.setPlatformName(visualDeviceAuthEntity.getPlatformName());
        }
        List<DeviceListPageVo> resultList = new ArrayList<>();
        resultList.add(vo);
        this.genInfo(resultList);
        return Result.ok(resultList.get(0));
    }

    @Override
    public DtoResult<VisualDeviceEntity> infoByApp(Long deviceId) {
        VisualDeviceEntity visualDeviceEntity = visualDeviceService.getById(deviceId);
        if (null == visualDeviceEntity) {
            return DtoResult.error("设备不存在");
        }
        if (!Objects.equals(visualDeviceEntity.getAccessWay(), AccessWayType.SDSDK.getType())) {
            visualDeviceEntity.setHasPreset(1);
            visualDeviceEntity.setHasTalk(1);
            visualDeviceEntity.setHasPtz(1);
        }
        SrvAppUserGroupDeviceEntity srvAppUserGroupDeviceEntity = appUserGroupDeviceService.getMyGroupDeviceByDeviceSn(visualDeviceEntity.getDeviceCodeCode());
        visualDeviceEntity.setToCDeviceName(ObjectUtil.isNotNull(srvAppUserGroupDeviceEntity) ? srvAppUserGroupDeviceEntity.getDeviceName() : "");
        DtoResult<DeviceCapacityVo> result = iFeignOpenSystemApiController.getDeviceCapacity(GetDeviceCapacityDto.builder().deviceId(visualDeviceEntity.getDeviceCodeCode()).build());
        if (Objects.equals(result.getCode(), 200)) {
            DeviceCapacityVo deviceCapacityVo = result.getData();
            Map<String, DeviceCapacityDto> deviceCapacityDtoMap = deviceCapacityVo.getDeviceCapacity();
            visualDeviceEntity.setDeviceCapacity(deviceCapacityDtoMap);

            if (Objects.nonNull(deviceCapacityDtoMap)) {
                if (deviceCapacityDtoMap.containsKey(DeviceCapacityEnum.PAN_TILT_CONTROL.getField())) {
                    visualDeviceEntity.setHasPtz(deviceCapacityDtoMap.get(DeviceCapacityEnum.PAN_TILT_CONTROL.getField()).getValue());
                }
                if (deviceCapacityDtoMap.containsKey(DeviceCapacityEnum.INTERCOM.getField())) {
                    visualDeviceEntity.setHasTalk(deviceCapacityDtoMap.get(DeviceCapacityEnum.INTERCOM.getField()).getValue());
                }
                if (deviceCapacityDtoMap.containsKey(DeviceCapacityEnum.PRESET.getField())) {
                    visualDeviceEntity.setHasPreset(deviceCapacityDtoMap.get(DeviceCapacityEnum.PRESET.getField()).getValue());
                }
            }
        }
        return DtoResult.ok(visualDeviceEntity);
    }

    @Resource
    private VisualVideoBiz visualVideoBiz;

    /**
     * 需要验证
     */
    @Override
    public DtoResult<InfoByBCAppDto> infoByBCApp(VideoLiveUrlDto videoLiveUrlDto) {
        InfoByBCAppDto dto = new InfoByBCAppDto();
        CompletableFuture<DtoResult<VisualDeviceEntity>> f1 =
                CompletableFuture.supplyAsync(() -> infoByApp(videoLiveUrlDto.getDeviceId()), taskExecutor);
        SrvDeviceOperateDto srvDeviceOperateDto = new SrvDeviceOperateDto();
        srvDeviceOperateDto.setDeviceId(videoLiveUrlDto.getDeviceId());
        CompletableFuture<DtoResult<BCDeviceDetailVo>> f2 =
                CompletableFuture.supplyAsync(() -> appUserGroupDeviceService.getDeviceAndRefInfoBySn(srvDeviceOperateDto), taskExecutor);
        CompletableFuture<DtoResult<CommonVideoLiveResp>> f3 =
                CompletableFuture.supplyAsync(() -> visualVideoBiz.getVideoLiveUrl(videoLiveUrlDto), taskExecutor);

        // 等待所有任务完成
        CompletableFuture.allOf(f1, f2, f3).join();
        try {
            DtoResult<VisualDeviceEntity> infoByAppRes = f1.get();
            if (infoByAppRes.success()) {
                dto.setVisualDeviceEntity(infoByAppRes.getData());
            }
            DtoResult<BCDeviceDetailVo> bcDeviceDetailVoDtoResult = f2.get();
            if (bcDeviceDetailVoDtoResult.success()) {
                dto.setBcDeviceDetailVo(bcDeviceDetailVoDtoResult.getData());
            }
            DtoResult<CommonVideoLiveResp> commonVideoLiveRespDtoResult = f3.get();
            if (commonVideoLiveRespDtoResult.success()) {
                dto.setCommonVideoLiveResp(commonVideoLiveRespDtoResult.getData());
            }
        } catch (Exception e) {
            log.error("error.infoByBCApp", e);
            return DtoResult.error("获取设备信息失败");
        }
        return DtoResult.ok(dto);
    }

    @Override
    public DtoResult<SrvVisualParentDeviceEntity> infoByDeviceCodeCode(String deviceCodeCode) {
        SrvVisualParentDeviceEntity visualParentDeviceEntity = visualParentDeviceService.getAny(new LambdaQueryWrapper<SrvVisualParentDeviceEntity>()
                .eq(SrvVisualParentDeviceEntity::getDeviceCodeCode, deviceCodeCode)
                .orderByAsc(SrvVisualParentDeviceEntity::getId)
        );
        if (Objects.isNull(visualParentDeviceEntity)) {
            return DtoResult.error("设备不存在");
        }
        return DtoResult.ok(visualParentDeviceEntity);
    }

    @Override
    public DtoResult<Integer> getOnlineStatusByDeviceCodeCode(String deviceCodeCode) {
        VisualDeviceAuthEntity visualDeviceAuthEntity = visualDeviceAuthService.getAny(new LambdaQueryWrapper<VisualDeviceAuthEntity>()
                .eq(VisualDeviceAuthEntity::getComponentName, SrvThirdPartyPlatformsBizComponent.vlinkerOpenBizComponentName)
                .eq(VisualDeviceAuthEntity::getPlatformType, SrvCameraPlatformTypeEnum.TYPE_1.getDicId())
        );
        SrvVisualParentDeviceEntity srvVisualParentDeviceEntity = visualParentDeviceService.getAny(new LambdaQueryWrapper<SrvVisualParentDeviceEntity>()
                .eq(Objects.nonNull(visualDeviceAuthEntity), SrvVisualParentDeviceEntity::getDeviceCodeCode, deviceCodeCode)
                .eq(SrvVisualParentDeviceEntity::getDeviceCodeCode, deviceCodeCode)
        );
        if (Objects.isNull(srvVisualParentDeviceEntity)) {
            return DtoResult.error("设备不存在");
        }
        DeviceInfoEntity deviceInfoEntity = new DeviceInfoEntity();
        deviceInfoEntity.setDeviceId(srvVisualParentDeviceEntity.getDeviceCode());
        ConvDeviceAndChannelDTO convDeviceAndChannelDTO = iFeignOpenSystemApiController.getDeviceChannelList(deviceInfoEntity);
        if (Objects.isNull(convDeviceAndChannelDTO) || Objects.isNull(convDeviceAndChannelDTO.getDeviceEntity())) {
            return DtoResult.ok(0);
        }

        // 更新在线离线状态
        taskExecutor.execute(() -> {
            DeviceEntity deviceEntity = convDeviceAndChannelDTO.getDeviceEntity();
            if (Objects.nonNull(deviceEntity) && !Objects.equals(deviceEntity.getStatus(), srvVisualParentDeviceEntity.getIsOnline())) {
                visualParentDeviceService.update(new LambdaUpdateWrapper<SrvVisualParentDeviceEntity>()
                        .set(SrvVisualParentDeviceEntity::getIsOnline, deviceEntity.getStatus())
                        .eq(SrvVisualParentDeviceEntity::getId, srvVisualParentDeviceEntity.getId())
                );
            }

            List<OpsDeviceChannelEntity> opsDeviceChannelEntityList = convDeviceAndChannelDTO.getOpsDeviceChannelEntityList();
            if (CollectionUtil.isNotEmpty(opsDeviceChannelEntityList)) {
                List<VisualDeviceEntity> visualDeviceEntityList = this.list(new LambdaQueryWrapper<VisualDeviceEntity>()
                        .eq(VisualDeviceEntity::getParentCode, srvVisualParentDeviceEntity.getDeviceCode()));
                for (VisualDeviceEntity visualDeviceEntity : visualDeviceEntityList) {
                    Optional<OpsDeviceChannelEntity> optional = opsDeviceChannelEntityList.stream().filter(t1 -> Objects.equals(t1.getDeviceSn(), visualDeviceEntity.getDeviceCodeCode())
                            && Objects.equals(t1.getChannelId(), visualDeviceEntity.getChannelCode())).findFirst();
                    if (optional.isPresent()) {
                        OpsDeviceChannelEntity opsDeviceChannelEntity = optional.get();
                        if (!Objects.equals(opsDeviceChannelEntity.getStatus(), visualDeviceEntity.getIsOnline())) {
                            this.update(new LambdaUpdateWrapper<VisualDeviceEntity>()
                                    .set(VisualDeviceEntity::getIsOnline, opsDeviceChannelEntity.getStatus())
                                    .eq(VisualDeviceEntity::getId, visualDeviceEntity.getId())
                            );
                        }
                    }
                }
            }
        });
        return DtoResult.ok(convDeviceAndChannelDTO.getDeviceEntity().getStatus());
    }

    @Override
    public Result edit(VisualDeviceEntity entity) {
        VisualDeviceOrgEntity visualDeviceOrgEntity = visualDeviceOrgService.getById(entity.getId());
        Long deviceId = visualDeviceOrgEntity.getDeviceId();
        // 构建查询条件
        LambdaQueryWrapper<VisualDeviceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<VisualDeviceEntity>()
                .eq(VisualDeviceEntity::getDeviceCode, entity.getDeviceCode())
                .eq(VisualDeviceEntity::getAuthId, entity.getAuthId());
        // 如果通道编码不为空，则需要根据设备编码和通道编码判断唯一
        if (StringUtil.isNotEmpty(entity.getChannelCode())) {
            lambdaQueryWrapper.eq(VisualDeviceEntity::getChannelCode, entity.getChannelCode());
        }
        lambdaQueryWrapper.ne(VisualDeviceEntity::getId, deviceId);
        VisualDeviceEntity visualDevice = this.getAny(lambdaQueryWrapper);
        if (null != visualDevice) {
            return Result.error("设备已存在，请重新输入！");
        }
        entity.setId(deviceId);
        this.updateById(entity);
        return Result.ok();
    }

    @Override
    public DtoResult<Void> syncDeviceAndChannelName(SyncDeviceAndChannelNameDto dto) {
        Set<Long> deviceOrgIdSet = dto.getDeviceOrgIdSet();
        if (CollectionUtil.isEmpty(deviceOrgIdSet)) {
            return DtoResult.error("参数不能为空");
        }
        List<VisualDeviceOrgEntity> visualDeviceOrgEntityList = visualDeviceOrgService.listByIds(deviceOrgIdSet);
        if (CollectionUtil.isEmpty(visualDeviceOrgEntityList)) {
            return DtoResult.ok();
        }
        Set<Long> deviceIdSet = visualDeviceOrgEntityList.stream().map(VisualDeviceOrgEntity::getDeviceId).collect(Collectors.toSet());

        VisualDeviceAuthEntity visualDeviceAuthEntity = visualDeviceAuthService.getAny(new LambdaQueryWrapper<VisualDeviceAuthEntity>()
                .eq(VisualDeviceAuthEntity::getComponentName, SrvThirdPartyPlatformsBizComponent.vlinkerOpenBizComponentName)
                .eq(VisualDeviceAuthEntity::getPlatformType, SrvCameraPlatformTypeEnum.TYPE_1.getDicId())
        );
        if (null == visualDeviceAuthEntity) {
            return DtoResult.error("未找到视频平台配置");
        }
        List<VisualDeviceEntity> visualDeviceEntityList = visualDeviceService.list(new LambdaQueryWrapper<VisualDeviceEntity>()
                .eq(VisualDeviceEntity::getAuthId, visualDeviceAuthEntity.getId())
                .in(VisualDeviceEntity::getId, deviceIdSet)
        );
        DtoResult<List<DeviceInfoEntity>> dtoResult = iFeignOpenSystemApiController.syncDeviceAndChannelName(visualDeviceEntityList);
        List<DeviceInfoEntity> deviceInfoEntityList = dtoResult.getData();
        if (CollectionUtil.isEmpty(deviceInfoEntityList)) {
            return DtoResult.ok();
        }
        Map<String, List<DeviceInfoEntity>> groupedByTripartiteSn = deviceInfoEntityList.stream()
                .filter(deviceInfoEntity -> StringUtil.isNotEmpty(deviceInfoEntity.getTripartiteSn()))
                .collect(Collectors.groupingBy(DeviceInfoEntity::getTripartiteSn));

        groupedByTripartiteSn.forEach((key, value) -> {
            if (CollectionUtil.isNotEmpty(value)) {
                DeviceInfoEntity deviceInfoEntity = value.get(0);
                visualParentDeviceService.update(new LambdaUpdateWrapper<SrvVisualParentDeviceEntity>()
                        .set(SrvVisualParentDeviceEntity::getDeviceName, deviceInfoEntity.getDeviceName())
                        .eq(SrvVisualParentDeviceEntity::getDeviceCodeCode, deviceInfoEntity.getTripartiteSn())
                );
            }
        });
        List<List<DeviceInfoEntity>> partList = CustomerListUtil.subListByPart(deviceInfoEntityList, 10);
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            for (List<DeviceInfoEntity> subList : partList) {
                taskExecutor.execute(() -> {
                    for (DeviceInfoEntity deviceInfoEntity : subList) {
                        this.update(new LambdaUpdateWrapper<VisualDeviceEntity>()
                                .set(VisualDeviceEntity::getDeviceName, deviceInfoEntity.getDeviceName())
                                .set(VisualDeviceEntity::getChannelName, deviceInfoEntity.getChannelName())
                                .eq(VisualDeviceEntity::getDeviceCode, deviceInfoEntity.getId())
                        );
                    }
                });
            }
        });
        try {
            future.get();
        } catch (Exception e) {
            log.error("同步设备名称异常...msg={}", e.getMessage(), e);
        }
        return DtoResult.ok();
    }

    @Override
    public Result editByPark(VisualDeviceEntityByPark entity) {
        VisualDeviceOrgEntity visualDeviceOrgEntity = visualDeviceOrgService.getById(entity.getId());
        Long deviceId = visualDeviceOrgEntity.getDeviceId();
        // 构建查询条件
        LambdaQueryWrapper<VisualDeviceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<VisualDeviceEntity>()
                .eq(VisualDeviceEntity::getDeviceCode, entity.getDeviceCode())
                .eq(VisualDeviceEntity::getAuthId, entity.getAuthId());
        // 如果通道编码不为空，则需要根据设备编码和通道编码判断唯一
        if (StringUtil.isNotEmpty(entity.getChannelCode())) {
            lambdaQueryWrapper.eq(VisualDeviceEntity::getChannelCode, entity.getChannelCode());
        }
        lambdaQueryWrapper.ne(VisualDeviceEntity::getId, deviceId);
        VisualDeviceEntity visualDevice = this.getAny(lambdaQueryWrapper);
        if (null != visualDevice) {
            return Result.error("设备已存在，请重新输入！");
        }
        entity.setId(deviceId);
        this.updateById(entity);
        boolean flag = visualDeviceOrgService.update(new LambdaUpdateWrapper<VisualDeviceOrgEntity>()
                .set(VisualDeviceOrgEntity::getDeviceTreeId, entity.getOrgIdByPark())
                .set(VisualDeviceOrgEntity::getPosDevice, 0)
                .in(VisualDeviceOrgEntity::getId, visualDeviceOrgEntity.getId()));
        return Result.ok();
    }

    @Override
    public Result bindBroadcast(BindBroadcastBoxDto dto) {
        // 1.把之前绑定到该广播的监控解绑
        this.update(new LambdaUpdateWrapper<VisualDeviceEntity>()
                .set(VisualDeviceEntity::getBindBroadcastId, null)
                .eq(VisualDeviceEntity::getBindBroadcastId, dto.getDeviceId())
        );
        // 2.把新的监控设备绑定到该广播
        this.update(new LambdaUpdateWrapper<VisualDeviceEntity>()
                .set(VisualDeviceEntity::getBindBroadcastId, dto.getDeviceId())
                .eq(VisualDeviceEntity::getId, dto.getCameraId())
        );
        return Result.ok();
    }

    @Override
    public Result bindBox(BindBroadcastBoxDto dto) {
        // 1.删除原来绑定的盒子信息
        visualCameraBoxService.remove(new LambdaQueryWrapper<VisualCameraBoxEntity>().eq(VisualCameraBoxEntity::getCameraId, dto.getCameraId()));
        // 2.新增绑定的盒子信息
        VisualCameraBoxEntity visualCameraBoxEntity = new VisualCameraBoxEntity();
        visualCameraBoxEntity.setCameraId(dto.getCameraId());
        visualCameraBoxEntity.setBoxId(dto.getDeviceId());
        visualCameraBoxService.save(visualCameraBoxEntity);
        return Result.ok();
    }

    @Override
    public void genInfo(List<DeviceListPageVo> resultList) {
        if (CollectionUtil.isNotEmpty(resultList)) {
            // 设备型号
            DtoResult<List<ProductEntity>> iotProductList = feignIotProductSystemApiController.getIotProductListBySn(Collections.emptyList());
            if (iotProductList.success() && CollectionUtil.isNotEmpty(iotProductList.getData())) {
                Map<Long, ProductEntity> productEntityMap = iotProductList.getData().stream().collect(Collectors.toMap(ProductEntity::getId, item -> item));
                resultList.forEach(item -> {
                    if (ObjectUtil.isNotNull(item.getProductId()) && productEntityMap.containsKey(item.getProductId())) {
                        item.setProductModel(productEntityMap.get(item.getProductId()).getProductModel());
                    }
                });
            }
            Map<Object, Object> dicMap = new HashMap<>();
            Set<Long> orgIdSet = new HashSet<>();
            for (DeviceListPageVo deviceListPageVo : resultList) {
                orgIdSet.add(deviceListPageVo.getOrgId());
                orgIdSet.add(deviceListPageVo.getBelongOrgId());
            }
            List<OrgEntity> orgList = orgService.getOrgList(orgIdSet);
            dicMap.putAll(orgList.stream().collect(Collectors.toMap(OrgEntity::getId, OrgEntity::getName, (k1, k2) -> k1)));
            dicMap.putAll(attributeDetailService.getAllIdNameMap());
            dicMap.putAll(sceneInfoService.list().stream().collect(Collectors.toMap(SceneInfoEntity::getId, SceneInfoEntity::getName, (k1, k2) -> k1)));
            resultList.replaceAll(b -> AttrUtil.putAttr(b, dicMap));
        }
    }

    @Override
    public void genBoxName(List<DeviceListPageVo> resultList) {
        List<DeviceListPageVo> resultListTemp;
        Long videoTypeId = SrvCameraPlatformTypeEnum.TYPE_1.getDicId();
        // 如果是视频平台
        if (!CollectionUtils.isEmpty(resultList)) {
            DevicePageListByYyDto devicePageListByYyDto = new DevicePageListByYyDto();
            // 视频设备id集合
            Set<Long> videoDeviceId = resultList.stream()
                    .filter(t1 -> Objects.equals(t1.getDeviceType(), videoTypeId))
                    .map(DeviceListPageVo::getDeviceId).collect(Collectors.toSet());
            if (CollectionUtil.isEmpty(videoDeviceId)) {
                return;
            }
            devicePageListByYyDto.setInDeviceIdSet(videoDeviceId);
            // 查询盒子信息
            List<DeviceListPageVo> deviceListPageVoList = baseMapper.getBoxList(devicePageListByYyDto);
            if (!CollectionUtils.isEmpty(deviceListPageVoList)) {
                resultListTemp = resultList.stream().peek(t1 -> {
                    for (DeviceListPageVo deviceListPageVo : deviceListPageVoList) {
                        if (Objects.equals(t1.getDeviceId(), deviceListPageVo.getDeviceId())) {
                            t1.setBindBoxId(deviceListPageVo.getBindBoxId());
                            t1.setBindBoxName(deviceListPageVo.getBindBoxName());
                        }
                    }
                }).collect(Collectors.toList());
                resultList.clear();
                resultList.addAll(resultListTemp);
            }
        }
    }

    @Override
    public Result getCountGroupOnline(DevicePageListByYyDto dto) {
        Long orgId = dto.getOrgId();
        if (dto.getAppScene() == null) {
            return Result.error("应用场景不能为空");
        }
        if (dto.getDeviceType() == null) {
            // 监控设备
            Long deviceType = SrvCameraPlatformTypeEnum.TYPE_1.getDicId();
            dto.setDeviceType(deviceType);
        }
        // 1.判断当前登录用户类型
        UserTypeEnum userTypeEnum = userService.getUserType(JwtUtil.getUserId());
        if (UserTypeEnum.TO_C.equals(userTypeEnum)) {
            return Result.error("暂无权限！");
        }
        if (JwtUtil.isSupper() || UserTypeEnum.GUAN_LI.equals(userTypeEnum) || UserTypeEnum.ZHU_ZHANG_HU.equals(userTypeEnum)) {
            if (null == dto.getOrgId()) {
                return Result.error("请选择一个企业！");
            }
        }

        List<CountDto> countGroupOnline;
        // 如果是企业子账号
        if (UserTypeEnum.ZI_ZHANG_HU.equals(userTypeEnum)) {
            // 获取该用户的企用户ID
            dto.setUserId(JwtUtil.getUserId());
            countGroupOnline = visualDeviceOrgMapper.getCountGroupOnlineByUser(dto);
        } else {
            // if (JwtUtil.isSupper() || UserTypeEnum.GUAN_LI.equals(userTypeEnum)) {
            //     Set<Long> orgIdSet = new HashSet<>();
            //     orgIdSet.add(orgId);
            //     orgIdSet.addAll(orgService.getChildIds(orgId, true));
            //     dto.setOrgIdSet(orgIdSet);
            //     dto.setOrgId(null);
            // }
            // 如果是企业主账号
            // if (UserTypeEnum.ZHU_ZHANG_HU.equals(userTypeEnum)) {
            dto.setOrgId(orgId);
            dto.setOrgIdSet(new HashSet<>());
            // }
            countGroupOnline = visualDeviceOrgMapper.getCountGroupOnline(dto);
        }
        Map<Integer, CountDto> collect = countGroupOnline.stream().collect(Collectors.toMap(CountDto::getIsOnline, Function.identity(), (k1, k2) -> k1));
        List<CountDto> res = new ArrayList<>();
        res.add(collect.getOrDefault(1, CountDto.builder().isOnline(1).count(0).build()));
        res.add(collect.getOrDefault(0, CountDto.builder().isOnline(0).count(0).build()));
        return Result.ok(res);
    }

    /**
     * 同步设备
     */
    @Transactional(rollbackFor = Exception.class)
    public SyncOpenDeviceListVo syncDevice(List<DeviceInfoEntity> openDeviceList, VisualDeviceAuthEntity visualDeviceAuthEntity, Long userId) {
        Long currentAuthId = visualDeviceAuthEntity.getId();
        Long platformType = visualDeviceAuthEntity.getPlatformType();

        boolean videoPlatform = Objects.equals(platformType, SrvCameraPlatformTypeEnum.TYPE_1.getDicId());

        SyncOpenDeviceListVo syncOpenDeviceListVo = new SyncOpenDeviceListVo();

        List<VisualDeviceEntity> addDeviceEntityList = new ArrayList<>();
        List<VisualDeviceEntity> updateDeviceEntityList = new ArrayList<>();

        List<SrvVisualParentDeviceEntity> allSrvVisualParentDeviceEntityList = visualParentDeviceService.list();
        List<SrvVisualParentDeviceEntity> currentAuthSrvVisualParentDeviceEntityList = allSrvVisualParentDeviceEntityList.stream()
                .filter(t1 -> Objects.equals(t1.getAuthId(), currentAuthId)).collect(Collectors.toList());

        // 查询根设备树
        OrgEntity rootOrgEntity = null;
        // 1. 处理设备
        Map<String, List<DeviceInfoEntity>> groupedDevices = openDeviceList.stream()
                .collect(Collectors.groupingBy(DeviceInfoEntity::getDeviceId));
        List<SrvVisualParentDeviceEntity> batchSaveSrvVisualParentDeviceEntityList = new ArrayList<>();
        List<SrvVisualParentDeviceEntity> batchEditSrvVisualParentDeviceEntityList = new ArrayList<>();

        LocalDateTime localDateTime = LocalDateTime.now();
        // 1.1 设备新增
        for (Map.Entry<String, List<DeviceInfoEntity>> entry : groupedDevices.entrySet()) {
            String deviceId = entry.getKey();
            List<DeviceInfoEntity> deviceInfoVOList = entry.getValue();
            if (StringUtil.isEmpty(deviceId) || CollectionUtil.isEmpty(deviceInfoVOList)) {
                continue;
            }
            if (currentAuthSrvVisualParentDeviceEntityList.stream().noneMatch(t1 -> Objects.equals(t1.getDeviceCode(), deviceId))) {
                if (rootOrgEntity == null) {
                    rootOrgEntity = orgService.getOne(new LambdaQueryWrapper<OrgEntity>()
                            .eq(OrgEntity::getParentId, 0));
                }
                DeviceInfoEntity deviceInfoEntity = deviceInfoVOList.get(0);
                SrvVisualParentDeviceEntity saveSrvVisualParentDeviceEntity = new SrvVisualParentDeviceEntity();

                saveSrvVisualParentDeviceEntity.setDeviceType(SrvCameraPlatformTypeEnum.getSrvDeviceTypeByOpenAppType(deviceInfoEntity.getType()));

                saveSrvVisualParentDeviceEntity.setDeviceCode(deviceId);
                saveSrvVisualParentDeviceEntity.setDeviceCodeCode(deviceInfoEntity.getTripartiteSn());
                saveSrvVisualParentDeviceEntity.setDeviceName(deviceInfoEntity.getDeviceName());
                saveSrvVisualParentDeviceEntity.setAccessWay(deviceInfoEntity.getAccessWay());
                saveSrvVisualParentDeviceEntity.setAuthId(currentAuthId);
                saveSrvVisualParentDeviceEntity.setBelongOrgId(rootOrgEntity.getId());

                int online = 0;
                if (deviceInfoVOList.stream().anyMatch(t1 -> Objects.equals(1, t1.getOnlineStatus()))) {
                    online = 1;
                }
                saveSrvVisualParentDeviceEntity.setIsOnline(online);
                saveSrvVisualParentDeviceEntity.setCreateBy(userId);
                saveSrvVisualParentDeviceEntity.setCreateTime(localDateTime);
                batchSaveSrvVisualParentDeviceEntityList.add(saveSrvVisualParentDeviceEntity);
            }
        }

        // 1.2 设备更新
        for (SrvVisualParentDeviceEntity srvVisualParentDeviceEntity : currentAuthSrvVisualParentDeviceEntityList) {
            Set<String> keySet = groupedDevices.keySet();
            for (String key : keySet) {
                if (Objects.equals(key, srvVisualParentDeviceEntity.getDeviceCode())) {
                    SrvVisualParentDeviceEntity editSrvVisualParentDeviceEntity = new SrvVisualParentDeviceEntity();
                    editSrvVisualParentDeviceEntity.setId(srvVisualParentDeviceEntity.getId());

                    List<DeviceInfoEntity> deviceInfoVOList = groupedDevices.get(key);
                    if (CollectionUtil.isNotEmpty(deviceInfoVOList)) {
                        DeviceInfoEntity deviceInfoEntity = deviceInfoVOList.get(0);
                        editSrvVisualParentDeviceEntity.setAccessWay(deviceInfoEntity.getAccessWay());
                        int online = 0;
                        if (deviceInfoVOList.stream().anyMatch(t1 -> Objects.equals(1, t1.getOnlineStatus()))) {
                            online = 1;
                        }
                        editSrvVisualParentDeviceEntity.setIsOnline(online);

                        if (StringUtil.isEmpty(srvVisualParentDeviceEntity.getDeviceCodeCode())) {
                            editSrvVisualParentDeviceEntity.setDeviceCodeCode(deviceInfoEntity.getTripartiteSn());
                        }
                        if (StringUtil.isEmpty(srvVisualParentDeviceEntity.getDeviceName())) {
                            editSrvVisualParentDeviceEntity.setDeviceName(deviceInfoEntity.getDeviceName());
                        }

                        editSrvVisualParentDeviceEntity.setDeviceType(SrvCameraPlatformTypeEnum.getSrvDeviceTypeByOpenAppType(deviceInfoEntity.getType()));
                    }
                    editSrvVisualParentDeviceEntity.setUpdateBy(userId);
                    editSrvVisualParentDeviceEntity.setUpdateTime(localDateTime);
                    batchEditSrvVisualParentDeviceEntityList.add(editSrvVisualParentDeviceEntity);
                }
            }
        }

        // TODO 1.3 无用的设备可能需要删除，目前手动删除处理

        if (CollectionUtil.isNotEmpty(batchSaveSrvVisualParentDeviceEntityList)) {
            visualParentDeviceService.saveBatch(batchSaveSrvVisualParentDeviceEntityList);
        }
        if (CollectionUtil.isNotEmpty(batchEditSrvVisualParentDeviceEntityList)) {
            visualParentDeviceService.updateBatchById(batchEditSrvVisualParentDeviceEntityList);
        }

        // 2. 处理通道
        // 获取所有通道
        List<VisualDeviceEntity> localDeviceList = this.list(new LambdaQueryWrapper<VisualDeviceEntity>()
                .eq(VisualDeviceEntity::getAuthId, currentAuthId));

        List<DeviceInfoEntity> insertList = openDeviceList.stream()
                .filter(deviceInfoEntity -> {
                    if (videoPlatform) {
                        return StringUtil.isNotEmpty(deviceInfoEntity.getChannelId());
                    } else {
                        return true;
                    }
                })
                .filter(deviceInfoEntity -> localDeviceList.stream()
                        .noneMatch(visualDeviceEntity -> deviceInfoEntity.getId().equals(visualDeviceEntity.getDeviceCode())))
                .collect(Collectors.toList());
        // 找出新增的通道
        // List<DeviceInfoEntity> insertList = openDeviceList.stream()
        //         // 过滤出openDeviceList中没有在localDeviceList中的通道
        //         .filter(deviceInfoEntity -> localDeviceList.stream()
        //                 .noneMatch(localDeviceEntity -> {
        //                     boolean matchId = deviceInfoEntity.getId().equals(localDeviceEntity.getDeviceCode());
        //                     if (videoPlatform) {
        //                         matchId = matchId && StringUtil.isNotEmpty(deviceInfoEntity.getChannelId());
        //                     }
        //                     return matchId;
        //                 }))
        //         .collect(Collectors.toList());
        //
        List<VisualDeviceEntity> updateList = new ArrayList<>();

        // 更新的通道
        for (VisualDeviceEntity localDevice : localDeviceList) {
            for (DeviceInfoEntity deviceInfo : openDeviceList) {
                if (videoPlatform && StringUtil.isEmpty(deviceInfo.getChannelId())) {
                    continue;
                }
                if (Objects.equals(localDevice.getDeviceCode(), deviceInfo.getId())) {
                    VisualDeviceEntity updateVisualDeviceEntity = new VisualDeviceEntity();
                    updateVisualDeviceEntity.setId(localDevice.getId());

                    updateVisualDeviceEntity.setDeviceType(SrvCameraPlatformTypeEnum.getSrvDeviceTypeByOpenAppType(deviceInfo.getType()));

                    updateVisualDeviceEntity.setShowDeviceName(videoPlatform ? deviceInfo.getChannelName() : deviceInfo.getDeviceName());

                    updateVisualDeviceEntity.setProductId(deviceInfo.getProductId());
                    updateVisualDeviceEntity.setIsOnline(deviceInfo.getOnlineStatus());
                    updateVisualDeviceEntity.setRegular(deviceInfo.getRegular());
                    updateVisualDeviceEntity.setAccessWay(deviceInfo.getAccessWay());
                    updateVisualDeviceEntity.setDeviceSn(deviceInfo.getDeviceSn());
                    if (StringUtil.isEmpty(localDevice.getParentCode())) {
                        updateVisualDeviceEntity.setParentCode(deviceInfo.getDeviceId());
                    }
                    if (StringUtil.isEmpty(localDevice.getDeviceCodeCode())) {
                        updateVisualDeviceEntity.setDeviceCodeCode(deviceInfo.getTripartiteSn());
                    }
                    if (StringUtil.isEmpty(localDevice.getChannelCode())) {
                        updateVisualDeviceEntity.setChannelCode(deviceInfo.getChannelId());
                    }
                    if (StringUtil.isEmpty(localDevice.getDeviceName())) {
                        updateVisualDeviceEntity.setDeviceName(deviceInfo.getDeviceName());
                        updateVisualDeviceEntity.setDeviceNameName(deviceInfo.getDeviceName());
                    }
                    if (StringUtil.isEmpty(localDevice.getChannelName())) {
                        updateVisualDeviceEntity.setChannelName(deviceInfo.getChannelName());
                    }
                    if (StringUtil.isEmpty(localDevice.getDeviceNameName())) {
                        updateVisualDeviceEntity.setDeviceNameName(deviceInfo.getDeviceName());
                    }
                    if (StringUtil.isEmpty(localDevice.getDeviceSn())) {
                        updateVisualDeviceEntity.setDeviceSn(deviceInfo.getDeviceSn());
                    }
                    updateVisualDeviceEntity.setVolume(deviceInfo.getVolume());

                    if (videoPlatform) {
                        if (deviceInfo.getDeviceCapacity().containsKey(DeviceCapacityEnum.PAN_TILT_CONTROL.getField())) {
                            updateVisualDeviceEntity.setHasPtz(deviceInfo.getDeviceCapacity()
                                    .get(DeviceCapacityEnum.PAN_TILT_CONTROL.getField()).getValue());
                        }
                        if (deviceInfo.getDeviceCapacity().containsKey(DeviceCapacityEnum.INTERCOM.getField())) {
                            updateVisualDeviceEntity.setHasTalk(deviceInfo.getDeviceCapacity()
                                    .get(DeviceCapacityEnum.INTERCOM.getField()).getValue());
                        }
                        if (deviceInfo.getDeviceCapacity().containsKey(DeviceCapacityEnum.PRESET.getField())) {
                            updateVisualDeviceEntity.setHasPreset(deviceInfo.getDeviceCapacity()
                                    .get(DeviceCapacityEnum.PRESET.getField()).getValue());
                        }
                        updateVisualDeviceEntity.setDeviceCapacity(deviceInfo.getDeviceCapacity());
                    } else {
                        updateVisualDeviceEntity.setDeviceCapacity(null);
                    }
                    updateVisualDeviceEntity.setUpdateBy(userId);
                    updateVisualDeviceEntity.setUpdateTime(localDateTime);
                    updateList.add(updateVisualDeviceEntity);
                }
            }
        }

        // 新增的通道
        if (CollectionUtil.isNotEmpty(insertList)) {
            List<VisualDeviceTreeEntity> visualDeviceTreeEntityList = visualDeviceTreeService.list(new LambdaQueryWrapper<VisualDeviceTreeEntity>()
                    .eq(VisualDeviceTreeEntity::getParentId, 0));
            if (rootOrgEntity == null) {
                rootOrgEntity = orgService.getOne(new LambdaQueryWrapper<OrgEntity>()
                        .eq(OrgEntity::getParentId, 0));
            }
            Long deviceTreeId = null;
            // 数据转换入库
            for (DeviceInfoEntity deviceInfoEntity : insertList) {
                VisualDeviceEntity visualDeviceEntity = new VisualDeviceEntity();

                if (Objects.equals(deviceInfoEntity.getType(), OpenThirdPartyPlatformsTypeEnum.IOT.getDicId())) {
                    visualDeviceEntity.setDeviceType(SrvCameraPlatformTypeEnum.TYPE_4.getDicId());
                } else if (Objects.equals(deviceInfoEntity.getType(), OpenThirdPartyPlatformsTypeEnum.BROADCAST.getDicId())) {
                    visualDeviceEntity.setDeviceType(SrvCameraPlatformTypeEnum.TYPE_2.getDicId());
                } else {
                    visualDeviceEntity.setDeviceType(SrvCameraPlatformTypeEnum.TYPE_1.getDicId());
                }
                visualDeviceEntity.setParentCode(deviceInfoEntity.getDeviceId());
                visualDeviceEntity.setDeviceCode(deviceInfoEntity.getId());
                visualDeviceEntity.setDeviceCodeCode(deviceInfoEntity.getTripartiteSn());
                visualDeviceEntity.setDeviceName(deviceInfoEntity.getDeviceName());
                visualDeviceEntity.setDeviceNameName(deviceInfoEntity.getDeviceName());
                visualDeviceEntity.setChannelCode(deviceInfoEntity.getChannelId());
                visualDeviceEntity.setChannelName(deviceInfoEntity.getChannelName());
                visualDeviceEntity.setIsOnline(deviceInfoEntity.getOnlineStatus());
                visualDeviceEntity.setAuthId(currentAuthId);
                visualDeviceEntity.setBelongOrgId(rootOrgEntity.getId());
                visualDeviceEntity.setProductId(deviceInfoEntity.getProductId());
                visualDeviceEntity.setRegular(deviceInfoEntity.getRegular());
                visualDeviceEntity.setAccessWay(deviceInfoEntity.getAccessWay());
                visualDeviceEntity.setDeviceSn(deviceInfoEntity.getDeviceSn());
                visualDeviceEntity.setVolume(deviceInfoEntity.getVolume());

                if (deviceInfoEntity.getDeviceCapacity() != null) {
                    if (deviceInfoEntity.getDeviceCapacity().containsKey(DeviceCapacityEnum.PAN_TILT_CONTROL.getField())) {
                        visualDeviceEntity.setHasPtz(deviceInfoEntity.getDeviceCapacity()
                                .get(DeviceCapacityEnum.PAN_TILT_CONTROL.getField()).getValue());
                    }
                    if (deviceInfoEntity.getDeviceCapacity().containsKey(DeviceCapacityEnum.INTERCOM.getField())) {
                        visualDeviceEntity.setHasTalk(deviceInfoEntity.getDeviceCapacity()
                                .get(DeviceCapacityEnum.INTERCOM.getField()).getValue());
                    }
                    if (deviceInfoEntity.getDeviceCapacity().containsKey(DeviceCapacityEnum.PRESET.getField())) {
                        visualDeviceEntity.setHasPreset(deviceInfoEntity.getDeviceCapacity()
                                .get(DeviceCapacityEnum.PRESET.getField()).getValue());
                    }
                    visualDeviceEntity.setDeviceCapacity(deviceInfoEntity.getDeviceCapacity());
                }
                visualDeviceEntity.setGateType(deviceInfoEntity.getGateType());

                Optional<SrvVisualParentDeviceEntity> optional = allSrvVisualParentDeviceEntityList.stream().filter(t1 -> Objects.equals(t1.getDeviceCode(), deviceInfoEntity.getDeviceId())).findFirst();
                if (optional.isPresent()) {
                    SrvVisualParentDeviceEntity srvVisualParentDeviceEntity = optional.get();
                    Long currentOrgId = srvVisualParentDeviceEntity.getBelongOrgId();
                    visualDeviceEntity.setBelongOrgId(currentOrgId);

                    Optional<VisualDeviceTreeEntity> optionalDeviceTree = visualDeviceTreeEntityList.stream().filter(t1 -> Objects.equals(t1.getOrgId(), currentOrgId)).findFirst();
                    if (optionalDeviceTree.isPresent()) {
                        deviceTreeId = optionalDeviceTree.get().getId();
                    }

                    List<Long> enterpriseOrgSceneIds = orgRenewService.getEnterpriseOrgSceneIds(currentOrgId);
                    if (CollectionUtil.isNotEmpty(enterpriseOrgSceneIds) && enterpriseOrgSceneIds.size() == 1) {
                        visualDeviceEntity.setAppScene(enterpriseOrgSceneIds.get(0));
                        visualDeviceEntity.setRemark(String.valueOf(enterpriseOrgSceneIds.get(0)));
                    }
                }
                visualDeviceEntity.setCreateBy(userId);
                visualDeviceEntity.setCreateTime(localDateTime);
                addDeviceEntityList.add(visualDeviceEntity);
            }
            if (CollectionUtil.isNotEmpty(addDeviceEntityList)) {
                this.saveBatch(addDeviceEntityList);
                // 保存设备和组织的绑定关系
                List<VisualDeviceOrgEntity> deviceOrgEntityList = new ArrayList<>();
                List<VisualDeviceOrgEntity> editAppSceneDeviceOrgEntityList = new ArrayList<>();
                for (VisualDeviceEntity visualDeviceEntity : addDeviceEntityList) {
                    VisualDeviceOrgEntity visualDeviceOrgEntity = new VisualDeviceOrgEntity();
                    Long id = IdUtil.getSnowflakeNextId();
                    visualDeviceOrgEntity.setId(id);
                    visualDeviceOrgEntity.setDeviceId(visualDeviceEntity.getId());
                    visualDeviceOrgEntity.setOrgId(visualDeviceEntity.getBelongOrgId());
                    // visualDeviceOrgEntity.setAppScene(visualDeviceEntity.getAppScene());
                    visualDeviceOrgEntity.setIsOwner(1);
                    if (null != deviceTreeId) {
                        visualDeviceOrgEntity.setDeviceTreeId(deviceTreeId);
                    }
                    visualDeviceOrgEntity.setCreateBy(userId);
                    visualDeviceOrgEntity.setCreateTime(localDateTime);
                    visualDeviceOrgEntity.setShowDeviceName(videoPlatform ? visualDeviceEntity.getChannelName() : visualDeviceEntity.getDeviceName());
                    deviceOrgEntityList.add(visualDeviceOrgEntity);

                    if (null != visualDeviceEntity.getAppScene()) {
                        VisualDeviceOrgEntity editAppSceneVisualDeviceOrgEntity = new VisualDeviceOrgEntity();
                        editAppSceneVisualDeviceOrgEntity.setId(id);
                        editAppSceneVisualDeviceOrgEntity.setAppScene(visualDeviceEntity.getAppScene());
                        editAppSceneDeviceOrgEntityList.add(editAppSceneVisualDeviceOrgEntity);
                    }
                }
                visualDeviceOrgService.saveBatch(deviceOrgEntityList);

                if (CollectionUtil.isNotEmpty(editAppSceneDeviceOrgEntityList)) {
                    Map<Long, List<VisualDeviceOrgEntity>> map = editAppSceneDeviceOrgEntityList.stream()
                            .collect(Collectors.groupingBy(VisualDeviceOrgEntity::getAppScene));
                    map.forEach((key, value) -> {
                        Set<Long> idSet = value.stream().map(VisualDeviceOrgEntity::getId).collect(Collectors.toSet());
                        EditAppSceneBatchDto editAppSceneBatchDto = new EditAppSceneBatchDto();
                        editAppSceneBatchDto.setAppScene(key);
                        editAppSceneBatchDto.setIds(idSet.stream().map(Object::toString).collect(Collectors.joining(",")));
                        editAppSceneBatchDto.setType(2);
                        visualDeviceOrgService.editAppSceneBatch(editAppSceneBatchDto);

                        // visualDeviceOrgService.update(new LambdaUpdateWrapper<VisualDeviceOrgEntity>()
                        //         .set(VisualDeviceOrgEntity::getAppScene,key)
                        //         .in(VisualDeviceOrgEntity::getId,idSet)
                        // );
                    });
                }
            }
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            List<List<VisualDeviceEntity>> partList = CustomerListUtil.subListByPart(updateList, 10);
            for (List<VisualDeviceEntity> subList : partList) {
                taskExecutor.execute(() -> {
                    Set<Long> deviceIdSet = subList.stream().map(VisualDeviceEntity::getId).collect(Collectors.toSet());
                    if (CollectionUtil.isNotEmpty(deviceIdSet)) {
                        List<VisualDeviceOrgEntity> visualDeviceOrgEntityList = visualDeviceOrgService.list(new LambdaQueryWrapper<VisualDeviceOrgEntity>()
                                .in(VisualDeviceOrgEntity::getDeviceId, deviceIdSet)
                                .isNull(VisualDeviceOrgEntity::getShowDeviceName)
                        );
                        if (CollectionUtil.isNotEmpty(visualDeviceOrgEntityList)) {
                            for (VisualDeviceOrgEntity visualDeviceOrgEntity : visualDeviceOrgEntityList) {
                                Optional<VisualDeviceEntity> optional = subList.stream().filter(visualDeviceEntity -> visualDeviceEntity.getId().equals(visualDeviceOrgEntity.getDeviceId())).findFirst();
                                if (!optional.isPresent()) {
                                    continue;
                                }
                                LambdaUpdateWrapper<VisualDeviceOrgEntity> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
                                lambdaUpdateWrapper.eq(VisualDeviceOrgEntity::getId, visualDeviceOrgEntity.getId());
                                lambdaUpdateWrapper.set(VisualDeviceOrgEntity::getShowDeviceName, optional.get().getShowDeviceName());
                                lambdaUpdateWrapper.set(VisualDeviceOrgEntity::getUpdateBy, userId);
                                lambdaUpdateWrapper.set(VisualDeviceOrgEntity::getUpdateTime, localDateTime);
                                visualDeviceOrgService.update(lambdaUpdateWrapper);
                            }
                        }
                    }
                    this.updateBatchById(subList);
                });
            }
        }
        syncOpenDeviceListVo.setAddDeviceList(addDeviceEntityList);
        syncOpenDeviceListVo.setUpdateDeviceList(updateDeviceEntityList);
        return syncOpenDeviceListVo;
    }

    @Override
    public List<DeviceInfoListResultDto> getDeviceInfoList(DeviceInfoListParamDto dto) {
        return baseMapper.getDeviceInfoList(dto);
    }

    /**
     * 开放平台的设备状态变更
     */
    @Override
    public void updateDeviceOnline(OpenNoticeOnLineMsgDTO openNoticeOnLineMsgDTO) {
        DateTime dateTime = DateTime.now();
        String nowStr1 = DateTime.of(dateTime).toString("yyyy年MM月dd日 HH:mm:ss");
        String nowStr2 = DateTime.of(dateTime).toString("HH:mm");
        openNoticeOnLineMsgDTO.getDeviceIdSet().forEach(deviceId -> {
            List<VisualDeviceEntity> list = this.list(new LambdaQueryWrapper<VisualDeviceEntity>()
                    .eq(VisualDeviceEntity::getDeviceCode, deviceId));
            list.forEach(visualDeviceEntity -> {
                LocalDateTime localDateTime = LocalDateTime.now();
                Set<Long> userIdSet;
                List<GroupAndFamilyNameByDeviceCodeCodeResultDTO> groupAndFamilyNameByDeviceCodeCodeResultDTOList;
                HashMap<Long, Boolean> userMsgPermissionByUserId;

                if (!Objects.equals(visualDeviceEntity.getIsOnline(), openNoticeOnLineMsgDTO.getOnline())) {
                    LambdaUpdateWrapper<VisualDeviceEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<VisualDeviceEntity>()
                            .set(VisualDeviceEntity::getIsOnline, openNoticeOnLineMsgDTO.getOnline())
                            .set(Objects.equals(0, openNoticeOnLineMsgDTO.getOnline()), VisualDeviceEntity::getOfflineDateTime, localDateTime)
                            .eq(VisualDeviceEntity::getId, visualDeviceEntity.getId());
                    visualParentDeviceService.update(new LambdaUpdateWrapper<SrvVisualParentDeviceEntity>().set(SrvVisualParentDeviceEntity::getIsOnline, openNoticeOnLineMsgDTO.getOnline()).eq(SrvVisualParentDeviceEntity::getDeviceCode, visualDeviceEntity.getDeviceCode()));
                    boolean update = this.update(lambdaUpdateWrapper);
                    if (update) {
                        // 更新设备上下线记录表
                        visualDeviceOnlineLogService.save(VisualDeviceOnlineLogEntity.builder()
                                .deviceId(visualDeviceEntity.getId())
                                .isOnline(openNoticeOnLineMsgDTO.getOnline())
                                .build());
                        List<SrvAppUserGroupDeviceEntity> srvAppUserGroupDeviceEntityList = appUserGroupDeviceService.getUserGroupDeviceListByDeviceSn(visualDeviceEntity.getDeviceCodeCode());
                        if (CollectionUtil.isEmpty(srvAppUserGroupDeviceEntityList)) {
                            return;
                        }
                        userIdSet = srvAppUserGroupDeviceEntityList.stream()
                                .map(SrvAppUserGroupDeviceEntity::getUserId)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toSet());
                        // 查询用户消息开关权限
                        UserPermissionDto permissionDto = UserPermissionDto.builder().deviceSn(visualDeviceEntity.getDeviceCodeCode()).userIds(userIdSet).build();
                        userMsgPermissionByUserId = userMsgPermissionService.queryUserMsgPermissionByDeviceSnAndUserIds(permissionDto, Objects.equals(openNoticeOnLineMsgDTO.getOnline(), StatusEnum.ZERO.getCode()) ? 2 : 1);
                        log.info("设备上下线-消息发送用户权限查询结果，UserPermissionDto:{}，permissionResult:{}", JSONObject.toJSONString(permissionDto), JSONObject.toJSONString(userMsgPermissionByUserId));
                        // 如果用户没有开启消息开关则过滤掉
                        AtomicReference<HashMap<Long, Boolean>> permissionRef = new AtomicReference<>(userMsgPermissionByUserId);
                        HashMap<Long, Boolean> permissionTemp = permissionRef.get();
                        userIdSet = userIdSet.stream()
                                .filter(userId -> permissionTemp.containsKey(userId) && permissionTemp.get(userId))
                                .collect(Collectors.toSet());
                        log.info("设备上下线-消息发送用户权限查询，用户过滤后结果...userIdSet={}", JSON.toJSON(userIdSet));


                        GroupAndFamilyNameByDeviceCodeCodeParamDTO groupAndFamilyNameByDeviceCodeCodeParamDTO = new GroupAndFamilyNameByDeviceCodeCodeParamDTO();
                        groupAndFamilyNameByDeviceCodeCodeParamDTO.setDeviceCodeCode(visualDeviceEntity.getDeviceCodeCode());
                        groupAndFamilyNameByDeviceCodeCodeParamDTO.setUserIdSet(userIdSet);
                        groupAndFamilyNameByDeviceCodeCodeResultDTOList = appUserGroupDeviceService.getGroupAndFamilyNameByDeviceCodeCode(groupAndFamilyNameByDeviceCodeCodeParamDTO);

                        // deviceCodeCode查询父表
                        SrvVisualParentDeviceEntity parentDeviceEntity = visualParentDeviceService.getAny(new LambdaQueryWrapper<SrvVisualParentDeviceEntity>()
                                .eq(SrvVisualParentDeviceEntity::getDeviceCodeCode, visualDeviceEntity.getDeviceCodeCode())
                        );
                        String content;
                        // 循环 userIds
                        for (GroupAndFamilyNameByDeviceCodeCodeResultDTO groupAndFamilyNameByDeviceCodeCodeResultDTO : groupAndFamilyNameByDeviceCodeCodeResultDTOList) {
                            Long userId = groupAndFamilyNameByDeviceCodeCodeResultDTO.getUserId();
                            if (!userMsgPermissionByUserId.containsKey(userId) || !userMsgPermissionByUserId.get(userId)) {
                                continue;
                            }
                            VisualMsgEntity visualMsgEntity = new VisualMsgEntity();
                            visualMsgEntity.setDeviceId(visualDeviceEntity.getId());
                            visualMsgEntity.setParentDeviceId(Objects.isNull(parentDeviceEntity) ? null : parentDeviceEntity.getId());
                            visualMsgEntity.setCreateTime(localDateTime);
                            if (Objects.equals(openNoticeOnLineMsgDTO.getOnline(), StatusEnum.ZERO.getCode())) {
                                // 新增消息记录
                                content = "尊敬的用户，您的设备\"{{deviceName}}\"于" + nowStr1 + "下线，请注意检查设备电源和网络状态！";
                                visualMsgEntity.setCategory(ToCMsgTypeEnum.MSG_TYPE_4.getParentCode());
                                visualMsgEntity.setType(ToCMsgTypeEnum.MSG_TYPE_4.getCode());
                                visualMsgEntity.setUserId(userId);
                            } else {
                                // 新增消息记录
                                content = "尊敬的用户，您的设备\"{{deviceName}}\"于" + nowStr1 + "上线！";
                                visualMsgEntity.setCategory(ToCMsgTypeEnum.MSG_TYPE_3.getParentCode());
                                visualMsgEntity.setType(ToCMsgTypeEnum.MSG_TYPE_3.getCode());
                                visualMsgEntity.setUserId(userId);
                            }
                            visualMsgEntity.setContent(content);
                            visualMsgService.save(visualMsgEntity);
                        }
                        AtomicReference<List<GroupAndFamilyNameByDeviceCodeCodeResultDTO>> groupAndFamilyNameByDeviceCodeCodeResultDTOListTemp = new AtomicReference<>(groupAndFamilyNameByDeviceCodeCodeResultDTOList);

                        taskExecutor.execute(() -> {
                            try {
                                this.pushDeviceStatusToApp(visualDeviceEntity, nowStr2, openNoticeOnLineMsgDTO.getOnline(), groupAndFamilyNameByDeviceCodeCodeResultDTOListTemp.get());
                            } catch (Exception e) {
                                log.error("推送设备状态到app异常...msg={}", e.getMessage(), e);
                            }
                        });
                    }
                }
            });
        });
    }

    public void pushDeviceStatusToApp(VisualDeviceEntity visualDeviceEntity, String nowStr, Integer isOnline, List<GroupAndFamilyNameByDeviceCodeCodeResultDTO> groupAndFamilyNameByDeviceCodeCodeResultDTOListTemp) {
        String deviceCodeCode = visualDeviceEntity.getDeviceCodeCode();
        if (CollectionUtil.isEmpty(groupAndFamilyNameByDeviceCodeCodeResultDTOListTemp)) {
            return;
        }
        for (GroupAndFamilyNameByDeviceCodeCodeResultDTO groupAndFamilyNameByDeviceCodeCodeResultDTO : groupAndFamilyNameByDeviceCodeCodeResultDTOListTemp) {
            Long userId = groupAndFamilyNameByDeviceCodeCodeResultDTO.getUserId();
            UserEntity userEntity = userService.getById(userId);
            if (Objects.isNull(userEntity) || Objects.equals(0, userEntity.getAppStatus())) {
                log.info("设备状态推送.用户不存在或未激活..程序continue.userId={}", userId);
                continue;
            }

            String deviceName = visualDeviceEntity.getDeviceName();

            try {
                JiGuangPushBean jiGuangPushBean = new JiGuangPushBean();
                JiGuangPushBean.ExtrasBean extrasBean = new JiGuangPushBean.ExtrasBean();
                extrasBean.setDeviceId(visualDeviceEntity.getId());
                extrasBean.setDeviceCodeCode(deviceCodeCode);
                extrasBean.setDeviceName(deviceName);
                extrasBean.setChannelCode(visualDeviceEntity.getChannelCode());
                extrasBean.setChannelName(visualDeviceEntity.getChannelName());
                extrasBean.setPartName(groupAndFamilyNameByDeviceCodeCodeResultDTO.getGroupName());
                extrasBean.setFamilyName(groupAndFamilyNameByDeviceCodeCodeResultDTO.getFamilyName());
                extrasBean.setCategory(Objects.equals(isOnline, StatusEnum.ONE.getCode()) ? ToCMsgTypeEnum.MSG_TYPE_3.getParentCode() : ToCMsgTypeEnum.MSG_TYPE_4.getParentCode());
                extrasBean.setType(Objects.equals(isOnline, StatusEnum.ONE.getCode()) ? ToCMsgTypeEnum.MSG_TYPE_3.getCode() : ToCMsgTypeEnum.MSG_TYPE_4.getCode());
                jiGuangPushBean.setExtrasBean(extrasBean);

                if (Objects.equals(isOnline, StatusEnum.ONE.getCode())) {
                    String appTitle = String.format("%s %s", nowStr, ToCMsgTypeEnum.MSG_TYPE_3.getMsg());

                    jiGuangPushBean.setTitle(appTitle);
                    jiGuangPushBean.setContent(String.format("%s-%s-%s", deviceName, groupAndFamilyNameByDeviceCodeCodeResultDTO.getGroupName(), groupAndFamilyNameByDeviceCodeCodeResultDTO.getFamilyName()));
                } else {
                    String appTitle = String.format("%s %s", nowStr, ToCMsgTypeEnum.MSG_TYPE_4.getMsg());

                    jiGuangPushBean.setTitle(appTitle);
                    jiGuangPushBean.setContent(String.format("%s-%s-%s", deviceName, groupAndFamilyNameByDeviceCodeCodeResultDTO.getGroupName(), groupAndFamilyNameByDeviceCodeCodeResultDTO.getFamilyName()));
                }
                boolean flag = jiGuangPushService.sendPushByAlias(jiGuangPushBean, String.valueOf(userId));
                log.info("极光推送设备状态到app...deviceCodeCode={}, userIds={}, isOnline={}, flag={}", deviceCodeCode, userId, isOnline, flag);
            } catch (Exception e) {
                log.error("推送设备状态到app异常...msg={}", e.getMessage(), e);
            }
        }
    }

    @Override
    @Transactional
    public Result editVolume(VisualDeviceEntity entity) {
        // 1.查询设备信息
        VisualDeviceEntity visualCameraEntity = visualDeviceService.getById(entity.getId());
        if (null == visualCameraEntity) {
            return Result.error("找不到该设备，请重试！");
        }
        if (null == visualCameraEntity.getDeviceSn()) {
            return Result.error("该设备未绑定sn，请绑定！");
        }
        // 找到该设备的授权信息
        VisualDeviceAuthEntity visualDeviceAuthEntity = visualDeviceAuthService.getById(visualCameraEntity.getAuthId());
        if (null == visualDeviceAuthEntity) {
            return Result.error("找不到对应的授权信息，请重试！");
        }
        // 获取业务处理类
        String componentName = visualDeviceAuthEntity.getComponentName();
        if (StringUtil.isEmpty(componentName)) {
            return Result.error("授权对应业务类不存在，请联系管理员！");
        }
        BroadcastBiz broadcastBiz = (BroadcastBiz) SpringUtil.getBean(componentName);
        LiveByWebParam param = new LiveByWebParam();
        param.setDeviceId(visualCameraEntity.getDeviceCode());
        param.setDeviceSn(visualCameraEntity.getDeviceSn());
        param.setVolume(entity.getVolume());
        DtoResult<DeviceUpdateResponse> fResult = broadcastBiz.editBroadcastBiz(param, visualDeviceAuthEntity);
        if (!fResult.success()) {
            return Result.error(fResult.getMessage());
        }
        super.update().set("volume", entity.getVolume()).eq("id", entity.getId()).update();
        return Result.ok();
    }
}